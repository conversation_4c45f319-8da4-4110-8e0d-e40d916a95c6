//TJADB Project
TITLE:<PERSON><PERSON><PERSON><PERSON> Maou
TITLEJA:第六天魔王
SUBTITLE:--<PERSON><PERSON> × <PERSON><PERSON><PERSON> “Godspeed” Aoki
SUBTITLEJA:黒沢ダイスケ × <PERSON><PERSON><PERSON> "Godspeed" Aoki
BPM:150
WAVE:<PERSON><PERSON><PERSON><PERSON>u.ogg
OFFSET:-1.691
DEMOSTART:64.049


COURSE:Edit
LEVEL:10
BALLOON:20,10,20,13,4,4,4,4,4,4,8,3,3
SCOREINIT:630
SCOREDIFF:0

#START
3,
#SCROLL 2
000000000000000000111222, //2

#BPMCHANGE 300
#SCROLL 1
7,
0000000080001110,
7008,
22, //6

7,
0000000080001110,
7,
800000000000000010001000200000100000100100200100, //10

12212212,
2011102012102011,
12211212,
2011102012102011, //14

11212211,
2011102012102011,
1010201010102011,
1020112011112000, //18

#MEASURE 1/4
#BPMCHANGE 75
#SCROLL 24
000580, //19

#MEASURE 4/4
#BPMCHANGE 300
#SCROLL 1
10700820,
1111222211221212,
10700820,
1111222211221212, //23

10700820,
1111222211221212,
1010221122112211,
200200100100200200100100200020001000100000200000, //27

20700820,
2222111122112121,
20700820,
2222111122112121, //31

20700820,
2222111122112121,
2020112211221122,
100100200200100100200200100010002000200000100000, //35

1000111121111000,
1111112111111000,
1222222210002000,
1222222210001111, //39

1121111110001111,
2111100020001111,
200100100100100000000000100010001000200010001000,
100000000000100000000000
#SCROLL 6
#GOGOSTART
600000000008000000000000, //43
#GOGOEND

#SCROLL 1
1000112111211000,
1111211121111000,
1222222210002000,
1222222210001111, //47

2111211110001121,
1121100020001121,
100100200100100000000000100020001000100010002000,
100000000000100000000000
#SCROLL 6
#GOGOSTART
600000000008000000000000,  //51
#GOGOEND

#SCROLL 1
1000112121211000,
1111212121111000,
1222222210002000,
1222222210001111, //55

2121211110001121,
2121100020001121,
200100200100100000000000100020001000200010002000,
100000000000100000000000
#SCROLL 6
#GOGOSTART
600000000008000000000000,  //59
#GOGOEND

#SCROLL 1
11221212,
12212221,
22112121,
22212212, //63

11221212,
12212221,
22112121,
202020102020100200100200, //67

21222222,
2220102220102220,
21221221,
2210102210102210, //71

21211211,
2110102110102110,
200000100000200200100100200200100100200020001000,
1221122112212170, //75

#MEASURE 2/4
#BPMCHANGE 75
#SCROLL 4
0822, //76

#MEASURE 4/4
#BPMCHANGE 300
#SCROLL 1
#GOGOSTART
1110101020001110,
1010202011221020,
1110101020001110,
1010202011221020, //80

100010001000200200200000100010001000200200200000,
1110222011102220,
2011102011107000,
800200101010201010100200, //84

1110202010201110,
2010202011102010,
200200200000200000100100100000100000500000000008,
0020102010212120, //88

100100100000100000200200200000200000500000000008,
000100200100201020102010,
200000100100100000200000100100100000200010002000,
100200100200100020001000200000100000200000100000, //92

1111111020201010,
1111201010202010,
1111111020201010,
1111201010202010, //96

100010001000200200200000100010001000200200200000,
1110222011102220,
2011102011107000,
800200101010201010100200, //100

1111101020201010,
1111202010102020,
1111101020201010,
1111202010102020, //104

1111102011102010,
200010001000100000200000100100100000200000100000,
1111102011102010,
200010001000100000200000100100100000200000100000, //108

222111222111,
221122112121,
2121212121212121,
200200100100200020001000100200100200100200100000, //112
#GOGOEND

40111120,
1010201120101011,
20111120,
1010201120101011, //116

20112212,
12121212,
102012102012,
102012121210, //120

#GOGOSTART
1111101020102010,
1111101020102010,
1110202011102020,
1120201011202010, //124

1111201020102010,
1111201020102010,
1210202012102020,
1210101210101210, //128

1111101020102010,
1111101020102010,
1110202011102020,
1120201011202010, //132

2111202020102010,
2111211120102010,
2111211121112010,
500000000000000000000000000000000000000008000000, //136
#GOGOEND

#SCROLL 0.89
1110102010102220,
#BARLINEOFF
12122111,
#BARLINEON
2011102010102220,
#BARLINEOFF
2010102012121210, //140

#SCROLL 0.8
#BARLINEON
1110102010102220,
#BARLINEOFF
12122111,
#BARLINEON
2011102010102220,
#BARLINEOFF
200100100200102020102020, //144

#SCROLL 0.75 
#BARLINEON
1022201020201110,
#BARLINEOFF
21221222,
#BARLINEON
1022102011221010,
#BARLINEOFF
2020102012121210, //148

#SCROLL 0.71
#BARLINEON
1022201020201110,
#BARLINEOFF
21221222,
#BARLINEON
1022102011221010,
#BARLINEOFF
200200100200102020102020, //152

#BARLINEON
#SCROLL 0.68
#GOGOSTART
100100100100200020002000100100100100200020002000,
#BARLINEOFF
1111222212102010,
#SCROLL 0.65 
#BARLINEON
100100100100200020002000100100100100200020002000,
#BARLINEOFF
1111222212102010, //156

#SCROLL 0.64
#BARLINEON
200200100100200200100100200200100100200010001000,
#BARLINEOFF
200200100100200200100100200010001000200000000000,
#SCROLL 0.63
#BARLINEON
200100200100200100200100200100200100200010001000,
#BARLINEOFF
200100200100200100200100200010001000200000000000, //160

#SCROLL 0.62
#BARLINEON
2222111122112121,
#BARLINEOFF
2222111122112121,
#BARLINEON
2211221122112211,
#BARLINEOFF
2121212121212121, //164

#BARLINEON
#BPMCHANGE 75
#SCROLL 2.48
1,
#BARLINEOFF
#SCROLL 1
0,
0, //167
#GOGOEND
#END


COURSE:Oni
LEVEL:10
BALLOON:20,4,4,5
SCOREINIT:250,720
SCOREDIFF:55

#START
37,
#BARLINEOFF
#SCROLL 2
0000000080001112, //1

#BARLINEON
#BPMCHANGE 300
#SCROLL 1
32,
20002011,
32,
12, //5

32,
20002011,
32,
200000001010200200101000, //9

31211211,
2010102010112010,
31211211,
2010102011112010, //13

31211211,
2010102010122020,
3010221010222010,
2211102222102211, //17

#MEASURE 1/4
#BPMCHANGE 75
#SCROLL 4
000000000000000000000000100010001000100100100100, //18

#MEASURE 4/4
#BPMCHANGE 300
#SCROLL 1
1000400000112010,
1010201010102011,
1000400000112010,
1010201010102211, //22

1000400000112010,
1010201010102011,
1010221010102210,
100000100000200200100000200020002000200000200000, //26

1000400000112010,
1010201010102011,
1000400000112010,
1010201010102211, //30

1000400000112010,
1010201010102011,
2010111020101110,
200010001000100000100000200100100100200000000000, //34

2000212121212000,
2121212121212000,
2211221120002000,
2121212120002121, //38

2121212120002211,
2211200020002121,
200100200100200000000000200020002000200020002000,
200000000000200000000000
#GOGOSTART
600008000000200000000000, //42
#GOGOEND

1000121212121000,
1212121212121000,
1122112210001000,
1212121210001212, //46

1212121210001122,
1122100010001212,
100200100200100000000000100010001000100010001000,
100000000000100000000000
#GOGOSTART
600008000000100000000000, //50
#GOGOEND

2000212121212000,
2121212121212000,
2211221120001000,
1212121210001212, //54

1212121210001122,
1122100020001212,
100200100200100000000000200020002000200020002000,
200000000000200000000000
#GOGOSTART
600008000000200000000000, //58
#GOGOEND

32,
20002011,
32,
20002011, //62

32,
20002011,
32,
400000001010200200101000,  //66

31211211,
2010102010112010,
31211211,
2010102011112010, //70

31211211,
2010102010122020,
300100201010100100202020,
1110112011221222, //74

#MEASURE 2/4
#BPMCHANGE 75
#SCROLL 4
0034, //75

#MEASURE 4/4
#BPMCHANGE 300
#SCROLL 1
#GOGOSTART
3000200010102011,
1010201010112010,
1000200010102011,
1010201010212020, //79

1000200010102011,
1010202011102010,
4000104000112000,
200100202010200200200200, //83

1000200010102011,
1010201021102020,
1110201110207000,
08211211, //87

2110202110207000,
000800200100201010201010,
4000104000117000,
000800101010100200200200, //91

3010201010112011,
1020102010112010,
1010201010112011,
1020102010212020, //95

1010201010112011,
1020202011102010,
4000104000112000,
200000100000200020001000200000200000200000200000, //99

1110201010112011,
1020102011102020,
1110201010112011,
1020102021102010, //103

1110201011102010,
1110211022102020,
1110202011102020,
2110221022202020, //107

111211222122,
111222112211,
2210102220104000,
200100202020200200200200, //111
#GOGOEND

3004,
000100100100101010101010,
1004,
11012110, //115

12222211,
11111122,
122222111111,
112200400000, //119

#GOGOSTART
1110101011102010,
1110201011102010,
1110101011102010,
1110201011102010, //123

1110101011102010,
1110201011102020,
12211221,
200100100000100000200100100000100000200010001000, //127

1110101011102010,
1110201011102010,
1110101011102010,
1110201011102010, //131

1110101020102010,
1110201020102010,
1120201020102020,
400100400100400010101010, //135
#GOGOEND

1010201011102011,
0110201011102011,
0110102011102020,
1010121022102220, //139

1010201021102011,
0110201021102011,
0110102021102020,
100000100000100200100000200020001000200020002000, //143

1010201022102011,
0110201022102011,
0110102022102020,
1010121022102220, //147

1010201021202011,
0110201021202011,
0110102021202020,
100000100000100200100000200020001000200020002000, //151

#GOGOSTART
100100100000100020002000200000200000100010001000,
200200200000200010001000100000100000200020002000,
100100100000100010001000200200200000200020002000,
100100100000200020002000100200100200200100200100, //155

5,
000000000000000000000008000000000000000000000000,
5,
000000000000000000000008000000000000000000000000, //159

2111112111112111,
1121111211112111,
1211121112111211,
2112112112121212, //163

#BPMCHANGE 75
#SCROLL 4
1,
#SCROLL 1
0,
0, //166
#GOGOEND
#END


COURSE:Hard
LEVEL:8
BALLOON:16,26
SCOREINIT:270,1030
SCOREDIFF:60

#START
37,
#BARLINEOFF
#SCROLL 2
0000000080002222, //1

#BARLINEON
#BPMCHANGE 300
#SCROLL 1
3,
00000011,
3,
22, //5

3,
00000011,
3,
00002220, //9

11101101,
10110110,
11101101,
10110110, //13

11101101,
10110110,
11201201,
2000222000222000, //17

#MEASURE 1/4
#BPMCHANGE 75
#SCROLL 4
0, //18

#MEASURE 4/4
#BPMCHANGE 300
#SCROLL 1
30400120,
1011102010101020,
10400120,
1011102010101010, //22

10400120,
1011102010101020,
12221222,
100100100100202020200200, //26

10400120,
1011102010101020,
10400120,
1011102010101010, //30

10400120,
1011102010101020,
12221222,
100010001000100000100000200200200000200000000000, //34

10211110,
21111110,
21111010,
21111021, //38

11111021,
11101021,
100100100000202020200000,
22
#GOGOSTART
32, //42
#GOGOEND

10221110,
22111110,
22111010,
22111022, //46

11111022,
11101022,
100100100000202020200000,
22
#GOGOSTART
32, //50
#GOGOEND

10222110,
22211110,
22211010,
22211022, //54

21111022,
21101022,
200100100000202020200000,
22
#GOGOSTART
32, //58
#GOGOEND

12,
20002011,
12,
20002011, //62

12,
20002011,
12,
20002220, //66

11101101,
10210210,
11101101,
10211210, //70

11101101,
10210210,
11201201,
2000222000222000, //74

#MEASURE 2/4
#BPMCHANGE 75
#SCROLL 4
0034, //75

#MEASURE 4/4
#BPMCHANGE 300
#SCROLL 1
#GOGOSTART
3000200010002011,
11201120,
1000200010002011,
11201122, //79

1000200010002011,
11221121,
40140140,
200100202020200200200200, //83

1000200010102011,
11201120,
11211220,
00201122, //87

11211220,
000100222200,
4000104000112000,
000000202020200200200200, //91

1010200010002011,
11201121,
1010200010002011,
11201122, //95

1010200010002011,
11221121,
40140140,
200100202020200200200200, //99

1000101020101011,
11211120,
1000101020101011,
11211120, //103

11112222,
1010101022202000,
11112222,
1010101022202000, //107

100111100111,
100222200222,
10230230,
200100202020200200200200, //111
#GOGOEND

3004,
000100100100101010100000,
3004,
11012110, //115

12222211,
11111122,
311422,
322300, //119

#GOGOSTART
11111121,
11211121,
11111121,
11211121, //123

11111121,
11211121,
11111111,
300000100300000100101010, //127

11111121,
11211121,
11111121,
11211121, //131

11111121,
11211121,
11212121,
4440, //135
#GOGOEND

11211021,
01211021,
01121022,
1010100022202020, //139

11211021,
01211021,
01121022,
100100100000202020200000, //143

11212021,
01212021,
01121022,
1010100022202020, //147

11212021,
01212021,
01121022,
100100100000202020200000, //151

#GOGOSTART
100100101010100100101010,
200200202020200200202020,
100100101010200200202020,
100100202020100200100200, //155

5,
000000000000000000000008000000000000000000000000,
5,
000000000000000000000008000000000000000000000000, //159

7,
0,
0,
8000000000001111, //163

#BPMCHANGE 75
#SCROLL 4
1,
#SCROLL 1
0,
0, //166 
#GOGOEND
#END


COURSE:Normal
LEVEL:7
BALLOON:10,2,8,6,20
SCOREINIT:290,1430
SCOREDIFF:63

#START
37,
#BARLINEOFF
08, //1

#BARLINEON
#BPMCHANGE 300
3,
0,
3,
0, //5

3,
0,
3,
0, //9

1,
00000011,
1,
11, //13

1,
00001111,
1,
30030030, //17

#MEASURE 1/4
#BPMCHANGE 75
#SCROLL 4
0, //18

#MEASURE 4/4
#BPMCHANGE 300
#SCROLL 1
1201,
11101110,
1201,
11101111, //22

1201,
11101110,
22202220,
11101122, //26

1201,
11101110,
1201,
11101111, //30

1201,
11101110,
22202220,
10102120, //34

10111110,
11111110,
11111010,
11111011, //38

11111011,
11101011,
100100100000202020200000,
22
#GOGOSTART
32, //42
#GOGOEND

10111110,
11111110,
11111010,
11111011, //46

11111011,
11101011,
100100100000202020200000,
22
#GOGOSTART
32, //50
#GOGOEND

10111110,
11111110,
11111010,
11111011, //54

11111011,
11101011,
100100100000202020200000,
22
#GOGOSTART
32, //58
#GOGOEND

12,
20002011,
12,
20002011, //62

12,
20002011,
12,
34, //66

30100100,
40020020,
30100100,
40022020, //70

30100100,
40020020,
30300300,
40040040, //74

#MEASURE 2/4
#BPMCHANGE 75
#SCROLL 4
0034, //75

#MEASURE 4/4
#BPMCHANGE 300
#SCROLL 1
#GOGOSTART
30201021,
01201120,
10201021,
01201122, //79

10201021,
01201120,
30030030,
01202222, //83

10112011,
01102220,
11210120,
00102220, //87

11210120,
000100222200,
30030070,
00802222, //91

10201021,
01201120,
10201021,
01201122, //95

10201021,
01201120,
30030030,
01202222, //99

10112011,
01102220,
10112011,
01102220, //103

11112222,
11112220,
11112222,
11112220, //107

7,
0008,
5,
000000000000000000000008000000000000000000000000, //111
#GOGOEND

3004,
0,
3004,
0, //115

11111022,
22222010,
7,
8040, //119

#GOGOSTART
11101110,
11201120,
11101110,
11201120, //123

11101110,
11201120,
11111111,
30130130, //127

11101110,
11201120,
11101110,
11201120, //131

11101110,
11201120,
11111111,
3330, //135
#GOGOEND

11011021,
01011021,
01101020,
11102222, //139

11011021,
01011021,
01101020,
100100100000202020200000, //143

11011021,
01011021,
01101020,
11102222, //147

11011021,
01011021,
01101020,
100100100000202020200000, //151

#GOGOSTART
11202210,
22101120,
11102220,
11201212, //155

5,
000000000000000000000008000000000000000000000000,
5,
000000000000000000000008000000000000000000000000, //159

9,
0,
0,
0, //163

#BPMCHANGE 75
#SCROLL 4
0000090000008000,
#SCROLL 1
0,
0, //166
#GOGOEND
#END


COURSE:Easy
LEVEL:5
BALLOON:8,3,9,16,16
SCOREINIT:210,2010
SCOREDIFF:43

#START
37,
#BARLINEOFF
08, //1

#BARLINEON
#BPMCHANGE 300
3,
0,
3,
0, //5

3,
0,
3,
0, //9

1,
00000011,
1,
11, //13

1,
00001110,
1,
30030030, //17

#MEASURE 1/4
#BPMCHANGE 75
#SCROLL 4
0, //18

#MEASURE 4/4
#BPMCHANGE 300
#SCROLL 1
1200,
1110,
1200,
1110, //22

1200,
1110,
11101110,
10002220, //26

1200,
1110,
1200,
1110, //30

1200,
1110,
11101110,
1044, //34

10111000,
11101000,
11100010,
11100011, //38

10100011,
10001011,
1022,
11
#GOGOSTART
30, //42
#GOGOEND

10111000,
11101000,
11100010,
11100011, //46

10100011,
10001011,
1022,
11
#GOGOSTART
30, //50
#GOGOEND

10111000,
11101000,
11100010,
11100011, //54

10100011,
10001011,
1022,
11
#GOGOSTART
30, //58
#GOGOEND

1,
00000011,
1,
00000011, //62

1,
00000011,
1,
22, //66

10100100,
20020020,
10100100,
20020020, //70

10100100,
20020020,
30300300,
40040040, //74

#MEASURE 2/4
#BPMCHANGE 75
#SCROLL 4
0034, //75

#MEASURE 4/4
#BPMCHANGE 300
#SCROLL 1
#GOGOSTART
30101011,
0122,
10101011,
0122, //79

10101011,
0222,
30030030,
00202220, //83

10102011,
0222,
10110110,
00102220, //87

10110110,
0122,
30030070,
08, //91

10101011,
00102220,
10101011,
00102220, //95

10101011,
00202220,
30030030,
00202220, //99

10102011,
01102020,
10102011,
01102000, //103

10101110,
10102220,
10101110,
10102220, //107

7,
0008,
5,
000000000000000000000008000000000000000000000000, //111
#GOGOEND

3003,
0,
3003,
0, //115

7,
0,
0,
08, //119

#GOGOSTART
11101110,
1212,
11101110,
1212, //123

11101110,
1212,
11102220,
30030030, //127

11101110,
1212,
11101110,
1212, //131

11101110,
1212,
11102220,
3330, //135
#GOGOEND

11011001,
01011001,
01101000,
1120, //139

11011001,
01011001,
01101000,
11102000, //143

11011001,
01011001,
01101000,
1120, //147

11011001,
01011001,
01101000,
11102000, //151

#GOGOSTART
11101110,
22202220,
11102220,
10101111, //155

5,
000000000000000000000008000000000000000000000000,
5,
000000000000000000000008000000000000000000000000, //159

9,
0,
0,
0, //163

#BPMCHANGE 75
#SCROLL 4
0000090000008000,
#SCROLL 1
0,
0,  //166
#GOGOEND
#END