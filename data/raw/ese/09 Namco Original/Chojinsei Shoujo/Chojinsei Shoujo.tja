//TJADB Project
TITLE:Chojinsei Shoujo
TITLEJA:超新星少女
SUBTITLE:--<PERSON><PERSON><PERSON>(BNSI) feat. <PERSON><PERSON> / <PERSON><PERSON><PERSON>t Theme Song
SUBTITLEJA:<PERSON><PERSON><PERSON>(BNSI) feat. 巫てんり 「ドンドコフィット」テーマソング
BPM:165
WAVE:<PERSON><PERSON><PERSON> Shoujo.ogg
OFFSET:-1.533
DEMOSTART:90.259


COURSE:Oni
LEVEL:8
BALLOON:21,4,4,4,16,22
SCOREINIT:450,1270
SCOREDIFF:100

#START
1000100020020010,
0200200200101120,
1000100120021020,
1122112210221120,

1000102010210010,
0120112220101120,
1000102010210210,
2210112211221070, //8

0,
000000080000000000000000000000000000100000500000,
000000000000000000000000000000000000000008000000,
1020111110102000,

3011202210120210,
1202102010221202,
1020221210120210,
1202102110202000, //16

10000112,
0,
10000221,
0,

10000112,
0,
10000221,
0010112011111110, //24

1020112010201120,
1020112010211020,
1020112010201120,
1020112010211020,

1020112010201120,
1020112010211020,
1021001021001021,
2010101020021122, //32

1010200211222020,
1010202011221020,
1010200211222020,
1022202012211020,

7878,
7833,
4000100010010020,
2002001212212212, //40

1020112010201120,
1020112010201120,
1010101011101110,
1111222211112000,
0, //45

30012110,
0010010020112222,
0000100020010010,
0100101020112210,

40212112,
0020221020122220,
3000100020010210,
000100000200100000101010100000100100200000200000, //53

#GOGOSTART
1021101020211020,
1011102020111020,
1021101020211020,
1021102210202000,

1022102210212010,
1022102210212210,
700000000000000000000000000000000000000000000008,
0343, //61
#GOGOEND 

1000100020020010,
0200200200101120,
1000100120021020,
1122112210221120,

1020102010210010,
0120112220101120,
1020102010210210,
2210112211221120, //69

#GOGOSTART
1120112010210120,
1120112010222020,
1120112010210120,
2220202210201020,

1120112010210120,
1120112010222020,
1120112010210210,
1120112211111111, //77

2000200020012010,
2210221120102070,
0,
0834, //81
#GOGOEND

1022102210211210,
2102101210121220,
2011201120121120,
1221201220102220,

1120112010211210,
2102101120121220,
2210221220122022, //88

#MEASURE 13/8
1102101220
#SCROLL 3
3040000000000000,

#BARLINEOFF
#MEASURE 4/4
#SCROLL 1
0,
0, //91
#END


COURSE:Hard
LEVEL:6
BALLOON:17,16,3,3,3,13,11,18
SCOREINIT:620,2300
SCOREDIFF:150

#START
1000100020020010,
01010201,
1000100020010020,
00000112,

1000100010020010,
01120112,
1000200010010020,
01120017, //8

0,
000000080000000000000000000000000000100000900000,
000000000000000000000000000000000090000000000008,
0,

3000200010020010,
0200102010201000,
1000200010020010,
0200100100202000, //16

10000111,
0,
10000222,
0,

10000111,
0,
10000222,
000000100000500000000000000000000008000000000000, //24

12101210,
1020100010111000,
12101210,
1020101110101000,

12101210,
1020100010111010,
5,
000000000008000000000000200000000200200000200000, //32

10201021,
02021212,
10201102,
0022202010010020,

7878,
7833,
4000100010010020,
2002001010010010, //40

2111,
1111,
11111111,
500000000000000000000000000000000008000000000000,
0, //45

30002110,
0010010020102220,
0000100020010010, 
500000000000000000000008000000000000000000000000,

40012110,
00012120,
3000100020010010,
500000000000000000000000000000000008000000000000, //53

#GOGOSTART
12102210,
11102110,
12102210,
12121220,

1000200010020010,
0020102010222000,
900000000000000000000000000000000000900000000008,
0, //61
#GOGOEND

1000100020020010,
01010201,
1000100020010020,
500000000000000000000008000000200000200000000000,

1000100010020010,
01120112,
1000200010010070,
0008, //69

#GOGOSTART
12121212,
1020102010222000,
12121212,
1010202200202000,

12121212,
1020102010222000,
12121212,
100000100000100100100000500000000000000008000000, //77

2222,
20202027,
0,
0834, //81
#GOGOEND

1000100010011010,
0100101010101000,
2000200020020020,
0220200220202000,

1000100010011010,
0100101110101110,
2000200220022020, //88

#MEASURE 13/8
1100102020
#SCROLL 1.5
3040000000000000,

#BARLINEOFF
#MEASURE 4/4
#SCROLL 1
0,
0, //91
#END


COURSE:Normal
LEVEL:5
BALLOON:11,10,2,2,2,9,8,7,13,6,6,6
SCOREINIT:850,4110
SCOREDIFF:220

#START
10101001,
0,
10101002,
0,

10101001,
01020000,
10101005,
000000000008000000500000000008000000000000700000, //8

0,
000000080000000000000000000000000000000000900000,
000000000000000000000000000000000090000000000008,
0,

30001001,
0110,
10001001,
0110, //16

10000101,
0,
10000202,
0,

10000101,
0,
10000202,
0, //24

1122,
1120,
1122,
1120,

1122,
1122,
5,
000000000008000000000000000000000000000000000000, //32

1202,
1202,
1202,
1202,

7878,
7833,
3,
1001000010010000, //40

1110,
1110,
11101110,
500000000000000000000000000000000008000000000000,
0, //45

32,
0022,
00102005,
000000000000000000000008000000000000000000000000,

42,
00012000,
3120,
500000000000000000000000000000000008000000000000, //53

#GOGOSTART
1212,
1122,
1212,
10201220,

10101001,
0220,
900000000000000000000000000000000000900000000008,
0, //61
#GOGOEND

10101001,
01010000,
10101002,
0,

10101001,
01020000,
10101007,
0008, //69

#GOGOSTART
10201120,
10201120,
10201120,
10220000,

10201120,
10201120,
10201007,
0008, //77

22,
20002007,
0,
000000000000000000080000000000000000000000000000, //81
#GOGOEND

1000100010010010,
7008,
2000200020020020,
7008,

3000300030030030,
7008,
4000400040040040, //88

#MEASURE 13/8
0000014000000,
#BARLINEOFF
#MEASURE 4/4
0,
0, //91
#END


COURSE:Easy
LEVEL:3
BALLOON:9,8,8,7,6,6,11,5,5,5
SCOREINIT:820,6930
SCOREDIFF:255

#START
10001001,
0,
10001002,
0,

10001001,
0,
10001005,
000000000008000000500000000008000000000000700000, //8

0,
000000080000000000000000000000000000000000900000,
000000000000000000000000000000000090000000000008,
0,

3,
0,
3,
0, //16

10000001,
0,
10000002,
0,

10000001,
0,
10000002,
0, //24

11,
1110,
11,
1110,

11,
1111,
5,
000000000008000000000000000000000000000000000000, //32

12,
11,
12,
11,

7,
08003030,
3,
22, //40

11,
11,
1111,
500000000000000000000000000000000008000000000000,
0, //45

32,
02,
00002005,
000000000000000000000008000000000000000000000000,

42,
02,
0500,
000000000000000000000000000000000008000000000000, //53

#GOGOSTART
11,
1110,
11,
1110,

10002005,
000000000000000000000008000000000000000000000000,
900000000000000000000000000000000000900000000008,
0, //61
#GOGOEND

10001001,
0,
10001002,
0,

10001001,
0,
10001007,
0008, //69

#GOGOSTART
1110,
11,
2220,
2,

1110,
11,
20202007,
0008, //77

2,
00000007,
0,
000000000000000000080000000000000000000000000000, //81
#GOGOEND

1110,
7008,
2220,
7008,

3330,
7008,
4440, //88

#MEASURE 13/8
0000003000000,
#BARLINEOFF
#MEASURE 4/4
0,
0, //91
#END