//TJADB Project
TITLE:<PERSON><PERSON> no Ekubo
TITLEJA:エリンギのエクボ
SUBTITLE:--<PERSON><PERSON><PERSON> feat. <PERSON><PERSON>
SUBTITLEJA:太鼓の達人オリジナル曲
BPM:147
WAVE:<PERSON><PERSON> no <PERSON><PERSON>.ogg
OFFSET:-2.363
DEMOSTART:73.757

COURSE:Oni
LEVEL:8
BALLOON:12
SCOREINIT:470
SCOREDIFF:115


#START


#MEASURE 5/4

0,
0,
0001010121,
1221012101,
10202010201000102211,
1221012101,
1221021112,
1221012101,
10202010201000102211,
10010020010010001011,  //10

10202011202020104000,
1212121121,
2121211212,
1212121121,
20101022201020201020,
1212121121,
2121211212,
1212121121,
3023021130,
10101022201110201020,  //20

20202011102220102010,
10101022201110201020,
40303022201110201020,
10101022201110201020,
20202011102220102010,
1112110201,
30303022203030300000,
1021012120,
20001020001020202220,
21101020000112211020,//30

000000100000200200200100100000000000202000002020000020200100,
1021012120,
12201020000112211020,
22201002010112020220,
10101010101010102022,
#MEASURE 6/4
102222102211122110500080,
#MEASURE 7/8
#GOGOSTART
10201101120120,
10201101210110,
10201101120120,
10201101210110,  //40

10201101120120,
10201101210110,
11011021110111,
20220220112112,
10201101120120,
10201101210110,
10201101120120,
10201101201200,
10201101120120,
10201101210110,  //50

10112010201201,
50000080300300,
10111022112101,
20112021121101,
10122011211101,
10221012111101,
20211021111102,
200000100100200020000200100100200100100100,
#MEASURE 4/4
1010101210101012,
1010101212102012,  //60

1010101210101012,
1010700000000080,
#GOGOEND
#MEASURE 5/4
10102011102010102011,
10102011102010102222,
1211012101,
20121212211212122222,
10102011102010102011,
10102011102010102222,
10012010012010001022,
11122011122011112020,  //70
#MEASURE 4/4
3,


#END


COURSE:Hard
LEVEL:7
BALLOON:10
SCOREINIT:520
SCOREDIFF:135


#START


#MEASURE 5/4

0,
0,
0001010121,
1021012101,
10002010201000102211,
1021012101,
1021021122,
1021012101,
10002010201000102211,
1021012101,  //10

10202011202010102000,
1010101102,
1010101102,
1010101102,
10101000002210201020,
1010101102,
1010101102,
1010101102,
1021021130,
10101000101110200020,  //20

10101000101110200020,
10101000101110200020,
10101000002220102010,
10101000101110200020,
10101000101110200020,
1112110201,
1110011100,
00221,
1000202000,
0000202011,  //30

2012012210,
1000202011,
1000202011,
1000202010,
10101010101010101011,
#MEASURE 6/4
100110200220100110201111,
#GOGOSTART
#MEASURE 7/8
10001000100100,
10001000100100,
10001000100100,
10001000200200,  //40

10001000100100,
10001000100100,
10001000100100,
10001000111122,
10011020100100,
10011020100100,
10011020100100,
10011020220220,
10011020102102,
10011020102102,  //50

10011020102102,
50000080300300,
10111020110220,
20111010220110,
10111020110220,
20111010220110,
10111020110220,
20111010220110,
#MEASURE 4/4
1010101110101011,
1010101212121012,  //60

1010101110101011,
1010700000000080,
#GOGOEND
#MEASURE 5/4
10002010001110202010,
10002010002210202010,
1012011001,
20001010001012122121,
10002010001110202010,
10002010002210202010,
1012011012,
11122011122011112020,  //70
#MEASURE 4/4
3,


#END


COURSE:Normal
LEVEL:6
BALLOON:8
SCOREINIT:610
SCOREDIFF:175


#START


#MEASURE 5/4

0,
0,
0001010120,
1001012000,
1001210010,
1001012000,
1001001120,
1001012000,
1001210010,
1001012000,  //10

1001001110,
1010101100,
1010101100,
1010101100,
1010120200,
1010101100,
1010101100,
1010101100,
1001001130,
1010102100,  //20

1010102100,
1010102100,
1110022200,
1010102100,
1010102100,
1010110101,
1110011100,
00220,
00220,
00220,  //30

00221,
10220,
10220,
10220,
1111111130,
#MEASURE 6/4
1113,
#GOGOSTART
#MEASURE 7/8
10002000100100,
10002000100100,
10002000100100,
10002000200200,  //40

10002000100100,
10002000100100,
10002000100100,
10001000110110,
10002000100100,
10002000100100,
10002000100100,
10002000200200,
10002000100100,
10002000100100,  //50

10002000100100,
50000080300300,
10002000110110,
10002000110110,
10002000110110,
10002000220220,
10002000110110,
10002000110110,
#MEASURE 4/4
1000101110001011,
1000101110001011,  //60

1000101110001011,
1010700000000080,
#GOGOEND
#MEASURE 5/4
1001012010,
1001012010,
1001012010,
1001001120,
1001012010,
1001012010,
1001001020,
1001001120,  //70
#MEASURE 4/4
3,


#END


COURSE:Easy
LEVEL:5
BALLOON:8
SCOREINIT:770
SCOREDIFF:303


#START


#MEASURE 5/4
#SCROLL 0.8

0,
0,
0,
50000800,
10200,
50000800,
3003000000,
50000800,
10200,
50000800,  //10

3003003000,
10100,
10100,
10100,
2220000000,
10100,
10100,
10100,
3003003000,
11100,  //20

11100,
11100,
2220000000,
11100,
11100,
11100,
1010010100,
00220,
00220,
00220,  //30

00110,
00220,
00220,
00220,
70008,
#MEASURE 6/4
1111,
#GOGOSTART
#MEASURE 7/8
58,
1010000,
1010000,
2020000,  //40

1010000,
1010000,
1010100,
3,
58,
1010000,
1010000,
2020000,
1010000,
1010000,  //50

1010000,
5000080,
1010100,
1010100,
1010100,
1010100,
2020200,
2020200,
#MEASURE 4/4
5,
0008,  //60

6,
08,
#GOGOEND
#MEASURE 5/4
1111000000,
1111000000,
1111000000,
2222000000,
1111000000,
1111000000,
1111000000,
2002002000,  //70
#MEASURE 4/4
3,//146combo


#END

