//TJADB Project
TITLE:<PERSON><PERSON><PERSON><PERSON> wo Utsu
TITLEJA:沓の子を打つ
SUBTITLE:--ka<PERSON><PERSON>
SUBTITLEJA:風薙ぎ(kazenagh)
BPM:155
WAVE:<PERSON><PERSON><PERSON><PERSON> wo Utsu.ogg
OFFSET:-1.235
DEMOSTART:38.199

COURSE:Edit
LEVEL:10
BALLOON:5
SCOREINIT:1220
SCOREDIFF:0

#START
#MEASURE 1/64
#SCROLL 9
0,
#SCROLL 8
0,
#SCROLL 7
0,
#SCROLL 6
0,
#SCROLL 5
0,
#SCROLL 4
0,
#SCROLL 3
0,
#SCROLL 2
0, //8

#MEASURE 4/4
#SCROLL 1
1220222212222020,
100200200000202020200200100200200200200000200000,
10002020202020201020002220200000,
1020222212221200,

1200122212221020,
100200200000102020200200100200200200100000200000,
10002020102020201020002210200000,
10100010100010100010100011100000,

000200020002,
#MEASURE 3/4
100000000100200000000100200020002000200000002000100000000000000000000000, //18

#MEASURE 7/8
10201220122010,
10201220122010,
10201220122010,
22101012204040,

10201220122010,
10201220112211,
10201220122010,
7080000, //26

#BARLINEOFF
#MEASURE 1/32
1,
#BARLINEON
#MEASURE 1/16
#SCROLL 0.6
0,
0
#SCROLL 1
2,
#SCROLL 0.6
0,
0,
0,
0
#SCROLL 1
1,
#SCROLL 0.6
0
#SCROLL 1
1,
#SCROLL 0.6
0,
0,
0
#SCROLL 1
2,
#SCROLL 0.6
0,
0,
0, //40

0
#SCROLL 1
1,
#SCROLL 0.6
0
#SCROLL 1
1,
#SCROLL 0.6
0,
0,
0
#SCROLL 1
2,
#SCROLL 0.6
0,
0,
0,
0
#SCROLL 1
1,
#SCROLL 0.6
0
#SCROLL 1
1,
#SCROLL 0.6
0,
0
#SCROLL 1
2,
#SCROLL 0.6
0,
0
#SCROLL 1
2, //54

#SCROLL 0.6
0
#SCROLL 1
1,
#SCROLL 0.6
0,
0
#SCROLL 1
2,
#SCROLL 0.6
0,
0,
0,
0
#SCROLL 1
1,
#SCROLL 0.6
0
#SCROLL 1
1,
#SCROLL 0.6
0,
0,
0
#SCROLL 1
2,
#SCROLL 0.6
0,
0,
0, //68

0
#SCROLL 1
1,
#SCROLL 0.6
0
#SCROLL 1
1,
#SCROLL 0.6
0
#SCROLL 1
1,
#SCROLL 0.6
0
#SCROLL 1
1,
#SCROLL 0.6
0
#SCROLL 1
2,
#SCROLL 0.6
0
#SCROLL 1
2,
#SCROLL 0.6
0
#SCROLL 1
2,
#SCROLL 0.6
0
#SCROLL 1
1,
#SCROLL 0.6
0
#SCROLL 1
1,
#SCROLL 0.6
0
#SCROLL 1
2,
#SCROLL 0.6
0
#SCROLL 1
2,
#SCROLL 0.6
0
#SCROLL 1
1,
#SCROLL 0.6
0
#SCROLL 1
1,
#SCROLL 0.6
0
#SCROLL 1
2,
#MEASURE 1/32
#SCROLL 0.6
0, //83

#MEASURE 7/8
#SCROLL 1
10222010122202,
22221020111202,
100000100000100000100000100100100100101010,
0, //87

#MEASURE 7/4
#BPMCHANGE 310
#SCROLL 0.5
1000001022200020102020102000,
12010120211222,
1020201000100020100022100000,
30000002021020,

21020210122202,
1000202020100000222010002220,
#GOGOSTART
10210010102011,
1020001000001000202210000000, //95
#GOGOEND

12021022112010,
2210001000102020001020202010,
1010202010002210001020001010,
10120201012100,

#SCROLL 1.5
500000000000000000000000000008000000000000000000
#SCROLL 0.5
100000100000200000200000100000000000,
2210001000102010001020202010,
200000200000100000200000200000100000100000000000200020002000100000000000000000200200,
2010102010001000202210202020, //103

1000000000202220002020200000,
1000222210001000002010001000,
22110122100000,
100000100102020100000200200200100000000000,

#SCROLL 1.5
4000
#SCROLL 0.5
2100112101,
0030002000002010002000100022,
102020100100000200222,
#MEASURE 5/4
20201000001010222000, //111

#MEASURE 1/8
#SCROLL 1
0,
#SCROLL 2
0,
#SCROLL 3
0,
#SCROLL 4
0, //115

#SCROLL 0.5
3,
#SCROLL 4
0,
#SCROLL 3.86
0,
#SCROLL 3.71
0,
#SCROLL 3.57
0,
#SCROLL 3.43
0,
#SCROLL 3.29
0,
#SCROLL 3.14
0,
#SCROLL 3
0,
#SCROLL 2.86
0,
#SCROLL 2.71
0,
#SCROLL 2.57
0,
#SCROLL 2.43
0,
#SCROLL 2.29
0,
#SCROLL 2.14
0,
#SCROLL 2
0,
#SCROLL 1.86
0,
#SCROLL 1.71
0,
#SCROLL 1.57
0,
#SCROLL 1.43
0,
#SCROLL 1.29
0,
#SCROLL 1.14
0,
#MEASURE 3/4
#SCROLL 1
0,
#BARLINEOFF
#MEASURE 7/4
#BPMCHANGE 155
#SCROLL 1
0, //139

#BARLINEON
#MEASURE 7/8
1200000,
2020000,
1200222,
#SCROLL 2
4, //143

#SCROLL 1.16
#GOGOSTART
30
#GOGOEND
#SCROLL 1
22
#SCROLL 1.16
#GOGOSTART
30
#GOGOEND
#SCROLL 1
22
#SCROLL 1.16
#GOGOSTART
30
#GOGOEND
#SCROLL 1
22
#SCROLL 1.16
#GOGOSTART 
30,

30
#GOGOEND
#SCROLL 1
22
#SCROLL 1.16
#GOGOSTART
30
#GOGOEND
#SCROLL 1
22
#SCROLL 1.16
#GOGOSTART
30
#GOGOEND
#SCROLL 1
22
#SCROLL 1.16
#GOGOSTART 
30, //145

30
#GOGOEND
#SCROLL 1
22
#SCROLL 1.16
#GOGOSTART
30
#GOGOEND
#SCROLL 1
22
#SCROLL 1.16
#GOGOSTART
30
#GOGOEND
#SCROLL 1
22
#SCROLL 1.16
#GOGOSTART 
30,

30
#GOGOEND
#SCROLL 1
22
#SCROLL 1.16
#GOGOSTART
30
#GOGOEND
#SCROLL 1
22
#SCROLL 1.16
#GOGOSTART
30
#GOGOEND
#SCROLL 1
22
#SCROLL 1.16
#GOGOSTART 
30, //147

30
#GOGOEND
#SCROLL 1
22
#SCROLL 1.16
#GOGOSTART
30
#GOGOEND
#SCROLL 1
22
#SCROLL 1.16
#GOGOSTART
30
#GOGOEND
#SCROLL 1
22
#SCROLL 1.16
#GOGOSTART 
30,

30
#GOGOEND
#SCROLL 1
22
#SCROLL 1.16
#GOGOSTART
30
#GOGOEND
#SCROLL 1
22
#SCROLL 1.16
#GOGOSTART
30
#GOGOEND
#SCROLL 1
22
#SCROLL 1.16
#GOGOSTART 
30, //149

30
#GOGOEND
#SCROLL 1
22
#SCROLL 1.16
#GOGOSTART
30
#GOGOEND
#SCROLL 1
22
#SCROLL 1.16
#GOGOSTART
30
#GOGOEND
#SCROLL 1
22
#SCROLL 1.16
#GOGOSTART 
30,
3, //151
#GOGOEND

#SCROLL 0.5
22212221222121,
22212221222121,
22212221222121,
22212221222121,

#SCROLL 1
10221022102210,
#SCROLL 1.5
10221022101202,
#MEASURE 7/4
#SCROLL 2
100100100100100100100100101010100101010100000000000000000000000000000000000000000000, //158

#MEASURE 7/8
#SCROLL 1
#GOGOSTART
12201220122012,
12201220122012,
100100202020202020100100100200200000100200,
10210010201021,

12201220122012,
12201220122012,
12210122101122,
#MEASURE 3/4
010102101020,
#MEASURE 1/32
2,
2,
2,
2, //170
#GOGOEND

#MEASURE 7/4
#BPMCHANGE 310
#SCROLL 0.5
1000201000101010200022200020,
12212212202101,
2220222000201010200020100020,
1000001000001010002002002020,

1000222020001000201000100000,
10222010221010,
2220002000222000200010202222,
1000002000100000100000222200, //178
#END


COURSE:Oni
LEVEL:8
BALLOON:4,6
SCOREINIT:1680
SCOREDIFF:0

#START
#MEASURE 1/64
#SCROLL 9
0,
#SCROLL 8
0,
#SCROLL 7
0,
#SCROLL 6
0,
#SCROLL 5
0,
#SCROLL 4
0,
#SCROLL 3
0,
#SCROLL 2
0, //8

#MEASURE 4/4
#SCROLL 1
1220222212222000,
1220202212222000,
1022222212022200,
1020222212221000,

1200122212221000,
1220102212221000,
1022122212021000,
1101101101102200,

000200020002,
#MEASURE 3/4
1, //18

#MEASURE 7/8
10201220102010,
10201022102010,
10201220102010,
22101010202020,

10201220102010,
10201020102211,
10201022102010,
7080000, //26

#BARLINEOFF
#MEASURE 1/32
2,
#BARLINEON
#MEASURE 1/16
#SCROLL 0.6
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0, //40

0
#SCROLL 1
2,
#SCROLL 0.6
0,
0,
0,
0
#SCROLL 1
2,
#SCROLL 0.6
0,
0,
0,
0
#SCROLL 1
2,
#SCROLL 0.6
0,
0,
0
#SCROLL 1
1,
#SCROLL 0.6
0,
0
#SCROLL 1
1, //54

#SCROLL 0.6
0
#SCROLL 1
2,
#SCROLL 0.6
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0, //68

0,
0,
0
#SCROLL 1
1,
#SCROLL 0.6
0
#SCROLL 1
1,
#SCROLL 0.6
0
#SCROLL 1
1,
#SCROLL 0.6
0,
0
#SCROLL 1
2,
#SCROLL 0.6
0
#SCROLL 1
2,
#SCROLL 0.6
0
#SCROLL 1
2,
#SCROLL 0.6
0,
0
#SCROLL 1
1,
#SCROLL 0.6
0
#SCROLL 1
1,
#SCROLL 0.6
0
#SCROLL 1
1,
#SCROLL 0.6
0,
#MEASURE 1/32
0, //83

#MEASURE 7/8
#SCROLL 1
10221020102010,
12201020111202,
100000100000100000100000100100100100101010,
0, //87

#MEASURE 7/4
#BPMCHANGE 310
#SCROLL 0.5
10011010200000,
11010010102000,
00110100201100,
3,

10011010200000,
11010010220022,
#GOGOSTART
10010010100020,
10010010222000, //95
#GOGOEND

10021020102010,
21010120102220,
11112222011011,
1,

#SCROLL 1.5
500000000000000000000000000008000000000000000000
#SCROLL 0.5
100000000000200000000000100000000000,
21010120102220,
11010020000000,
00022010222000, //103

10000222022200,
10112000021000,
12210122100000,
10110102021000,

#SCROLL 1.5
4000
#SCROLL 0.5
1010220101,
03000030000300,
002020100100000200000,
#MEASURE 5/4
0010022210, //111

#MEASURE 1/8
#SCROLL 1
0,
#SCROLL 2
0,
#SCROLL 3
0,
#SCROLL 4
0, //115

#SCROLL 0.5
3,
#SCROLL 4
0,
#SCROLL 3.86
0,
#SCROLL 3.71
0,
#SCROLL 3.57
0,
#SCROLL 3.43
0,
#SCROLL 3.29
0,
#SCROLL 3.14
0,
#SCROLL 3
0,
#SCROLL 2.86
0,
#SCROLL 2.71
0,
#SCROLL 2.57
0,
#SCROLL 2.43
0,
#SCROLL 2.29
0,
#SCROLL 2.14
0,
#SCROLL 2
0,
#SCROLL 1.86
0,
#SCROLL 1.71
0,
#SCROLL 1.57
0,
#SCROLL 1.43
0,
#SCROLL 1.29
0,
#SCROLL 1.14
0,
#MEASURE 3/4
#SCROLL 1
0,
#BARLINEOFF
#MEASURE 7/4
#BPMCHANGE 155
#SCROLL 1
0, //139

#BARLINEON
#MEASURE 7/8
1200000,
2020000,
1200222,
#SCROLL 2
4, //143

#SCROLL 1.16
#GOGOSTART
3
#GOGOEND
#SCROLL 1
2
#SCROLL 1.16
#GOGOSTART
3
#GOGOEND
#SCROLL 1
2
#SCROLL 1.16
#GOGOSTART
3
#GOGOEND
#SCROLL 1
2
#SCROLL 1.16
#GOGOSTART 
3,

3
#GOGOEND
#SCROLL 1
2
#SCROLL 1.16
#GOGOSTART
3
#GOGOEND
#SCROLL 1
2
#SCROLL 1.16
#GOGOSTART
3
#GOGOEND
#SCROLL 1
2
#SCROLL 1.16
#GOGOSTART 
3, //145

3
#GOGOEND
#SCROLL 1
2
#SCROLL 1.16
#GOGOSTART
3
#GOGOEND
#SCROLL 1
2
#SCROLL 1.16
#GOGOSTART
3
#GOGOEND
#SCROLL 1
2
#SCROLL 1.16
#GOGOSTART 
3,

3
#GOGOEND
#SCROLL 1
2
#SCROLL 1.16
#GOGOSTART
3
#GOGOEND
#SCROLL 1
2
#SCROLL 1.16
#GOGOSTART
3
#GOGOEND
#SCROLL 1
2
#SCROLL 1.16
#GOGOSTART 
3, //147

3
#GOGOEND
#SCROLL 1
2
#SCROLL 1.16
#GOGOSTART
3
#GOGOEND
#SCROLL 1
2
#SCROLL 1.16
#GOGOSTART
3
#GOGOEND
#SCROLL 1
2
#SCROLL 1.16
#GOGOSTART 
3,

3
#GOGOEND
#SCROLL 1
2
#SCROLL 1.16
#GOGOSTART
3
#GOGOEND
#SCROLL 1
2
#SCROLL 1.16
#GOGOSTART
3
#GOGOEND
#SCROLL 1
2
#SCROLL 1.16
#GOGOSTART 
3, //149

3
#GOGOEND
#SCROLL 1
2
#SCROLL 1.16
#GOGOSTART
3
#GOGOEND
#SCROLL 1
2
#SCROLL 1.16
#GOGOSTART
3
#GOGOEND
#SCROLL 1
2
#SCROLL 1.16
#GOGOSTART 
3,
3, //151
#GOGOEND

#SCROLL 0.5
22202220222020,
22202220222020,
22202220222020,
22202220222020,

#SCROLL 1
10222022202220,
#SCROLL 1.5
10222022202220,
#MEASURE 7/4
#SCROLL 2
100100100100100100100100500000000000000008000000000000000000000000000000000000000000, //158

#MEASURE 7/8
#SCROLL 1
#GOGOSTART
12201220122010,
12201220122010,
11222211112022,
10010010003000,

12201220122010,
12201220122010,
11110222201122,
#MEASURE 3/4
000100000100000700000000000000000008,
#MEASURE 1/32
0,
0,
0,
0, //170
#GOGOEND

#MEASURE 7/4
#BPMCHANGE 310
#SCROLL 0.5
10010111202220,
12212210102000,
20220011102002,
10010030000000,

10222010210100,
10222010221010,
22020220202222,
10010100100200, //178
#END


COURSE:Hard
LEVEL:8
BALLOON:
SCOREINIT:2410
SCOREDIFF:0

#START
#MEASURE 1/64
#SCROLL 9
0,
#SCROLL 8
0,
#SCROLL 7
0,
#SCROLL 6
0,
#SCROLL 5
0,
#SCROLL 4
0,
#SCROLL 3
0,
#SCROLL 2
0, //8

#MEASURE 4/4
#SCROLL 1
1010202002222000,
1010202002222000,
1010202002022000,
1010101002222000,

1010202202222000,
1010202202222000,
1010222202022000,
1101101101102200,

0,
#MEASURE 3/4
122210, //18

#MEASURE 7/8
10001110100010,
1011110,
10001110100010,
2221022,

10001011100010,
1011120,
10001011100010,
1, //26

#BARLINEOFF
#MEASURE 1/32
2,
#BARLINEON
#MEASURE 1/16
#SCROLL 0.6
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0, //40

0
#SCROLL 1
2,
#SCROLL 0.6
0,
0,
0,
0
#SCROLL 1
2,
#SCROLL 0.6
0,
0,
0,
0
#SCROLL 1
2,
#SCROLL 0.6
0,
0,
0,
0,
0, //54

0
#SCROLL 1
2,
#SCROLL 0.6
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0, //68

0
#SCROLL 1
2,
#SCROLL 0.6
0,
0,
0,
0
#SCROLL 1
2,
#SCROLL 0.6
0,
0,
0,
0
#SCROLL 1
2,
#SCROLL 0.6
0,
0,
0,
0,
0,
#MEASURE 1/32
0, //83

#MEASURE 7/8
#SCROLL 1
10001022200010,
10001110102000,
10101010111111,
0, //87

#MEASURE 7/4
#BPMCHANGE 310
#SCROLL 0.5
10010010200000,
10010010002000,
10010010200000,
3,

10010010200000,
10010010002000,
#GOGOSTART
10010010100000,
10010010004000, //95
#GOGOEND

10010010201000,
10010010201000,
10102220111011,
1,

3001110,
11001010001000,
10010010004000,
00020020222000, //103

10100222000000,
1021000,
1001100,
10110002022000,

#SCROLL 1.5
0000
#SCROLL 0.5
1010100101,
01000010000100,
3001040,
#MEASURE 5/4
0000011110, //111

#MEASURE 1/8
#SCROLL 1
0,
#SCROLL 2
0,
#SCROLL 3
0,
#SCROLL 4
0, //115

#SCROLL 0.5
3,
#SCROLL 4
0,
#SCROLL 3.86
0,
#SCROLL 3.71
0,
#SCROLL 3.57
0,
#SCROLL 3.43
0,
#SCROLL 3.29
0,
#SCROLL 3.14
0,
#SCROLL 3
0,
#SCROLL 2.86
0,
#SCROLL 2.71
0,
#SCROLL 2.57
0,
#SCROLL 2.43
0,
#SCROLL 2.29
0,
#SCROLL 2.14
0,
#SCROLL 2
0,
#SCROLL 1.86
0,
#SCROLL 1.71
0,
#SCROLL 1.57
0,
#SCROLL 1.43
0,
#SCROLL 1.29
0,
#SCROLL 1.14
0,
#MEASURE 3/4
#SCROLL 1
0,
#BARLINEOFF
#MEASURE 7/4
#BPMCHANGE 155
#SCROLL 1
0, //139

#BARLINEON
#MEASURE 7/8
2200000,
2020000,
2200222,
2, //143

#SCROLL 1.16
#GOGOSTART
3
#GOGOEND
#SCROLL 1
2
#SCROLL 1.16
#GOGOSTART
3
#GOGOEND
#SCROLL 1
2
#SCROLL 1.16
#GOGOSTART
3
#GOGOEND
#SCROLL 1
2
#SCROLL 1.16
#GOGOSTART 
3,

3
#GOGOEND
#SCROLL 1
2
#SCROLL 1.16
#GOGOSTART
3
#GOGOEND
#SCROLL 1
2
#SCROLL 1.16
#GOGOSTART
3
#GOGOEND
#SCROLL 1
2
#SCROLL 1.16
#GOGOSTART 
3, //145

3
#GOGOEND
#SCROLL 1
2
#SCROLL 1.16
#GOGOSTART
3
#GOGOEND
#SCROLL 1
2
#SCROLL 1.16
#GOGOSTART
3
#GOGOEND
#SCROLL 1
2
#SCROLL 1.16
#GOGOSTART 
3,

3
#GOGOEND
#SCROLL 1
2
#SCROLL 1.16
#GOGOSTART
3
#GOGOEND
#SCROLL 1
2
#SCROLL 1.16
#GOGOSTART
3
#GOGOEND
#SCROLL 1
2
#SCROLL 1.16
#GOGOSTART 
3, //147

3
#GOGOEND
#SCROLL 1
2
#SCROLL 1.16
#GOGOSTART
3
#GOGOEND
#SCROLL 1
2
#SCROLL 1.16
#GOGOSTART
3
#GOGOEND
#SCROLL 1
2
#SCROLL 1.16
#GOGOSTART 
3,

3
#GOGOEND
#SCROLL 1
2
#SCROLL 1.16
#GOGOSTART
3
#GOGOEND
#SCROLL 1
2
#SCROLL 1.16
#GOGOSTART
3
#GOGOEND
#SCROLL 1
2
#SCROLL 1.16
#GOGOSTART 
3, //149

3
#GOGOEND
#SCROLL 1
2
#SCROLL 1.16
#GOGOSTART
3
#GOGOEND
#SCROLL 1
2
#SCROLL 1.16
#GOGOSTART
3
#GOGOEND
#SCROLL 1
2
#SCROLL 1.16
#GOGOSTART 
3,
3, //151
#GOGOEND

#SCROLL 0.5
2020202,
2020202,
2020202,
2020202,

#SCROLL 1
10111022201000,
#SCROLL 1.5
10111022201000,
#MEASURE 7/4
#SCROLL 2
500000000000000000000000000000000008000000000000000000000000000000000000000000000000, //158

#MEASURE 7/8
#SCROLL 1
#GOGOSTART
10201020111000,
10201020222000,
10201020111000,
30030030000000,

10201020111000,
10201020222000,
10100202001010,
#MEASURE 3/4
010101000000,
#MEASURE 1/32
0,
0,
0,
0, //170
#GOGOEND

#MEASURE 7/4
#BPMCHANGE 310
#SCROLL 0.5
10010010200000,
10010010102000,
2201100,
10010010000000,

10010010200000,
10010010102000,
10010200201000,
10000000000200, //178
#END


COURSE:Normal
LEVEL:7
BALLOON:
SCOREINIT:3570
SCOREDIFF:0

#START
#MEASURE 1/64
#SCROLL 9
0,
#SCROLL 8
0,
#SCROLL 7
0,
#SCROLL 6
0,
#SCROLL 5
0,
#SCROLL 4
0,
#SCROLL 3
0,
#SCROLL 2
0, //8

#MEASURE 4/4
#SCROLL 1
10111000,
20222000,
1000110010000000,
22201000,

1111,
2222,
1111,
1001001001001000,

0,
#MEASURE 3/4
3, //18

#MEASURE 7/8
1010101,
1010101,
1010101,
1010120,

1010101,
1010101,
1010101,
1, //26

#BARLINEOFF
#MEASURE 1/32
2,
#BARLINEON
#MEASURE 1/16
#SCROLL 0.6
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0, //40

0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0, //54

0
#SCROLL 1
2,
#SCROLL 0.6
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0, //68

0
#SCROLL 1
2,
#SCROLL 0.6
0,
0,
0,
0
#SCROLL 1
2,
#SCROLL 0.6
0,
0,
0,
0
#SCROLL 1
2,
#SCROLL 0.6
0,
0
#SCROLL 1
2,
#SCROLL 0.6
0,
0,
0,
#MEASURE 1/32
0, //83

#MEASURE 7/8
#SCROLL 1
1212100,
1212100,
1111111,
0, //87

#MEASURE 7/4
#BPMCHANGE 310
#SCROLL 0.5
1001200,
10000010000200,
1001200,
3,

1001200,
10000010000200,
#GOGOSTART
3,
0000020, //95
#GOGOEND

1212121,
2121212,
1212121,
1,

3000121,
2121212,
1,
22000000003000, //103

10000222000000,
10001100100000,
1000300,
3,

#SCROLL 1.5
00
#SCROLL 0.5
21200,
03000030000300,
0000040,
#MEASURE 5/4
2020010100, //111

#MEASURE 1/8
#SCROLL 1
0,
#SCROLL 2
0,
#SCROLL 3
0,
#SCROLL 4
0, //115

#SCROLL 0.5
3,
#SCROLL 4
0,
#SCROLL 3.86
0,
#SCROLL 3.71
0,
#SCROLL 3.57
0,
#SCROLL 3.43
0,
#SCROLL 3.29
0,
#SCROLL 3.14
0,
#SCROLL 3
0,
#SCROLL 2.86
0,
#SCROLL 2.71
0,
#SCROLL 2.57
0,
#SCROLL 2.43
0,
#SCROLL 2.29
0,
#SCROLL 2.14
0,
#SCROLL 2
0,
#SCROLL 1.86
0,
#SCROLL 1.71
0,
#SCROLL 1.57
0,
#SCROLL 1.43
0,
#SCROLL 1.29
0,
#SCROLL 1.14
0,
#MEASURE 3/4
#SCROLL 1
0,
#BARLINEOFF
#MEASURE 7/4
#BPMCHANGE 155
#SCROLL 1
0, //139

#BARLINEON
#MEASURE 7/8
1,
0,
1000222,
2, //143

#SCROLL 1.16
#GOGOSTART
3
#GOGOEND
0
#GOGOSTART
3
#GOGOEND
0
#GOGOSTART
3
#GOGOEND
0
#GOGOSTART 
3,

3
#GOGOEND
0
#GOGOSTART
3
#GOGOEND
0
#GOGOSTART
3
#GOGOEND
0
#GOGOSTART 
3, //145

3
#GOGOEND
0
#GOGOSTART
3
#GOGOEND
0
#GOGOSTART
3
#GOGOEND
0
#GOGOSTART 
3,

3
#GOGOEND
0
#GOGOSTART
3
#GOGOEND
0
#GOGOSTART
3
#GOGOEND
0
#GOGOSTART 
3, //147

4
#GOGOEND
0
#GOGOSTART
4
#GOGOEND
0
#GOGOSTART
4
#GOGOEND
0
#GOGOSTART 
4,

4
#GOGOEND
0
#GOGOSTART
4
#GOGOEND
0
#GOGOSTART
4
#GOGOEND
0
#GOGOSTART 
4, //149

4
#GOGOEND
0
#GOGOSTART
4
#GOGOEND
0
#GOGOSTART
4
#GOGOEND
0
#GOGOSTART 
4,
3, //151
#GOGOEND

#SCROLL 0.5
2020200,
2020200,
2020200,
2020200,

#SCROLL 1
1012200,
#SCROLL 1.5
1012200,
#MEASURE 7/4
#SCROLL 2
500000000000000000000000000000000008000000000000000000000000000000000000000000000000, //158

#MEASURE 7/8
#SCROLL 1
#GOGOSTART
1210110,
1210110,
1210110,
3,

1210110,
1210110,
10100202001010,
#MEASURE 3/4
020202000000,
#MEASURE 1/32
0,
0,
0,
0, //170
#GOGOEND

#MEASURE 7/4
#BPMCHANGE 310
#SCROLL 0.5
10010010100000,
10010010100000,
2201100,
3,

10010010100000,
10010010100000,
10000100001000,
20000000000200, //178
#END


COURSE:Easy
LEVEL:5
BALLOON:
SCOREINIT:5000
SCOREDIFF:0

#START
#MEASURE 1/64
#SCROLL 9
0,
#SCROLL 8
0,
#SCROLL 7
0,
#SCROLL 6
0,
#SCROLL 5
0,
#SCROLL 4
0,
#SCROLL 3
0,
#SCROLL 2
0, //8

#MEASURE 4/4
#SCROLL 1
11,
22,
11,
2011,

1110,
2220,
1110,
3,

0,
#MEASURE 3/4
3, //18

#MEASURE 7/8
1010100,
1010100,
1010100,
2020200,

1010100,
1010100,
1010100,
1, //26

#BARLINEOFF
#MEASURE 1/32
2,
#BARLINEON
#MEASURE 1/16
#SCROLL 0.6
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0, //40

0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0, //54

0
#SCROLL 1
2,
#SCROLL 0.6
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0, //68

0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
#MEASURE 1/32
0, //83

#MEASURE 7/8
#SCROLL 1
1010100,
1010100,
1010111,
0, //87

#MEASURE 7/4
#BPMCHANGE 310
#SCROLL 0.5
1001100,
1001010,
1001100,
3,

2002200,
2002020,
#GOGOSTART
3,
0, //95
#GOGOEND

1010101,
0101011,
1010101,
1,

3000101,
0101011,
1,
0000020, //103

2,
1010100,
1000300,
3,

#SCROLL 1.5
00
#SCROLL 0.5
11100,
03000030000300,
0000040,
#MEASURE 5/4
0, //111

#MEASURE 1/8
#SCROLL 1
0,
#SCROLL 2
0,
#SCROLL 3
0,
#SCROLL 4
0, //115

#SCROLL 0.5
3,
#SCROLL 4
0,
#SCROLL 3.86
0,
#SCROLL 3.71
0,
#SCROLL 3.57
0,
#SCROLL 3.43
0,
#SCROLL 3.29
0,
#SCROLL 3.14
0,
#SCROLL 3
0,
#SCROLL 2.86
0,
#SCROLL 2.71
0,
#SCROLL 2.57
0,
#SCROLL 2.43
0,
#SCROLL 2.29
0,
#SCROLL 2.14
0,
#SCROLL 2
0,
#SCROLL 1.86
0,
#SCROLL 1.71
0,
#SCROLL 1.57
0,
#SCROLL 1.43
0,
#SCROLL 1.29
0,
#SCROLL 1.14
0,
#MEASURE 3/4
#SCROLL 1
0,
#BARLINEOFF
#MEASURE 7/4
#BPMCHANGE 155
#SCROLL 1
0, //139

#BARLINEON
#MEASURE 7/8
1,
0,
1000202,
2, //143

#SCROLL 1.16
#GOGOSTART
3
#GOGOEND
0
#GOGOSTART
3
#GOGOEND
0
#GOGOSTART
3
#GOGOEND
0
#GOGOSTART 
0,

3
#GOGOEND
0
#GOGOSTART
3
#GOGOEND
0
#GOGOSTART
3
#GOGOEND
0
#GOGOSTART 
0, //145

3
#GOGOEND
0
#GOGOSTART
3
#GOGOEND
0
#GOGOSTART
3
#GOGOEND
0
#GOGOSTART 
0,

3
#GOGOEND
0
#GOGOSTART
3
#GOGOEND
0
#GOGOSTART
3
#GOGOEND
0
#GOGOSTART 
0, //147

4
#GOGOEND
0
#GOGOSTART
4
#GOGOEND
0
#GOGOSTART
4
#GOGOEND
0
#GOGOSTART 
0,

4
#GOGOEND
0
#GOGOSTART
4
#GOGOEND
0
#GOGOSTART
4
#GOGOEND
0
#GOGOSTART 
0, //149

4
#GOGOEND
0
#GOGOSTART
4
#GOGOEND
0
#GOGOSTART
4
#GOGOEND
0
#GOGOSTART 
0,
3, //151
#GOGOEND

#SCROLL 0.5
2020200,
2020200,
2020200,
2020200,

#SCROLL 1
1010100,
#SCROLL 1.5
2020200,
#MEASURE 7/4
#SCROLL 2
500000000000000000000000000000000008000000000000000000000000000000000000000000000000, //158

#MEASURE 7/8
#SCROLL 1
#GOGOSTART
1110100,
1010100,
2220200,
10010010000000,

1110100,
1010100,
10000200001000,
#MEASURE 3/4
1,
#MEASURE 1/32
0,
0,
0,
0, //170
#GOGOEND

#MEASURE 7/4
#BPMCHANGE 310
#SCROLL 0.5
1001100,
1001010,
2201100,
3,

1001100,
1001010,
10000100001000,
2, //178
#END