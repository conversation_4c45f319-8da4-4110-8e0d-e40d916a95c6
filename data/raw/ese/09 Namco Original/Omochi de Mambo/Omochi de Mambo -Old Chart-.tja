//TJADB Project
TITLE:Omochi de Mambo -Old Chart-
TITLEJA:おもちでマンボ
SUBTITLE:--<PERSON><PERSON><PERSON> feat. <PERSON><PERSON><PERSON> Kaneko
SUBTITLEJA:旧譜面
BPM:124
WAVE:Omochi de Mambo.ogg
OFFSET:-2.041
DEMOSTART:87.613

COURSE:Oni
LEVEL:3
BALLOON:
SCOREINIT:
SCOREDIFF:
SCOREMODE:0

#START
#MEASURE 5/8
1002002000,
1002002000,
1002002000,
3, //4

#MEASURE 4/4
1010200000010110,
22200000,
1010200000010110,
1001001000000000,

1010200000010110,
22200000,
1010200000010110,
1001003000000000, //12

1000100011011000,
1000100011011000,
1000100011011020,
500000000000008000000000100000000100000000000000,

1000100011011020,
1130,
1000100011020201,
0, //20

#MEASURE 5/8
11100,
11100,
11100,
1,

11200,
11200,
11200,
1, //28

11210,
11210,
11210,
11210,

1,
3,
3,
2, //36

#MEASURE 4/4
1010200000020210,
11200000,
1010200000020210,
1001001000000000,

1010200000020210,
11200000,
1010200000020210,
1001003000000000, //44

1000100011021000,
1000100011021000,
1000100011021000,
500000000008000000100100100000000100000000000000,

1000100011021000,
1130,
1000100011020201,
0, //52

#MEASURE 5/8
11100,
11100,
11100,
1,

11220,
11220,
11220,
10011, //60

12211,
12211,
12211,
12211,

1,
3,
3,
2, //68

#MEASURE 4/4
1001110100010110,
22200000,
1001110100010110,
1001001000004000,

1001110100010110,
22200000,
1001110100010110,
1001001000002010,

1001001000002010,
1001003000002000,
0, //79
#END


COURSE:Hard
LEVEL:4
BALLOON:
SCOREINIT:710
SCOREDIFF:170
SCOREMODE:1

#START
#MEASURE 5/8
1002002000,
1002002000,
1002002000,
3, //4

#MEASURE 4/4
1010200000010110,
22200000,
1010200000010110,
1001001000000000,

1010200000010110,
22200000,
1010200000010110,
1001003000000000, //12

1000100011011000,
1000100011011000,
1000100011011020,
500000000000008000000000100000000100000000000000,

1000100011011020,
1130,
1000100011020201,
0, //20

#MEASURE 5/8
11100,
11100,
11100,
1,

11200,
11200,
11200,
1, //28

11210,
11210,
11210,
11210,

1,
3,
3,
2, //36

#MEASURE 4/4
1010200000020210,
11200000,
1010200000020210,
1001001000000000,

1010200000020210,
11200000,
1010200000020210,
1001003000000000, //44

1000100011021000,
1000100011021000,
1000100011021000,
500000000008000000100100100000000100000000000000,

1000100011021000,
1130,
1000100011020201,
0, //52

#MEASURE 5/8
11100,
11100,
11100,
1,

11220,
11220,
11220,
10011, //60

12211,
12211,
12211,
12211,

1,
3,
3,
2, //68

#MEASURE 4/4
1001110100010110,
22200000,
1001110100010110,
1001001000004000,

1001110100010110,
22200000,
1001110100010110,
1001001000002010,

1001001000002010,
1001003000002000,
0, //79
#END


COURSE:Normal
LEVEL:3
BALLOON:
SCOREINIT:840
SCOREDIFF:220
SCOREMODE:1

#START
#MEASURE 5/8
1,
1,
1,
3, //4

#MEASURE 4/4
11100000,
22200000,
11100000,
1001001000000000,

11100000,
22200000,
11100000,
1001003000000000, //12

1000100010010000,
1000100010010000,
1000100010010000,
1000100010010000,

1000100010010000,
1130,
1000100010000003,
0001, //20

#MEASURE 5/8
10010,
10010,
10010,
10010,

20010,
20010,
20010,
20010, //28

10010,
20010,
10010,
20010,

1,
1,
3,
2, //36

#MEASURE 4/4
11100020,
11100000,
11100020,
1001001000000000,

11100020,
11100000,
11100020,
1001003000000000, //44

1000100011010000,
1000100011010000,
1000100011010000,
1110,

1000100011010000,
1130,
1000100010000003,
00000011, //52

#MEASURE 5/8
10011,
10011,
10011,
10011,

10022,
20011,
10022,
20011, //60

10022,
20011,
10022,
20011,

1,
1,
3,
2, //68

#MEASURE 4/4
11100010,
22200000,
11100020,
1001001000000000,

11100010,
22200000,
11100020,
1001001000000000,

1001001000000000,
1001003000002000,
0, //79
#END