//TJADB Project
TITLE:<PERSON><PERSON> ～Blooming～
TITLEJA:綻放 ～Blooming～
SUBTITLE:--<PERSON><PERSON> Found Music Workshop
BPM:172
WAVE:<PERSON>hou Blooming.ogg
OFFSET:-2.916
DEMOSTART:14.071

COURSE:Edit
LEVEL:8
BALLOON:8,24,8,21
SCOREINIT:370
SCOREDIFF:100


#START


10102001,
01102000,
10102001,
01102012,
10102001,
01102000,
#BPMCHANGE 171.23
1121,
#BPMCHANGE 172
21221222,//8

#GOGOSTART
3030404022111000,
3030404022112000,
3030404022111020,
1011201022112020,
3030404022111000,
3030404022112000,
3030404022111010,
2211102211102010,//16

#GOGOEND
11210121,
1110201000102010,
1011201000102010,
5008,  //20
11210121,
1110201000102010,
1011201010112020,
3344,
100000100000200000100000100000000000500000000008,
0122,
100000100000200000100000100000000000500000000008,
0000100020101120,//28
1000403000201122,
1000403000201122,
1010201110102012,
1010201120112210,
1010201102112011,
1010201102112012,
1010201102112011,
1010201102112010,//36
2210102210102211,
1022111022111120,
30303303,
03443400,//40

#GOGOSTART
3030404022111000,
3030404022112000,
3030404022111020,
1011201022112020,
3030404022111000,
3030404022112000,
3030404022111010,
2211102211102000,//48
3030404022221000,
3030404011112000,
3030404022221020,
1011201011112020,
3000101020001011,
1020101020001011,
12012010,
70000800,
3000101020001011,
12112022,//58

#GOGOEND
7,
,
8,
,
,
,
,
,
10010050,
08,//68
1125,
0081,
1021,
1021,
1111,
30303333,//74

#GOGOSTART
3030404022111000,
3030404022112000,
3030404022111020,
1011202022112020,
3030404022221000,
3030404011112000,
3030404022221020,
1011202011112020,//82
3000101020001011,
1020101020001011,
12012010,
70000800,
3000101020001011,
12112022,
7,
0,
8,

#GOGOEND


#END


COURSE:Oni
LEVEL:7
BALLOON:8,8,24,8,24
SCOREINIT:630
SCOREDIFF:130


#START


2,
22,
2,
22,
2,

22,
#BPMCHANGE 171.23
2,
#BPMCHANGE 172
50000800,
#GOGOSTART
11221210,
11221220,  //10

11221212,
12212112,
11221210,
11221220,
11221212,  //15

11221022,
#GOGOEND
10120120,
11211020,
10201121,
7008,  //20

10120120,
11211020,
10201121,
3344,
11012000,  //25

1121,
11012010,
10102122,
30030030,
30030030,  //30

11111111,
11113030,
11221121,
11221212,
11221121,  //35

11221212,
1120201220201120,
2012202011202020,
30303303,
03343400,  //40

#GOGOSTART
31221210,
11221220,
11221212,
12212112,
11221210,  //45

11221220,
11221212,
11221022,
1010202011201000,
1010202011202000,  //50

1010202011201020,
1020201022202010,
10102012,
10102012,
10012012,  //55

7008,
10102012,
10102122,
#GOGOEND
7,
0,  //60

8,
0,
0,
0,
0,  //65

0022,
20012000,
0012,
20012000,
0012,  //70

20012000,
0022,
1111,
30303333,
#GOGOSTART
3010202011201000,  //75

1010202011202000,
1010202011201020,
12212112,
1010202011201000,
1010202011202000,  //80

1010202011201020,
11221222,
10102012,
10102012,
10012012,  //85

7008,
10102012,
3344,
7,
0,
8,

#GOGOEND


#END


COURSE:Hard
LEVEL:6
BALLOON:8,8,20,8,15
SCOREINIT:610
SCOREDIFF:160


#START


2,
22,
2,
22,
2,  //5

22,
2,
50000800,
#GOGOSTART
11221010,
11221020,  //10

11221010,
11102220,
11221010,
11221020,
11221010,  //15

11101020,
#GOGOEND
10120120,
11201020,
10201120,
70000800,  //20

10120120,
11201020,
10201120,
3344,
11012000,  //25

1121,
11012010,
1122,
30030030,
30030030,  //30

11101110,
11103030,
11201120,
11202220,
11201120,  //35

11202220,
1110001110001110,
0011100011100000,
30303303,
03343400,  //40

#GOGOSTART
31221010,
11221020,
11221010,
11102220,
11221010,  //45

11221020,
11221010,
11101020,
11221010,
11221020,  //50

11221010,
11102220,
10102012,
1120,
10012012,  //55

70000800,
10102012,
1122,
#GOGOEND
7,
0,  //60

8,
,
,
,
,  //65

0022,
20012000,
0012,
20012000,
0012,  //70

20012000,
0022,
1111,
3333,
#GOGOSTART
31221010,  //75

11221020,
11221010,
11201120,
11221010,
11221020,  //80

11221010,
11201120,
10102012,
1120,
10012012,  //85

70000800,
1121,
3344,
9,
09,  //90

8,
#GOGOEND
0,
#END

COURSE:Normal
LEVEL:5
SCOREINIT:770
SCOREDIFF:223
BALLOON:6,4,8,6,12,6,12

#START
#MEASURE 4/4

1,
,
1,
1,
1,  //5

,
1,
50000800,
#GOGOSTART
30101110,
10101110,  //10

10101110,
50000800,
30101110,
10101110,
10101110,  //15

7008,
#GOGOEND
,
1120,
10101110,
50000800,  //20

1012,
1120,
10102220,
7008,
10011000,  //25

1121,
10011000,
1121,
30030000,
30030000,  //30

7,
0008,
1121,
1120,
1121,  //35

1120,
3003,
0033,
3330,
60000800,  //40

#GOGOSTART
30101110,
10102220,
10101110,
50000800,
30101110,  //45

10102220,
10101110,
7008,
30101110,
10102220,  //50

10101110,
50000800,
1120,
1110,
1120,  //55

50000800,
1120,
11,
#GOGOEND
9,
09,  //60

8,
,
,
,
,  //65

,
2,
,
2,
,  //70

2,
2,
1111,
50000800,
#GOGOSTART
30101110,  //75

10102220,
10101110,
50000800,
30101110,
10102220,  //80

10101110,
7008,
3120,
1110,
12,  //85

50000800,
1120,
3333,
9,
09,  //90

8,
#GOGOEND
0,
#END

COURSE:Easy
LEVEL:3
SCOREINIT:605
SCOREDIFF:205
BALLOON:4,4,8,4,12,4,12

#START
#MEASURE 4/4

1,
,
1,
1,
1,  //5

,
1,
50000800,
#GOGOSTART
3011,
1011,  //10

1011,
50000800,
3011,
1011,
1011,  //15

7008,
#GOGOEND
,
11,
1110,
50000800,  //20

1,
11,
1110,
7008,
1,  //25

0111,
1,
0111,
3,
3,  //30

7,
0008,
1110,
2220,
1110,  //35

2220,
3003,
03,
3330,
60000800,  //40

#GOGOSTART
3011,
1011,
1011,
50000800,
3011,  //45

1011,
1011,
7008,
3011,
1011,  //50

1011,
50000800,
12,
1110,
12,  //55

50000800,
12,
11,
#GOGOEND
9,
09,  //60

8,
,
,
,
,  //65

,
2,
,
2,
,  //70

2,
2,
11,
50000800,
#GOGOSTART
3011,  //75

1011,
1011,
50000800,
3011,
1011,  //80

1011,
7008,
32,
1110,
12,  //85

50000800,
12,
3033,
9,
09,  //90

8,
#GOGOEND
0,
#END
