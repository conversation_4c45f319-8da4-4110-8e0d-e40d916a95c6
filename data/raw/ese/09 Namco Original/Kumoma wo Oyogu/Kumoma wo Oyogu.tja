//TJADB Project
TITLE:<PERSON><PERSON><PERSON> wo <PERSON>yogu
TITLEJA:雲間を游ぐ
SUBTITLE:--<PERSON><PERSON><PERSON><PERSON><PERSON>
SUBTITLEJA:くるぶっこちゃん
BPM:128
WAVE:<PERSON><PERSON><PERSON> wo Oyogu.ogg
OFFSET:-1.016
DEMOSTART:71.318


COURSE:Oni
LEVEL:10
BALLOON:
SCOREINIT:1480
SCOREDIFF:0

#START
#MEASURE 5/8
#SCROLL 1.5
2012000000,
#MEASURE 7/8
20120020220020,
#MEASURE 5/8
20201020002222102020,
#MEASURE 4/4
200000100200000100202020200010002000102020202020, //4

#MEASURE 9/16
110201010,
#MEASURE 15/16
010210100210222,
#MEASURE 3/4
000100100100200000100000100200201010,
#MEASURE 7/8
20110101200122, //8

#MEASURE 13/16
200000200100100010002000
#BPMCHANGE 160
#SCROLL 1.2
100100200200100,
#MEASURE 6/4
#BPMCHANGE 128
#SCROLL 1.5
100000000000202020100100200000
#BPMCHANGE 256
#SCROLL 0.75
100100100100
#BPMCHANGE 128
#SCROLL 1.5
100000100100000300000300000000, //10

#MEASURE 15/16
122021020101201,
#MEASURE 4/4
1020210102110201,
#MEASURE 15/16
000000202020100020002000201010100210001200100, //13

#MEASURE 4/4
1022112212110212,
#MEASURE 15/16
102210221202011,
#MEASURE 4/4
0220110220110120,
100200200000202020100000202020102000000200000100, //17

1201201211210121,
1201201021120120,
102020200100200200100200000100000200000010002020,
#MEASURE 3/4
100201210210,
201000100201000100201000100201000100, //22

#MEASURE 8/4
#BPMCHANGE 256
#SCROLL 0.75
100100100000200000100000100000200000000000200000100000100100200000100000100010002000100000200000,
100100100000200000100000100000200000000000200000100000100100200000100000100010001000200000100000, //24

#MEASURE 5/8
#BPMCHANGE 128
#SCROLL 1.5
3001201010,
#MEASURE 3/4 
300000100000000100020000100000000100,
#MEASURE 5/8
3010001201,
#MEASURE 7/8
300000100020001000100, //28

#MEASURE 49/64
#BPMCHANGE 112
#SCROLL 1.714
100000000000000000000000000000000000100000000000200000000000000000000000100000000000000000000000100000000000000000000000000020000000000000000000000,
100000000000000000000000000000000000100000002000000000000000000000001000000000000000000000000000100000000000100000000000200000000000200000000000000,
#MEASURE 7/8
10012222221112,
10010020021020, //32

#MEASURE 105/64
#BPMCHANGE 224
#SCROLL 0.857
100010002000200010001000100020000000200000001000000020002000100010002000200010001000200020001000000020000,
#MEASURE 15/16
#BPMCHANGE 160
#SCROLL 1.2
200000000000200000000000200000000010200100000, //34

#MEASURE 7/8
#BPMCHANGE 128
#SCROLL 1.5
10021000102102,
10001001202010,
100020002000200100200000200220100200200200,
100020202020100200100200200200101010101010, //38

#MEASURE 4/4
20020202,
10020202,
12200020,
600000000000000000000000000008000000000000000000, //42

#GOGOSTART
100000100000200100000100000200101010200100100200,
100000100000200100000100000200101010200200100100,
#GOGOEND
000100202020100100000300000000300000100200200400,
000000400000202020101010200100000100200100200200, //46

#GOGOSTART
100000200000100200000100100200101010200100100200,
100000200000100200000100100200101010200200100200,
#GOGOEND
100200200000400000000200101010202020101010202020,
1122121221021202, //50

#MEASURE 8/4
#BPMCHANGE 256
#SCROLL 0.75
#GOGOSTART
10222010201022201022201020102010,
10201022201022201010222020102000,
100000000000100000000000200000200000000000200000100000200000200000000000500000000000000000000008,
002020102020102120102120, //54

100000100000200000200000100010002000200010001000200000100000000000200000100100000000200100000000,
10102010111120102010002010002010,
11100011200011102010001120101011,
200000000000000000202020200200000200000000200000, //58
#GOGOEND

#MEASURE 5/8
#BPMCHANGE 128
#SCROLL 1.5
1001000000,
#MEASURE 7/8
20120020110010,
#MEASURE 5/8
2001002002,
#MEASURE 4/4
1201001001001010, //62

#MEASURE 9/16
100120000,
#MEASURE 15/16
100200101201222,
#MEASURE 3/4
1120,
#MEASURE 7/8
10010020020010, //66

0,
0,
#MEASURE 4/4
0, //69
#END


COURSE:Hard
LEVEL:7
BALLOON:21,14,50
SCOREINIT:2800
SCOREDIFF:0

#START
#MEASURE 5/8
#SCROLL 1.5
2002000000,
#MEASURE 7/8
20020020220020,
#MEASURE 5/8
2002000000,
#MEASURE 4/4
200000000200000000200000200020002000200000000000, //4

#MEASURE 9/16
110201010,
#MEASURE 15/16
010200100100222,
#MEASURE 3/4
011020101000,
#MEASURE 7/8
20110101200122, //8

#MEASURE 13/16
70000000
#BPMCHANGE 160
#SCROLL 1.2
00000,
#MEASURE 6/4
#BPMCHANGE 128
#SCROLL 1.5
0000000080
#BPMCHANGE 256
#SCROLL 0.75
0000
#BPMCHANGE 128
#SCROLL 1.5
3003030300, //10

#MEASURE 15/16
111010010101200,
#MEASURE 4/4
2010110101100207,
#MEASURE 15/16
000000000008000, //13

#MEASURE 4/4
1000100011020202,
#MEASURE 15/16
102010201102001,
#MEASURE 4/4
0220110220110220,
100000000000202000000000202000002000000200000100, //17

1000000011110220,
1100101020010020,
100000100000200000000200000100000200000010002020,
#MEASURE 3/4
2222,
500000000000000000000000000008000000, //22

#MEASURE 8/4
#BPMCHANGE 256
#SCROLL 0.75
1020110200101020,
1020110200101020, //24

#MEASURE 5/8
#BPMCHANGE 128
#SCROLL 1.5
30200,
#MEASURE 3/4 
300000000000000000020000000000000000,
#MEASURE 5/8
3000000200,
#MEASURE 7/8
300000000020001000100, //28

#MEASURE 49/64
#BPMCHANGE 112
#SCROLL 1.714
100000000000000000000000000000000000100000000000200000000000000000000000100000000000000000000000100000000000000000000000000020000000000000000000000,
100000000000000000000000000000000000100000002000000000000000000000001000000000000000000000000000100000000000000000000000200000000000200000000000000,
#MEASURE 7/8
10002222222022,
10010020020020, //32

#MEASURE 105/64
#BPMCHANGE 224
#SCROLL 0.857
500000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000008,
#MEASURE 15/16
#BPMCHANGE 160
#SCROLL 1.2
000020002000200, //34

#MEASURE 7/8
#BPMCHANGE 128
#SCROLL 1.5
1000010,
2000020,
10002010001022,
10002210202220, //38

#MEASURE 4/4
20020202,
10020202,
12200020,
600000000000000000000000000008000000000000000000, //42

#GOGOSTART
1010210102102010,
1010210102102010,
#GOGOEND
1020100300300004,
0040222020020202, //46

#GOGOSTART
1020110210201020,
1020110210201020,
#GOGOEND
1110400010001000,
500000000000000000000000000000000008000000000000, //50

#MEASURE 8/4
#BPMCHANGE 256
#SCROLL 0.75
#GOGOSTART
1220202202202020,
1202202200221020,
100000000000100000000000200000200000000000200000000000200000200000000000500000000000000000000008,
022122122122, //54

1101201021021020,
1101201021021020,
1101201011012010,
22, //58
#GOGOEND

#MEASURE 5/8
#BPMCHANGE 128
#SCROLL 1.5
1001000000,
#MEASURE 7/8
20020020000010,
#MEASURE 5/8
2001007000,
#MEASURE 4/4
0, //62

#MEASURE 9/16
0,
#MEASURE 15/16
000000000008000,
#MEASURE 3/4
1120,
#MEASURE 7/8
10010020020010, //66

0,
0,
#MEASURE 4/4
0, //69
#END


COURSE:Normal
LEVEL:6
BALLOON:13,11,12,40
SCOREINIT:4730
SCOREDIFF:0

#START
#MEASURE 5/8
#SCROLL 1.5
2002000000,
#MEASURE 7/8
20020000200000,
#MEASURE 5/8
2002000000,
#MEASURE 4/4
2002000020002000, //4

#MEASURE 9/16
100001010,
#MEASURE 15/16
00112,
#MEASURE 3/4
010020101000,
#MEASURE 7/8
20010001020020, //8

#MEASURE 13/16
70000000
#BPMCHANGE 160
#SCROLL 1.2
00000,
#MEASURE 6/4
#BPMCHANGE 128
#SCROLL 1.5
0000000080
#BPMCHANGE 256
#SCROLL 0.75
0000
#BPMCHANGE 128
#SCROLL 1.5
3003030300, //10

#MEASURE 15/16
100010010100200,
#MEASURE 4/4
1000100101000207,
#MEASURE 15/16
000000000008000, //13

#MEASURE 4/4
1000100020020000,
#MEASURE 15/16
100010001001000,
#MEASURE 4/4
0200100200100200,
1222, //17

1000000010010010,
1000100020010020,
0010200101020000,
#MEASURE 3/4
0112,
500000000000000000000000000008000000, //22

#MEASURE 8/4
#BPMCHANGE 256
#SCROLL 0.75
1000100200101000,
1000100200101000, //24

#MEASURE 5/8
#BPMCHANGE 128
#SCROLL 1.5
30200,
#MEASURE 3/4 
300000000000000000020000000000000000,
#MEASURE 5/8
3000000200,
#MEASURE 7/8
300000000020000000000, //28

#MEASURE 49/64
#BPMCHANGE 112
#SCROLL 1.714
1,
1,
#MEASURE 7/8
1020202,
10010020020020, //32

#MEASURE 105/64
#BPMCHANGE 224
#SCROLL 0.857
500000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000008,
#MEASURE 15/16
#BPMCHANGE 160
#SCROLL 1.2
000020002000200, //34

#MEASURE 7/8
#BPMCHANGE 128
#SCROLL 1.5
1000010,
2000020,
1021012,
1021020, //38

#MEASURE 4/4
2,
1,
1002,
600000000000000000000000000008000000000000000000, //42

#GOGOSTART
1000200100102000,
1000200100102000,
#GOGOEND
1000100300300004,
0040000020020000, //46

#GOGOSTART
10201120,
10201120,
#GOGOEND
1411,
500000000000000000000000000000000008000000000000, //50

#MEASURE 8/4
#BPMCHANGE 256
#SCROLL 0.75
#GOGOSTART
1020202200202020,
1002002200202020,
10202220,
7008, //54

1000101020020020,
1000101020020020,
1001001010010010,
12, //58
#GOGOEND

#MEASURE 5/8
#BPMCHANGE 128
#SCROLL 1.5
1001000000,
#MEASURE 7/8
20020020000000,
#MEASURE 5/8
2001007000,
#MEASURE 4/4
0, //62

#MEASURE 9/16
0,
#MEASURE 15/16
000000000008000,
#MEASURE 3/4
0110,
#MEASURE 7/8
10010010010010, //66

0,
0,
#MEASURE 4/4
0, //69
#END


COURSE:Easy
LEVEL:5
BALLOON:10,8,8,30
SCOREINIT:6840
SCOREDIFF:0

#START
#MEASURE 5/8
#SCROLL 1.5
2,
#MEASURE 7/8
20020000000000,
#MEASURE 5/8
2,
#MEASURE 4/4
2002000000002000, //4

#MEASURE 9/16
100000010,
#MEASURE 15/16
00102,
#MEASURE 3/4
010000001000,
#MEASURE 7/8
20000000020020, //8

#MEASURE 13/16
70000000
#BPMCHANGE 160
#SCROLL 1.2
00000,
#MEASURE 6/4
#BPMCHANGE 128
#SCROLL 1.5
0000000080
#BPMCHANGE 256
#SCROLL 0.75
0000
#BPMCHANGE 128
#SCROLL 1.5
3003030300, //10

#MEASURE 15/16
100000010000200,
#MEASURE 4/4
0000100001000207,
#MEASURE 15/16
000000000008000, //13

#MEASURE 4/4
1120,
#MEASURE 15/16
100010001000000,
#MEASURE 4/4
0100100100100100,
1022, //17

2,
1120,
1000100000020000,
#MEASURE 3/4
0112,
500000000000000000000008000000000000, //22

#MEASURE 8/4
#BPMCHANGE 256
#SCROLL 0.75
1110,
1110, //24

#MEASURE 5/8
#BPMCHANGE 128
#SCROLL 1.5
3,
#MEASURE 3/4 
3,
#MEASURE 5/8
3,
#MEASURE 7/8
3, //28

#MEASURE 49/64
#BPMCHANGE 112
#SCROLL 1.714
2,
2,
#MEASURE 7/8
2020200,
10010020020020, //32

#MEASURE 105/64
#BPMCHANGE 224
#SCROLL 0.857
500000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000008,
#MEASURE 15/16
#BPMCHANGE 160
#SCROLL 1.2
0, //34

#MEASURE 7/8
#BPMCHANGE 128
#SCROLL 1.5
1000020,
2000020,
1001010,
1001000, //38

#MEASURE 4/4
2,
1,
1,
600000000000000000000000000008000000000000000000, //42

#GOGOSTART
1000200100100000,
1000200100100000,
#GOGOEND
1000000300300004,
0040000020000000, //46

#GOGOSTART
1212,
1212,
#GOGOEND
1410,
500000000000000000000000000000000008000000000000, //50

#MEASURE 8/4
#BPMCHANGE 256
#SCROLL 0.75
#GOGOSTART
22220220,
2002002000202000,
1222,
7008, //54

1000100020020000,
1000100020020000,
1111,
12, //58
#GOGOEND

#MEASURE 5/8
#BPMCHANGE 128
#SCROLL 1.5
1001000000,
#MEASURE 7/8
2002000,
#MEASURE 5/8
20070,
#MEASURE 4/4
0, //62

#MEASURE 9/16
0,
#MEASURE 15/16
000000000008000,
#MEASURE 3/4
0100,
#MEASURE 7/8
1001001, //66

0,
0,
#MEASURE 4/4
0, //69
#END