//TJADB Project
TITLE:<PERSON><PERSON>u-genjitsu no Telescope
TITLEJA:仮想現実のテレスコープ
SUBTITLE:--deli.
SUBTITLEJA:deli.
BPM:250
WAVE:<PERSON><PERSON>u-genjitsu no Telescope.ogg
OFFSET:-0.107
DEMOSTART:77.387


COURSE:Edit
LEVEL:10
BALLOON:5,5,11,6
SCOREINIT:850
SCOREDIFF:0

#START
#MEASURE 6/4
#SCROLL 0.5
2,
2,
2,
0, //4

2,
2,
2,
0, //8

#MEASURE 5/4
#SCROLL 1
#GOGOSTART 
10111010101020102010,
#MEASURE 6/4
301110101010201022101110,
301411401410,
300010401020112010101010, //12

300010201000111010201000,
100010201000111010201020,
#GOGOEND
100010201000111010201020,
101210323232, //16

#MEASURE 5/4
11101020100030004000,
11101020100030004000,
#MEASURE 6/4
111010201000300040004000,
302220111022101010101111, //20

#MEASURE 4/4
10110120,
1110201000102020,
1111202011112020,
1111222210002010, //24

2011102210102000,
1011201000112020,
1011101020111010,
4011104011102222, //28

1000112000003000,
#MEASURE 3/4
102220102020,
#MEASURE 4/4
1000112000007000,
#MEASURE 3/4
008011112020, //32

#MEASURE 4/4
10120030,
#MEASURE 3/4
102220202020,
#MEASURE 4/4
1000221000227000,
08222220, //36

#MEASURE 6/4
#SCROLL 0.5
100022202000101020202020,
100022202000122020202010,
100011201020100022100022,
101010102010200022202020,
100011102010200022202020,
100011102010200022212000, //42

#MEASURE 3/4
#SCROLL 0.6
322112,
#SCROLL 0.7
322112,
#SCROLL 0.8
300200200100202010,
#SCROLL 0.9
101010101022101010, //46

#MEASURE 6/4
#SCROLL 1
#GOGOSTART
301102101102102201,
102202101122102200,
111102101101102200,
100020200010100010200200200100200200, //50

301102101102102202,
102201101202101200,
500000000000000000000000000000000000000000000008000000002000200000000000, 
500000000000000000000000000000000000000000000000000000000008000000000000, //54

300000200300000200300010202020101010,
111401101202102201,
111102101101102201,
101020200010100010200020100200100100, //58
#GOGOEND

700000000000000800000011,
211211414141,
111010201022102010201020,
112010222020101020111000, //62

#GOGOSTART
300000000000200010001000100000100000200000100000100000100100200020002000,
100000100000200000100100100100200000200000100000100010001000200000100000,
100020100010201120102011,
201010101020101010200100201010201010, //66

300000200100000100200100101010202020,
100100100000200000100000100010001000200010001000200010001000200010001000,
400000000000100000200000000000100100200000000000100000200000100010001000,
202110202110211121112110, //70

111211211111211211,
200000100100200200100100200020001000100000200200100010002000200000100000,
100000000000200000200000100010002000200000100000100000200200100100200200,
700000000000800000221010221010221010, //74

221011221011221011221011,
212011212011212011212011,
212111212111212111212110,
200100100200100100101010202020101020, //78
#GOGOEND

4,
22,
2,
22, //82

#MEASURE 4/4
30101110,
30111010,
3000101010101011,
21121120, //86

#GOGOSTART
2111102010102111,
1020101022121010,
2111102010102111,
1020101122121010, //90

2111102010102111,
1020101022121010,
4010101210104010,
100000100200100000100000100020002000100020002000, //94

2111102010102111,
1020101022121010,
2111102010102111,
1020101022121010, //98

2111102010102111,
1020101022121010,
4010101210104010,
100000100200100000100000500000000000000008000000, //102

222111222111,
221122112211,
2111211121112111,
200010001000200200200200100010001000200000000000, //106

100010001000200010001000100100100100200010001000,
100010001000200010001000100100100100200010001000,
100010001000200010001000100100100100200010001000,
211211222222, //110

111222111222,
122122122122,
500000000000000000000000000000000008000000000000,
500000000000000000000000000008000000200000000000, //114

211111112112,
112112112212,
1122112211221122,
122112211121, //118
#GOGOEND

30200200,
20020020,
02200200,
20020020, //122

02200200,
20020020,
0,
0,
0, //127
#END


COURSE:Oni
LEVEL:9
BALLOON:32,6,7,8,9,6,5,6,36,3,72
SCOREINIT:1320
SCOREDIFF:0

#START
#MEASURE 6/4
#SCROLL 0.5
1,
11,
22,
0, //4

2,
22,
1,
0, //8

#MEASURE 5/4
#SCROLL 1
#GOGOSTART 
10101110100010201020,
#MEASURE 6/4
101011101000201010201020,
102122102112,
111211211222, //12

7,
000008,
#GOGOEND
000012,
111010102000100010001000, //16

#MEASURE 5/4
7000083040,
7000083040,
#MEASURE 6/4
700008304040,
100000100000200011102000, //20

#MEASURE 4/4
10201120,
1110201010102000,
1010201110102020,
1010202010001110, //24

2000111000002000,
0202,
1110101020201010,
40140140, //28

10020030,
#MEASURE 3/4
021122,
#MEASURE 4/4
10020030,
#MEASURE 3/4
221112, //32

#MEASURE 4/4
10020030,
#MEASURE 3/4
021222,
#MEASURE 4/4
10210270,
08, //36

#MEASURE 6/4
#SCROLL 0.5
122122,
102020102220,
102022102202,
102022102020,
101022201020,
101022201110, //42

#MEASURE 3/4
#SCROLL 0.6
302110,
#SCROLL 0.7
302110,
#SCROLL 0.8
302110,
#SCROLL 0.9
112110, //46

#MEASURE 6/4
#SCROLL 1
#GOGOSTART
100100201101100200,
101100201101100200,
100100201101100200,
101100200101101101, //50

100100201101101200,
100100200101101201,
100100201100100200,
102201001101102000, //54

300300301020,
101100200101101200,
100100200101100200,
102201100200100100, //58
#GOGOEND

1,
110110303030,
500000000000000000000000000000000000000000000000000000000000000008000000,
121222112110, //62

#GOGOSTART
100000101010200200100000101010202020,
100200112200100200,
102010121020,
110110414140, //66

300000000000700000000000000000000000000800000000100010001000100000000000,
300000000000000070000000000000000000000800000000100010001000100000000000,
102101201210,
110110404040, //70

211211211211211200,
221221221200100200,
100100111100100111, 
700000800221221200, //74

110110110110,
120120120120,
330330330330,
111211200111111200, //78
#GOGOEND

7,
0,
8,
22, //82

#MEASURE 4/4
30101110,
30111010,
30101110,
30030030, //86

#GOGOSTART
1110201010001110,
22112211,
1110201010001110,
1010201011102010, //90

1110101020001110,
1020100011101020,
11011011,
000100100100102020202020, //94

1110201010001110,
11221121,
1110201010001110,
2020101011102010, //98

1110201010001110,
2010100011101020,
11011011,
0010100011102220, //102

222111222100,
101010202020100100200000,
21212127,
000000800000000000500000000008000000200000000000, //106

111200100111,
111100111211,
111200100111,
222222222100, //110

111222111222,
111222111222,
500000000000000000000000000000000008000000000000,
111111200100, //114

5,
000000000000000000000000000000000000000008000000,
11011011,
000100100000202020101010, //118
#GOGOEND

7,
0,
0,
0, //122

0,
0,
08,
0,
0, //127
#END


COURSE:Hard
LEVEL:7
BALLOON:24,5,5,5,16,6,6,6,25,52
SCOREINIT:2270
SCOREDIFF:0

#START
#MEASURE 6/4
#SCROLL 0.5
1,
11,
22,
0,

2,
22,
1,
0, //8

#MEASURE 5/4
#SCROLL 1
#GOGOSTART
1011101010,
#MEASURE 6/4
101110201010,
3333,
400400101110,

9,
09,
#GOGOEND
8,
101120101010,

#MEASURE 5/4
7000083040,
7000083040,
#MEASURE 6/4
700008304040,
100100201120, //20

#MEASURE 4/4
5,
000000000000000000000008000000000000000000000000,
10201011,
10201022,

20110010,
0102,
10101110,
30030030, //28

10020030,
#MEASURE 3/4
001111,
#MEASURE 4/4
10020030,
#MEASURE 3/4
001111,

#MEASURE 4/4
10020030,
#MEASURE 3/4
002222,
#MEASURE 4/4
30030030,
0,

#MEASURE 6/4
#SCROLL 0.5
122122,
102020102220,
122122,
102022202020,

102022201020,
102022201020,
#MEASURE 3/4
#SCROLL 0.6
32,
#SCROLL 0.7
32,
#SCROLL 0.8
32,
#SCROLL 0.9
122, //46

#MEASURE 6/4
#SCROLL 1
#GOGOSTART
121112,
101100000200100200,
101100000100200200,
101100000100100200,

112102,
112112,
500000000000000000000000000000000000000000000008000000000000200000000000,
500000000000000000000000000000000000000000000008000000000000000000000000,

300300301000,
112112,
112112,
100211, //58
#GOGOEND

1,
300300303030,
9,
980,

#GOGOSTART
101020111010,
101020111020,
102010111020,
110110404040,

700811,
700811,
101101101110,
78, //70

111201,
222102,
111201,
100222,

110110110110,
110110110110,
330330330330,
200200102020, //78
#GOGOEND

7,
0,
8,
22,

#MEASURE 4/4
3,
3,
3,
30030030, //86

#GOGOSTART
11101011,
1121,
11101011,
1121,

11101011,
1122,
11011011,
01112020, //94

11101011,
1221,
11101011,
1121,

11101011,
1121,
11011011,
01101020, //102

1111,
10101110,
10101011,
1112,

5,
000008000000000000000000100000000000200000000000,
1211,
100100111100, //110

100100111100,
100100111100,
500000000000000000000000000000000008000000000000,
500000000000000000000008000000000000100000000000,

5,
0,
0,
000000000000000000000008000000000000000000000000, //118
#GOGOEND

7,
0,
0,
0,

0,
0,
08,
0,
0, //127
#END


COURSE:Normal
LEVEL:5
BALLOON:17,4,12,5,5,4,19,40
SCOREINIT:4220
SCOREDIFF:0

#START
#MEASURE 6/4
#SCROLL 0.5
2,
22,
22,
0,

2,
22,
2,
0, //8

#MEASURE 5/4
#SCROLL 1
#GOGOSTART
10011,
#MEASURE 6/4
100121,
3333,
300300202020,

9,
09,
#GOGOEND
8,
000111, //16

#MEASURE 5/4
30034,
30034,
#MEASURE 6/4
300343,
100100101010,

#MEASURE 4/4
5,
000000000000000000000008000000000000000000000000,
22,
22,

2002,
0202,
2022,
70000800, //28

10020030,
#MEASURE 3/4
0,
#MEASURE 4/4
10020030,
#MEASURE 3/4
0,

#MEASURE 4/4
10020030,
#MEASURE 3/4
0,
#MEASURE 4/4
30030030,
0, //36

#MEASURE 6/4
#SCROLL 0.5
22,
22,
22,
22,

202200,
202200,
#MEASURE 3/4
#SCROLL 0.6
3,
#SCROLL 0.7
3,
#SCROLL 0.8
3,
#SCROLL 0.9
3, //46

#MEASURE 6/4
#SCROLL 1
#GOGOSTART
101101,
100201,
100101,
12,

100101,
101101,
5,
000000000000000000000000000000000000000000000008000000000000000000000000,

33,
101101,
101101,
12, //58
#GOGOEND

1,
300333,
9,
980, //62

#GOGOSTART
100101,
100202,
200101,
100333,

700811,
700811,
100101,
78, //70

111101,
111101,
111101,
100111,

1111,
1111,
3333,
200102, //78
#GOGOEND

7,
0,
8,
22,

#MEASURE 4/4
3,
3,
3,
30030030, //86

#GOGOSTART
1001,
0011,
1001,
1011,

1011,
00001005,
0,
000000000000000000000008000000000000000000000000, //94

1001,
0111,
1,
1111,

1001,
00101005,
0,
000000000000000000000008000000000000000000000000, //102

1110,
1110,
1110,
1011,

5,
000008000000000000000000000000000000000000000000,
1,
2220, //110

1110,
1110,
500000000000000000000000000000000008000000000000,
500000000000000000000008000000000000100000000000,

5,
0,
0,
000000000000000000000008000000000000000000000000, //118
#GOGOEND

7,
0,
0,
0,

0,
0,
08,
0,
0, //127
#END


COURSE:Easy
LEVEL:4
BALLOON:14,3,10,4,4,14,33
SCOREINIT:6350
SCOREDIFF:0

#START
#MEASURE 6/4
#SCROLL 0.5
2,
2,
2,
0,

2,
2,
2,
0, //8

#MEASURE 5/4
#SCROLL 1
#GOGOSTART
1,
#MEASURE 6/4
1,
33,
31,

9,
09,
#GOGOEND
8,
0, //16

#MEASURE 5/4
3,
3,
#MEASURE 6/4
4,
11, //20

#MEASURE 4/4
5,
000000000000000000000008000000000000000000000000,
11,
22,

2001,
0,
11,
70000800, //28

1003,
#MEASURE 3/4
0,
#MEASURE 4/4
1003,
#MEASURE 3/4
0,

#MEASURE 4/4
2004,
#MEASURE 3/4
0,
#MEASURE 4/4
30030030,
0, //36

#MEASURE 6/4
#SCROLL 0.5
2,
2,
2,
2,

22,
22,
#MEASURE 3/4
#SCROLL 0.6
3,
#SCROLL 0.7
3,
#SCROLL 0.8
3,
#SCROLL 0.9
3, //46

#MEASURE 6/4
#SCROLL 1
#GOGOSTART
100101,
12,
100101,
12,

100101,
101100,
5,
000000000000000000000000000000000000000000000008000000000000000000000000,

33,
101100,
101100,
12, //58
#GOGOEND

2,
33,
9,
980, //62

#GOGOSTART
100101,
12,
100101,
13,

78,
78,
11,
3, //70

100101,
12,
100101,
12,

1110,
1110,
3330,
22, //78
#GOGOEND

7,
0,
8,
22,

#MEASURE 4/4
3,
3,
3,
3, //86

#GOGOSTART
1001,
0011,
1001,
01,

1011,
00001005,
0,
000000000000000000000008000000000000000000000000, //94

1001,
0110,
1,
1010,

1001,
00001005,
0,
000000000000000000000008000000000000000000000000, //102

11,
11,
11,
01,

5,
000008000000000000000000000000000000000000000000,
1,
11, //110

1110,
1110,
3,
3001,

5,
0,
0,
000000000000000000000008000000000000000000000000, //118
#GOGOEND

7,
0,
0,
0,

0,
0,
08,
0,
0, //127
#END

