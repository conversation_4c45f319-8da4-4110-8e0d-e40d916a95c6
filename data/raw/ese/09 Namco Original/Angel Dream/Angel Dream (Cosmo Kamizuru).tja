//TJADB Project
TITLE:Angel Dream
TITLEJA:エンジェル ドリーム
SUBTITLE:--PROJECT IM@S vα-liv/Cosmo Kamizuru
SUBTITLEJA:PROJECT IM@S vα-liv 上水流宇宙
BPM:180
WAVE:<PERSON> Dream (Cosmo Kamizuru).ogg
OFFSET:-0.637
DEMOSTART:55.291

//shinuchi: 3870/2990/1750(SP=DP)/1250/1040

COURSE:Edit
LEVEL:9
BALLOON:
SCOREINIT:350,1110
SCOREDIFF:85

#START
2220222022202222,
1,
0022222022222022,
1,
0022122012221022,

1022102010201020,
1022122011221022,
1022102010102010,
03333040, //9

1022102010221020,
1020102210222020,
1022102010221020,
1020102210221010,

1022102010221020,
1020102210222020,
1022102210221022,
1022002200221010, //17

1022102010221020,
1022122010222220,
1022102010221020,
1022122010221210,

1022102010221020,
1022122010222220,
1022102210221010,
0022101022101010, //25

40201021,
0010200010102220,
10201021,
0010200010102220,

10201121,
01201121,
2210100021201000,
1120100012101000, //33

10201021,
01201122,
2210100021201000,
1120100012101000,

11221222,
11221222,
1110222011202210,
03300040, //41

#GOGOSTART
1111202011112022,
1020111010404000,
1121202011212022,
1020112010404000,

1111202011112022,
1020111010404011,
2000201022102010,
01121440, //49

1111202011112022,
1022111010404000,
1121202011212022,
1022112010404000,

1111202011112022,
1022111010404011,
2000201022102010,
03343040, //57
#GOGOEND

1020102011201020,
1022122012221022,
1020102011201020,
1022122011221022,

1020102011201020,
1022122012221020,
1120112011201120,
1122202000000000, //65

1000001120000010,
01011120,
1000001120000010,
0010001011102000,

1000001120000010,
01011120,
1120001012100010,
2210001021200010, //73

2000001120001010,
02101120,
1120001012100010,
2210001021200010,

1020102010201120,
1020112010201120,
1120112011221120,
03300040, //81

1,
3430,
1,
3440,

11,
11,
1020102010201011,
1010001000102022, //89

#SECTION
#BRANCHSTART p,60,80
#N
#GOGOSTART
1111202011112022,
1020111010404022,

#E
#GOGOSTART
1112202011122022,
1020121010404022,

#M
#GOGOSTART
1211202012112022,
1020112010404022, //91

#SECTION
#BRANCHSTART p,60,80
#N
#SCROLL 1.2
1121202011212022,
1020112010404022,

#E
#SCROLL 1.2
1211202012112022,
1020212010404022,

#M
#SCROLL 1.2
1221202012212022,
1020122010404022, //93

#SECTION
#BRANCHSTART p,60,80
#N
#SCROLL 1.5
1111202011112022,
1020111010404011,
2000201022102010,
0010102010404022,

#E
#SCROLL 1.5
1112202011122022,
1020121010404011,
2000201022102010,
0010102010404022,

#M
#SCROLL 1.5
1211202012112022,
1020112010404011,
2000201022102010,
0010102010404022, //97

#SECTION
#BRANCHSTART p,60,80
#N
#SCROLL 2
1111202011112022,
1022111010404022,

#E
#SCROLL 2
1112202011122022,
1022121010404022,

#M
#SCROLL 2
1211202012112022,
1022112010404022, //99

#SECTION
#BRANCHSTART p,60,80
#N
#SCROLL 3
1121202011212022,
1022112010404022,

#E
#SCROLL 3
1211202012112022,
1022112010404022,

#M
#SCROLL 3
1221202012212022,
1022122010404022, //101

#SECTION
#BRANCHSTART p,60,80
#N
#SCROLL 4
1111202011112022,
1022111010404011,
2000201022102010,
01121000,
03343040,
#GOGOEND

#E
#SCROLL 4
1112202011122022,
1022121010404011,
2000201022102010,
01121000,
03343040,
#GOGOEND

#M
#SCROLL 4
1211202012112022,
1022112010404011,
2000201022102010,
01121000,
03343040,
#GOGOEND
#BRANCHEND

#SCROLL 1
0,
0,
0,
0, //110 
#END


COURSE:Oni
LEVEL:8
BALLOON:
SCOREINIT:400,1300
SCOREDIFF:100

#START
2220222022202000,
3,
3,
3,
33,

10101012,
1000100010221020,
1000100010221022,
03333000, //9

10201121,
01201220,
10201121,
01201020,

10201121,
01201220,
11201122,
1000000000222020, //17

1020102010221020,
1020102010221020,
1020102010221020,
1022102012221020,

1020102010221020,
1020102010221020,
1022102010221020,
0011101011101000, //25

1002201010022010,
0012201010002000,
1002201010022010,
0012201010002011,

1002201010022010,
0012201010022010,
1210001012100010,
1210001012100010, //33

1002201010022010,
0012201010222020,
1120100011201000,
1120100011201000,

5,
000000000000000000000008000000000000000000000000,
1110111011111030,
03300000, //41

#GOGOSTART
1011201020112010,
1120112010222000,
1011201020112010,
1120112010222000,

1011201020112020,
1022102210112020,
1110222011102220,
500000000000000000000000000000000008000000000000, //49

1020102011201120,
1000111110202000,
1020102011201120,
1000222220102000,

1020102011201120,
1000111110222020,
1110111011111110,
03333000, //57
#GOGOEND

1020102010221020,
1020102010221020,
1020102010221020,
1022102012221020,

1020102010221020,
1020102010221020,
1220122012201220,
1022122010000000, //65

10012001,
01012000,
10012001,
01012000,

10012001,
01012001,
12012201,
12012200, //73

10012011,
0111,
2110100021101000,
2110100021101000,

5,
000000000000000000000008000000000000000000000000,
1110111011111030,
03300000, //81

20202022,
0022200020000000,
20202022,
0022202020000000,

12101210,
12121210,
10010000,
33030303, //89

#GOGOSTART
1011201020112010,
1120112210222000,
1011201020112010,
1120112210222020,

1011201020112020,
1022102210222020,
1110222011102220,
500000000000000000000000000000000008000000000000, //97

1020102011201120,
1022111110202000,
1020102011201120,
1022102210112020,

1020102011201120,
1022111110222020,
1112111211111110,
0030303030222220, //105

03333040,
#GOGOEND
0,
0,
0,
0, //110
#END


COURSE:Hard
LEVEL:7

STYLE:SINGLE

BALLOON:
SCOREINIT:450,1900
SCOREDIFF:120

#START
0,
3,
3,
3,
33,

10101012,
10101012,
10101212,
03333000, //9

10201120,
11210120,
10201120,
11210120,

10201120,
11210120,
10201120,
1, //17

12121212,
12121210,
12121212,
12101210,

12121212,
12121210,
12101212,
1, //25

10011021,
01011000,
10011021,
01011220,

10011021,
01011220,
12,
2, //33

10112011,
21012021,
10021002,
10021020,

5,
000000000000000000000008000000000000000000000000,
1110111011100030,
03300000, //41

#GOGOSTART
10101212,
1000111110000000,
10101212,
1000222220000000,

10101212,
1000111110002220,
1000222010002220,
500000000000000000000000000000000008000000000000, //49

10101012,
1020100011111000,
10101012,
1020100022222000,

10101212,
1000111110000000,
1111100011111000,
03333000, //57
#GOGOEND

12121210,
12121210,
12121212,
12121210,

12121210,
12121210,
12121212,
1020102210000000, //65

10022001,
01022000,
10022001,
01022000,

10022001,
01022002,
10021002,
10021000, //73

10022001,
01022000,
11,
11,

1111,
11111111,
1110111011100030,
03300000, //81

3,
3,
3,
33,

3232,
3232,
600000000000000000000000000000000008000000000000,
33030300, //89

#GOGOSTART
10101212,
1000111110004000,
10101212,
1000222220003000,

10101212,
1000111110002220,
1000222010002220,
500000000000000000000000000000000008000000000000, //97

10101012,
1020102011111000,
10101012,
1020102022222000,

10101212,
1020111110002000,
1111100011111000,
03333000, //105

03333040,
#GOGOEND
0,
0,
0,
0, //110
#END

STYLE:DOUBLE

BALLOON:
SCOREINIT:460,1910
SCOREDIFF:120

#START P1
0,
3,
3,
3,
33,

10101012,
10101012,
10101212,
03333000, //9

10201120,
11210120,
10201120,
11210120,

10201120,
11210120,
10201120,
1, //17

12121212,
12121210,
12121212,
12101210,

12121212,
12121210,
12101212,
1, //25

10011021,
01011000,
10011021,
01011220,

10011021,
01011220,
12,
2, //33

10112011,
21012021,
10021002,
10021020,

5,
000000000000000000000008000000000000000000000000,
1110111011100030,
03300000, //41

#GOGOSTART
1000100011111000,
12121220,
10101212,
1000222220004000,

1000100011111000,
12121220,
1110200011102000,
500000000000000000000000000000000008000000000000, //49

10101212,
1000111110000000,
1000100022222000,
12121030,

10101212,
1000111110000000,
1111100011111000,
03333000, //57
#GOGOEND

12121210,
12121210,
12121212,
12121210,

12121210,
12121210,
12121212,
1020102210000000, //65

10022001,
01022000,
10022001,
01022000,

10022001,
01022002,
10021002,
10021000, //73

10022001,
01022000,
11,
11,

1111,
11111111,
1110111011100030,
03300000, //81

3,
3,
3,
33,

3232,
3232,
600000000000000000000000000000000008000000000000,
33030300, //89

#GOGOSTART
10101212,
1000111110000000,
1000100022222000,
12121030,

10101212,
1000111110000000,
1110200011102000,
500000000000000000000000000000000008000000000000, //97

1000100011111000,
12121220,
10101212,
1000222220004000,

1000100011111000,
12121220,
1111100011111000,
03333000, //105

03333040,
#GOGOEND
0,
0,
0,
0, //110
#END

BALLOON:
SCOREINIT:460,1910
SCOREDIFF:120

#START P2
0,
3,
3,
3,
33,

10101012,
10101012,
10101212,
03333000, //9

10201120,
11210120,
10201120,
11210120,

10201120,
11210120,
10201120,
1, //17

12121212,
12121210,
12121212,
12101210,

12121212,
12121210,
12101212,
1, //25

10011021,
01011000,
10011021,
01011220,

10011021,
01011220,
12,
2, //33

10112011,
21012021,
10021002,
10021020,

5,
000000000000000000000008000000000000000000000000,
1110111011100030,
03300000, //41

#GOGOSTART
10101212,
1000111110000000,
1000100022222000,
12121030,

10101212,
1000111110000000,
1110200011102000,
500000000000000000000000000000000008000000000000, //49

1000100011111000,
12121220,
10101212,
1000222220004000,

1000100011111000,
12121220,
1111100011111000,
03333000, //57
#GOGOEND

12121210,
12121210,
12121212,
12121210,

12121210,
12121210,
12121212,
1020102210000000, //65

10022001,
01022000,
10022001,
01022000,

10022001,
01022002,
10021002,
10021000, //73

10022001,
01022000,
11,
11,

1111,
11111111,
1110111011100030,
03300000, //81

3,
3,
3,
33,

3232,
3232,
600000000000000000000000000000000008000000000000,
33030300, //89

#GOGOSTART
1000100011111000,
12121220,
10101212,
1000222220004000,

1000100011111000,
12121220,
1110200011102000,
500000000000000000000000000000000008000000000000, //97

10101212,
1000111110000000,
1000100022222000,
12121030,

10101212,
1000111110000000,
1111100011111000,
03333000, //105

03333040,
#GOGOEND
0,
0,
0,
0, //110
#END


COURSE:Normal
LEVEL:6
BALLOON:10,10
SCOREINIT:600,3370
SCOREDIFF:173

//#51,53,55,99,101,103 in 3DS1: 22203000,

#START
0,
3,
3,
3,
33,

1111,
1111,
1111,
01111000, //9

10001110,
12,
10001110,
12,

10001110,
12,
10001110,
1, //17

10101110,
1202,
10101110,
12,

10101110,
1202,
10101110,
1, //25

10022000,
12,
10022000,
1,

10022000,
12,
1,
0, //33

10022000,
10022000,
11,
11,

5,
000000000000000000000008000000000000000000000000,
7,
0000000008000000, //41

#GOGOSTART
1111,
11103000,
1111,
11103000,

1111,
11103000,
11101110,
3, //49

1111,
22204000,
1111,
22204000,

1111,
22204000,
11101110,
01111000, //57
#GOGOEND

1111,
1110,
2222,
2220,

1111,
1110,
2222,
2220, //65

10022000,
12,
10022000,
1,

10022000,
12,
1,
0, //73

10022000,
10022000,
11,
11,

5,
000000000000000000000008000000000000000000000000,
7,
0000000008000000, //81

3,
3,
3,
33,

34,
34,
3,
33030000, //89

#GOGOSTART
1111,
11103000,
1111,
11103000,

1111,
11103000,
11101110,
3, //97

1111,
22204000,
1111,
22204000,

1111,
22204000,
11101110,
500000000000000000000000000000000008000000000000, //105

01111030,
#GOGOEND
0,
0,
0,
0, //110
#END


COURSE:Easy
LEVEL:3
BALLOON:8,8
SCOREINIT:410,4710
SCOREDIFF:120

#START
0,
3,
0,
3,
3,

11,
11,
11,
0330, //9

1110,
11,
1110,
1,

1110,
12,
1110,
1, //17

1111,
12,
1111,
12,

1111,
12,
1111,
1, //25

12,
2,
12,
2,

12,
2,
1,
0, //33

12,
22,
11,
11,

500000000000000000000000000000000000000000000008,
0,
7,
08000000, //41

#GOGOSTART
1111,
13,
2222,
24,

1111,
13,
11,
1, //49

1111,
0330,
2222,
0440,

1111,
0330,
11,
0330, //57
#GOGOEND

1111,
12,
1111,
12,

1111,
12,
1111,
1, //65

12,
2,
12,
2,

12,
2,
1,
0, //73

12,
22,
11,
11,

500000000000000000000000000000000000000000000008,
0,
7,
08000000, //81

3,
4,
3,
43,

33,
33,
3,
3330, //89

#GOGOSTART
1111,
13,
2222,
24,

1111,
13,
11,
1, //97

1111,
0330,
2222,
0440,

1111,
0330,
11,
0330, //105

0330,
#GOGOEND
0,
0,
0,
0, //110
#END