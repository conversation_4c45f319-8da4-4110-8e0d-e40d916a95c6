//TJADB Project
TITLE:<PERSON><PERSON><PERSON><PERSON> o Hibikasero!
TITLEJA:音虫を響かせろ！
SUBTITLE:--<PERSON><PERSON><PERSON> × <PERSON><PERSON> Okada(BNSI)
SUBTITLEJA:Ki<PERSON><PERSON> × <PERSON><PERSON> Okada(BNSI)
BPM:150
WAVE:<PERSON><PERSON><PERSON><PERSON> o Hibikasero.ogg
OFFSET:-1.141
DEMOSTART:31.426


COURSE:Oni
LEVEL:9
BALLOON:7,18
SCOREINIT:570,1640
SCOREDIFF:145

#START
6,
#MEASURE 2/4
000000000008000000000000,
2, //3

#MEASURE 4/4
1021020210201000,
1101120210210000,
2022020220202000,
2202220220220001, 

1121101211010110,
1021021011020002,
2222202222020220,
2022022022040000, //11

01120112,
1111102210010020,
1222102210000022,
100000100200100000100000500000000000000008000000,

0010102010010020,
1011221170000000,
8,
#SCROLL 1.7
3333302222203232,
3, //20

#SCROLL 1
#GOGOSTART
1011012110111010,
100000100100000100100200100000100000101010100000,
1011012120111010,
1122011210101010,

2012012112112021,
200000100200000100200100100200100100201010200000,
2012012112112021,
2212212211221110, 
#GOGOEND
202020000200200000200000200000100000100000100000, //29

1000102011011020,
100000200200100010001000200000000100000000100000,
1201102012011020,
1022101020001000,

1001102011011020,
100000100200100010001000200000000100000000100000,
1201102012011020,
100010001000202020001010100000000100200200200000, //37

1011011020202100,
100200000200100000200000100010001000100000000000,
1111011020202100,
101010100200100100200000100010001000200000000000,

500000000000000008000000100000200000200100000000,
1212202011102212,
1111002010202100,
200000000100000000200000100020002000100000000000, //45

011102011202,
011112021102,
300300301212,
1012101012212120,

1011102010010020,
100100100200100100100200500000000000000008000000,
111120211120,
102020102020102020222020,
4447,
0008, //55

#GOGOSTART
1012012112112021,
200000100200000100200100100200200000101010200000,
1012012112112021,
2012012122122011,

2112012112112012,
1121021121121022,
1211211211211221,
2212212211221030,
#GOGOEND
0, //64
#END


COURSE:Hard
LEVEL:6
BALLOON:6,15
SCOREINIT:600,2380
SCOREDIFF:163

#START
6,
#MEASURE 2/4
000000000008000000000000,
2, //3

#MEASURE 4/4
1011010010101000,
1101110110110000,
2022020020202000,
2202220220220000,

1010100010010010,
1001001011010000,
2020200020020020,
2002002022020000, //11
 
01120112,
1011102010010010,
3000300030000011,
100000000100000000100000500000000000000008000000,

0010102010010020,
1010111070000000,
8,
2220201110102220,
200000500000000000000000000000000008000000000000, //20

#GOGOSTART
1011011010101000,
2011011010101000,
1011011010101000,
2011011010102000,

2022022010101010,
2022022010101010,
2022022010201020,
2022022030403040,
#GOGOEND
0, //29

0010102010010020,
100111100000,
0010102010010020,
1011101020000000,

0010102010010020,
100111100000,
1000102010010020,
500000000000000000000000000000000000000008000000, //37

1011011010101000,
200200000200200000200000200020002000200000000000,
1111011010101000,
200000000200200000200000200020002000200000000000,

500000000008000000000000100000100000100000100000,
2220202022202000,
1111001010101000,
200000000200000000200000200020002000200000000000, //45

011100022200,
011102011100,
300300300022,
500000000000000000000000000008000000000000000000,

0010102010010020,
100100100000100000100000500000000008000000000000,
111110222220,
111111111100, 
4447,
0008, //55

#GOGOSTART
1011011010101000,
2011011010101000,
1011011010101000,
2011011010102000,

1022011020202020,
1022011020202020,
1022011010201020,
1022011030403040,
#GOGOEND
0, //64
#END


COURSE:Normal
LEVEL:5
BALLOON:4,10
SCOREINIT:810,4260
SCOREDIFF:255

#START
6,
#MEASURE 2/4
000000000008000000000000,
2, //3

#MEASURE 4/4
10001110,
1000100010010000,
20002220,
2000200020020000,

10111000,
1001000010010000,
20222000,
2002000020020000, //11

01100220,
11101000,
1110,
1120,

01102020,
11117000,
8,
30030022,
200000500000000000000000000000000008000000000000, //20
 
#GOGOSTART
10001110,
20001110,
10001110,
20001110,

10011110,
10022220,
20011111,
10022222,
#GOGOEND
0, //29

10022001,
1220,
10022001,
1210,

10022001,
1220,
10022001,
500000000000000000000000000000000008000000000000, //37

20001110,
20000200,
20001110,
20000220,

500000000008000000000000100000100000100000100000,
00000200,
20001110,
1022, //45

1112,
1212,
3330,
500000000000000000000000000000000008000000000000,

0020200010010010,
100000000000100000000000500000000008000000000000,
3333,
600000000000000000000000000000000000000008000000,
4447,
0008, //55

#GOGOSTART
10001110,
20001110,
10001110,
20001110,

10011110,
10022220,
20011111,
10033333,
#GOGOEND
0, //64
#END


COURSE:Easy
LEVEL:3
BALLOON:3,7,3,3,5
SCOREINIT:740,6590
SCOREDIFF:263

#START
6,
#MEASURE 2/4
000008000000000000000000,
2, //3

#MEASURE 4/4
1011,
11,
2022,
22,

1110,
11,
2220,
22, //11

0101,
12,
1110,
2220,

0110,
1170,
8000,

30030030,
300000500000000000000000000008000000000000000000, //20

#GOGOSTART
500000000000000008000000100000000000100000000000,
500000000000000008000000100000000000100000000000,
500000000000000008000000100000000000100000000000,
500000000000000008000000100000000000100000000000,

500000000000000008000000300000000000300000000000,
500000000000000008000000300000000000300000000000,
600000000000000008000000300000000000300000000000,
600000000000000008000000300000000000300000000000,
#GOGOEND
0, //29

01,
0220,
0101,
0220,

0011,
0220,
01,
500000000000000000000000000008000000000000000000, //37

1011,
7008,
1011,
78,

1011,
78,
1011,
0022, //45

0101,
0101,
1110,
500000000000000000000000000000000000000008000000,

0110,
1110,
3333,
600000000000000000000000000000000008000000000000,
4447,
00000800, //55

#GOGOSTART
500000000000000008000000100000000000100000000000,
500000000000000008000000100000000000100000000000,
500000000000000008000000100000000000100000000000,
500000000000000008000000100000000000100000000000,

500000000000000008000000300000000000300000000000,
500000000000000008000000300000000000300000000000,
600000000000000008000000300000000000300000000000,
600000000000000008000000300000000000300000000000,
#GOGOEND
0, //64
#END