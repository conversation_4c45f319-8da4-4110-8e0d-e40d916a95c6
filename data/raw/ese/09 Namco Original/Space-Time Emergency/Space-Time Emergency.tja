//TJADB Project
TITLE:Space-Time Emergency
TITLEJA:Space-Time Emergency
SUBTITLE:--GARIN
SUBTITLEJA:GARIN
BPM:150
WAVE:Space-Time Emergency.ogg
OFFSET:-0.262
DEMOSTART:52.679


COURSE:Oni
LEVEL:9
BALLOON:37,7,40,17,37
SCOREINIT:600,1770
SCOREDIFF:145

#START
#MEASURE 7/8
10000000200202,
#MEASURE 3/4
1011,
#MEASURE 7/8
20000000200202,
#MEASURE 4/4
1020201022211122,

#MEASURE 7/8
10221210200202,
#MEASURE 15/16
112120100100100,
#MEASURE 4/4
2122121020020112,
#MEASURE 7/16
1001021,

#MEASURE 4/4
7,
00000008,
2022020202002022,
0202012210202222, //12

1011210102102011,
2101021022112112,
#SCROLL 1.1
0011210102102011,
2101122120000000,

#SCROLL 1.3
1011210102102011,
2101020022112112,
#SCROLL 1.5
0011210102102011,
200100000100100200200100700000000000000000080000, //20

#SCROLL 1
1000001210010020,
000000202020200000100000000000100100100200200200,
100000202020200000200000100000000100000000200000,
0022221000221020,

1000001210210120,
000000202020200000100000000000100100200200100200,
100000202020200000200000100000000100000000200000,
0011112000221270,
#SCROLL 1.9
0,
00000008,

#SCROLL 2.3
1012210101201012,
2102012022112112,
#SCROLL 2.7
0012210201201012,
200100000200000100200200500000000000000008000000, //34

#SCROLL 1
#GOGOSTART
3000112210210122,
1021012210120211,
0220112210220101,
2202112210221220,

0010112010220112,
2122122212212221,
2021002021021122,
1111010101011200, //42

3000112210210122,
1021012210120211,
0220112210220101,
2202112212021200,

2010112010220112,
2122122122122121,
2021002021021122,

1111010101010111,
2222111122112217,
00000008,
100000200000100200100100200200100000100100101010, //53
#GOGOEND

#MEASURE 7/8
10000000200202,
#MEASURE 3/4
1011,
#MEASURE 7/8
20000000200202,
#MEASURE 4/4
1112211122211122,

#MEASURE 7/8
10221121201202,
#MEASURE 15/16
112212102102101,
#MEASURE 4/4
2122112120120112,
#MEASURE 7/16
1021021, //61

#MEASURE 4/4
7,
00000008,
3,
0, //65
#END


COURSE:Hard
LEVEL:7
BALLOON:30,5,32,14,30
SCOREINIT:770,2940
SCOREDIFF:200

#START
#MEASURE 7/8
10000000200201,
#MEASURE 3/4
1011,
#MEASURE 7/8
20000000200201,
#MEASURE 4/4
1000202020202220,

#MEASURE 7/8
10001000200201,
#MEASURE 15/16
100020100100100,
#MEASURE 4/4
2000100020020100,
#MEASURE 7/16
1001020,

#MEASURE 4/4
9,
09,
8,
0, //12

1011010101001011,
0101020001002001,
#SCROLL 1.1
0011010101001011,
0101022020000000,

#SCROLL 1.2
1011010101001011,
0101020001002001,
#SCROLL 1.4
0011010101001011,
000100000100000200200000700000000000000000080000, //20

#SCROLL 1
1000001110010020,
0011101000101110,
2022202010010050,
000000000000000000000000000000000008000000000000,

1000001110010020,
0022202000101110,
2022202010010020,
0011102000111070,
#SCROLL 1.7
0,
00000008,

#SCROLL 2
1011010101001011,
0101010001002001,
#SCROLL 2.3
0011010101001011,
000100000100000100000000500000000000000008000000, //34

#SCROLL 1
#GOGOSTART
3000101010110110,
1011011010110201,
0000101010110101,
1001101010102000,

0010101010110110,
0100100010010000,
1011002011010110,
500000000000000000000000000000000008000000000000, //42

3000101010110110,
1011011010110201,
0000101010110101,
1001011022020200,

2010101010110110,
0100100100100100,
1011002011010110,

500000000000000000000000000000000008000000000000,
2020101022201117,
00000008,
1010101011101110, //53
#GOGOEND

#MEASURE 7/8
10000000200201,
#MEASURE 3/4
1011,
#MEASURE 7/8
20000000200201,
#MEASURE 4/4
1000202020202220,

#MEASURE 7/8
10001000200201,
#MEASURE 15/16
100020100100100,
#MEASURE 4/4
2000100020020100,
#MEASURE 7/16
1001020,

#MEASURE 4/4
7,
00000008,
3,
0, //65
#END


COURSE:Normal
LEVEL:5
BALLOON:3,3,18,21,8,8,10,3,3,21
SCOREINIT:1080,5800
SCOREDIFF:325

#START
#MEASURE 7/8
10000000200200,
#MEASURE 3/4
1011,
#MEASURE 7/8
20000000200200,
#MEASURE 4/4
1,

#MEASURE 7/8
10000000200200,
#MEASURE 15/16
100000100100100,
#MEASURE 4/4
2000000070000800,
#MEASURE 7/16
7000080,

#MEASURE 4/4
9,
09,
8,
0, //12

1201,
200000000000000000000000000500000000000000008000,
#SCROLL 1.1
0201,
22,

#SCROLL 1.2
1221,
200000000000200000000000000500000000000000008000,
#SCROLL 1.3
0221,
200000000000200000000000500000000000000008000000, //20

#SCROLL 1
1000001010010030,
0,
1000101010010020,
01120110,

1000001010010040,
0,
1000101010010020,
01120117,

#SCROLL 1.5
0,
00000008,

#SCROLL 1.7
1222,
100000000000200000000000000500000000000000008000,
#SCROLL 1.9
0222,
100000000000200000000000500000000000000008000000, //34

#SCROLL 1
#GOGOSTART
10111010,
10111020,
10111010,
10111020,

10111110,
0700000000000080,
10121011,
500000000000000000000000000000000008000000000000, //42

10112010,
10112020,
10112010,
10112020,

10111110,
0700000000000080,
10121010,

500000000000000000000000000000000008000000000000,
1000100010201009,
000000000000000000000000000000000009000000000000,
8, //53
#GOGOEND

#MEASURE 7/8
10000000200200,
#MEASURE 3/4
1011,
#MEASURE 7/8
20000000200200,
#MEASURE 4/4
10102022,

#MEASURE 7/8
10000000200200,
#MEASURE 15/16
100000100100100,
#MEASURE 4/4
2000000070000800,
#MEASURE 7/16
7000080,

#MEASURE 4/4
9,
09,
8,
0, //65
#END


COURSE:Easy
LEVEL:3
BALLOON:2,2,15,16,5,5,8,2,2,16
SCOREINIT:1090,10890
SCOREDIFF:563

#START
#MEASURE 7/8
1,
#MEASURE 3/4
1,
#MEASURE 7/8
2,
#MEASURE 4/4
1,

#MEASURE 7/8
1000200,
#MEASURE 15/16
1,
#MEASURE 4/4
2000000070000800,
#MEASURE 7/16
7000080,

#MEASURE 4/4
9,
09,
8,
0, //12

1001,
2,
#SCROLL 1.1
1001,
2,

#SCROLL 1.2
1201,
2,
#SCROLL 1.3
1201,
2, //20

#SCROLL 1
10001003,
0,
10001003,
00030000,

10001004,
0,
10001003,
00030007,
#SCROLL 1.4
0,
00000008,

#SCROLL 1.5
2202,
2,
#SCROLL 1.6
2202,
200000000000000000000000500000000008000000000000, //34

#SCROLL 1
#GOGOSTART
11,
1110,
11,
1110,

1111,
0700000000000080,
1011,
500000000000000000000000000008000000000000000000, //42

12,
1120,
12,
1120,

1111,
0700000000000080,
1011,

500000000000000000000000000008000000000000000000,
1000100010001009,
000000000000000000000000000000000009000000000000,
8, //53
#GOGOEND

#MEASURE 7/8
1,
#MEASURE 3/4
1,
#MEASURE 7/8
2000200,
#MEASURE 4/4
1,

#MEASURE 7/8
1000200,
#MEASURE 15/16
100000000000000000500000000000000008000000000,
#MEASURE 4/4
2000000070000800,
#MEASURE 7/16
7000080,

#MEASURE 4/4
9,
09,
8,
0, //65
#END