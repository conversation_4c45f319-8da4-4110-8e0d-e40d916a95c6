//TJADB Project
TITLE:IOSYS <PERSON>ki no Bakushoku Matsuri 2024
TITLEJA:イオシス秋の爆食祭2024
SUBTITLE:--<PERSON><PERSON> (IOSYS) feat. <PERSON><PERSON><PERSON>
SUBTITLEJA:コバヤシユウヤ(IOSYS) feat. まるもこ
BPM:110
WAVE:IOSYS Aki no Bakushoku Matsuri 2024.ogg
OFFSET:-0.279
DEMOSTART:62.657


COURSE:Oni
LEVEL:10
BALLOON:2,3,4,5,6,7,848
SCOREINIT:1230
SCOREDIFF:0

#START
#MEASURE 1/32
#SCROLL 8
0,
#SCROLL 9
0,
#SCROLL 4.9
0,
#SCROLL 2
0,
#MEASURE 3/8
#SCROLL 2.4
0,

#MEASURE 1/32
#SCROLL 8
0,
#SCROLL 9
0,
#SCROLL 4.9
0,
#SCROLL 2
0,
#MEASURE 3/8
#SCROLL 2.4
0, //10

#MEASURE 1/32
#SCROLL 8
0,
#<PERSON><PERSON><PERSON> 9
0,
#SCROLL 4.9
0,
#SCROLL 2
0,
#MEASURE 3/8
#SCROLL 2.4
0,

#MEASURE 1/32
#SCROLL 8
0,
#SCROLL 9
0,
#SCROLL 4.9
0,
#SCRO<PERSON> 2
0,
#MEASURE 3/8
#SCROLL 2.4
0, //20

#MEASURE 1/32
#SCROLL 8
0,
#SCROLL 9
0,
#SCROLL 4.9
0,
#SCROLL 2
0,
#MEASURE 3/8
#SCROLL 2.4
0,

#MEASURE 1/32
#SCROLL 8
0,
#SCROLL 9
0,
#SCROLL 4.9
0,
#SCROLL 2
0,
#MEASURE 3/8
#SCROLL 2.4
0, //30

#MEASURE 1/32
#SCROLL 8
0,
#SCROLL 9
0,
#SCROLL 4.9
0,
#SCROLL 2
0,
#MEASURE 3/8
#SCROLL 2.4
0,

#MEASURE 1/32
#SCROLL 8
0,
#SCROLL 9
0,
#SCROLL 4.9
0,
#SCROLL 2
0,
#MEASURE 3/8
#SCROLL 2.4
0, //40

#MEASURE 4/4
#BPMCHANGE 178
#SCROLL 1
200100000100200100000100200000101010200000000000, //41

1021202011221020,
100200200200100000200200100100101010200000101010,
1021202011221020,
100200200200100000200200100200202020200000101010, //45

1021202011221020,
100200200200100000200000300000000300000000101010,
1021202011221022,
100000101010100000101010100000101010100100100000, //49

500008000000
#BPMCHANGE 176.2
#SCROLL 1.01
500008000000
#BPMCHANGE 170
#SCROLL 1.05
500008000000
500008000000,
#BPMCHANGE 160
#SCROLL 1.1
212
#BPMCHANGE 150
#SCROLL 1.2
212
#BPMCHANGE 140
#SCROLL 1.3
212
#BPMCHANGE 135
#SCROLL 1.35
212, //51

#BPMCHANGE 135
#SCROLL 1
100200100200100101001100,
100200100200100202001200,
100200100200100101002202,
100000000101100100100100, //55

100100100100100000200000100010200010000010100020,
100100100100100000200000100010200010000020200020,
100200100200100201001200,
30303330,
#BPMCHANGE 178
#SCROLL 1.4
500000000000000000000000000000000000000008000000, //60

#GOGOSTART
33307008,
40407008,
30307008,
33303040, //64

30307008,
40407008,
33307008,
#GOGOEND
#SCROLL 1
1010101101012000, //68

#SCROLL 1.4
#GOGOSTART
12221222,
1114,
#SCROLL 1.45
12121210,
1010101110200020, //72

#SCROLL 1.5
1110300022204000,
111100300000222200400000,
#SCROLL 1.55
111100300300222200400400,
101010100200100200100200101010101010100000100100, //76
#GOGOEND

#MEASURE 5/4
#SCROLL 1
0000
#BPMCHANGE 44.5
0, //77

#MEASURE 4/4
#BPMCHANGE 178
#GOGOSTART
1020102102102010,
1020102102102010,
1020102102102010,
100200100000100000200100202020200000200000200200, //81

1020102102102010,
1020102102102010,
1020122102102010,
1222122200304000, //85

1022201022102010,
1202102030003000,
1022201022102010,
1202102030003000, //89

1010101012222211,
10300022,
#GOGOEND
333333,
0300, //93

#BPMCHANGE 130
100000102000100000000100000100100000100000000000,
100000102000100000000100000100100000100000000000,
100110200100100110210000,
110110200100110100210111, //97

#BPMCHANGE 140
#GOGOSTART
2222,
#BPMCHANGE 150
200000000000200000000000200000000000200001010100,
#BPMCHANGE 160
22222222,
#BPMCHANGE 170
2222222222222220,
#GOGOEND
#BPMCHANGE 178
34,
4334, //103

1000202220101010,
0022222212212212,
#GOGOSTART
1022222010210210,
10101011
#GOGOEND
12121000, //107

#GOGOSTART
1022222010222010,
1022222010222210,
1102102011021020,
100200200201020200200200000000300000400000202020, //111

1002102012021020,
100202020100200200100000300000000000300000202020,
1002102012021020,
100202020100200200100000300000000000300000202020, //115

100000100000100000100200100200100200202020101010,
10300022,
#GOGOEND
4011104011104011,
1040111040111111, //119

#GOGOSTART
1021202011221020,
100200200200100000200200100100101010200000101010,
1021202011221020,
100200200200100000200200100200202020200000101010, //123

100000200100200000200200100100101010200000200000,
100200200200100000200200100200202020200000100000,
1212121211112222,
101010201010101010101010100000101010100100100000, //127
#GOGOEND

500008000000500008000000500008000000500008000000,
7,
8300,
0,
0, //132
#END


COURSE:Hard
LEVEL:7
BALLOON:7,514
SCOREINIT:1920
SCOREDIFF:0

#START
#MEASURE 1/32
#SCROLL 8
0,
#SCROLL 9
0,
#SCROLL 4.9
0,
#SCROLL 2
0,
#MEASURE 3/8
#SCROLL 2.4
0,

#MEASURE 1/32
#SCROLL 8
0,
#SCROLL 9
0,
#SCROLL 4.9
0,
#SCROLL 2
0,
#MEASURE 3/8
#SCROLL 2.4
0, //10

#MEASURE 1/32
#SCROLL 8
0,
#SCROLL 9
0,
#SCROLL 4.9
0,
#SCROLL 2
0,
#MEASURE 3/8
#SCROLL 2.4
0,

#MEASURE 1/32
#SCROLL 8
0,
#SCROLL 9
0,
#SCROLL 4.9
0,
#SCROLL 2
0,
#MEASURE 3/8
#SCROLL 2.4
0, //20

#MEASURE 1/32
#SCROLL 8
0,
#SCROLL 9
0,
#SCROLL 4.9
0,
#SCROLL 2
0,
#MEASURE 3/8
#SCROLL 2.4
0,

#MEASURE 1/32
#SCROLL 8
0,
#SCROLL 9
0,
#SCROLL 4.9
0,
#SCROLL 2
0,
#MEASURE 3/8
#SCROLL 2.4
0, //30

#MEASURE 1/32
#SCROLL 8
0,
#SCROLL 9
0,
#SCROLL 4.9
0,
#SCROLL 2
0,
#MEASURE 3/8
#SCROLL 2.4
0,

#MEASURE 1/32
#SCROLL 8
0,
#SCROLL 9
0,
#SCROLL 4.9
0,
#SCROLL 2
0,
#MEASURE 3/8
#SCROLL 2.4
0, //40

#MEASURE 4/4
#BPMCHANGE 178
#SCROLL 1
20202220, //41

1000100011101000,
1000100011102000,
1000100011101000,
1000100022202000, //45

1000100011101000,
1000100030030000,
1000100011101000,
12221222, //49

5
#BPMCHANGE 176.2
#SCROLL 1.01
0
#BPMCHANGE 170
#SCROLL 1.05
0
0,
#BPMCHANGE 160
#SCROLL 1.1
000000000000
#BPMCHANGE 150
#SCROLL 1.2
000000080000
#BPMCHANGE 140
#SCROLL 1.3
000000000000
#BPMCHANGE 135
#SCROLL 1.35
000000000000, //51

#BPMCHANGE 135
#SCROLL 1
100200100200100101000200,
100200100200300003000000,
100200100200100101000200,
30001111, //55

100100100100100000200000100000000010000000200000,
100100100100100000200000100000000010000000200000,
100200100200100101000200,
30303330,
#BPMCHANGE 178
#SCROLL 1.4
500000008000, //60

#SCROLL 1
#GOGOSTART
30302121,
40402121,
40302121,
33303040, //64

30402121,
40402121,
30402121,
#GOGOEND
500000000000000000000000000008000000000000000000, //68

#GOGOSTART
12221222,
1114,
12121210,
10101202, //72

11302240,
1110300022204000,
1110303022204040,
1110111011101111, //76
#GOGOEND

#MEASURE 5/4
#SCROLL 1
0000
#BPMCHANGE 44.5
0, //77

#MEASURE 4/4
#BPMCHANGE 178
#GOGOSTART
10220111,
0020202010222010,
10210121,
2220201000101010, //81

10220111,
0020202010222010,
10210105,
000000000000000000000008000000000000000000000000, //85

1022202010202020,
10213030,
1022202010202020,
10213030, //89

1010101011101110,
30300011,
#GOGOEND
600000000000000000000000000000000000000000000008,
0, //93

#BPMCHANGE 130
1010100101101000,
1010100101101000,
100100200100100100220000,
110100100100110100110000, //97

#BPMCHANGE 140
#GOGOSTART
2222,
#BPMCHANGE 150
2222,
#BPMCHANGE 160
22222222,
#BPMCHANGE 170
2220222022202220,
#GOGOEND
#BPMCHANGE 178
44,
3334, //103

1000202220101010,
0020202011111010,
#GOGOSTART
1020102010222020,
7
#GOGOEND
8, //107

#GOGOSTART
1000202020111010,
0020202010222010,
1000201110100050,
000000000000000000000008000000000000000000000000, //111

1022202010202020,
10213030,
1022202010202020,
10213030, //115

1010101011101110,
30300011,
#GOGOEND
600000000000000000000000000000000000000000000008,
0000000020201111, //119

#GOGOSTART
1000100011101000,
1000100011102000,
1000100011101000,
1000100022202000, //123

1010101011102000,
1010101022202000,
1010101011101110,
500000000000000000000000000000000000000000000008, //127
#GOGOEND

0,
7,
8300,
0,
0, //132
#END


COURSE:Normal
LEVEL:6
BALLOON:5,337
SCOREINIT:3290
SCOREDIFF:0

#START
#MEASURE 1/32
#SCROLL 8
0,
#SCROLL 9
0,
#SCROLL 4.9
0,
#SCROLL 2
0,
#MEASURE 3/8
#SCROLL 2.4
0,

#MEASURE 1/32
#SCROLL 8
0,
#SCROLL 9
0,
#SCROLL 4.9
0,
#SCROLL 2
0,
#MEASURE 3/8
#SCROLL 2.4
0, //10

#MEASURE 1/32
#SCROLL 8
0,
#SCROLL 9
0,
#SCROLL 4.9
0,
#SCROLL 2
0,
#MEASURE 3/8
#SCROLL 2.4
0,

#MEASURE 1/32
#SCROLL 8
0,
#SCROLL 9
0,
#SCROLL 4.9
0,
#SCROLL 2
0,
#MEASURE 3/8
#SCROLL 2.4
0, //20

#MEASURE 1/32
#SCROLL 8
0,
#SCROLL 9
0,
#SCROLL 4.9
0,
#SCROLL 2
0,
#MEASURE 3/8
#SCROLL 2.4
0,

#MEASURE 1/32
#SCROLL 8
0,
#SCROLL 9
0,
#SCROLL 4.9
0,
#SCROLL 2
0,
#MEASURE 3/8
#SCROLL 2.4
0, //30

#MEASURE 1/32
#SCROLL 8
0,
#SCROLL 9
0,
#SCROLL 4.9
0,
#SCROLL 2
0,
#MEASURE 3/8
#SCROLL 2.4
0,

#MEASURE 1/32
#SCROLL 8
0,
#SCROLL 9
0,
#SCROLL 4.9
0,
#SCROLL 2
0,
#MEASURE 3/8
#SCROLL 2.4
0, //40

#MEASURE 4/4
#BPMCHANGE 178
#SCROLL 1
0, //41

1111,
1120,
1111,
1120, //45

1111,
1120,
1111,
2222, //49

5
#BPMCHANGE 176.2
#SCROLL 1.01
0
#BPMCHANGE 170
#SCROLL 1.05
0
0,
#BPMCHANGE 160
#SCROLL 1.1
000000000000
#BPMCHANGE 150
#SCROLL 1.2
000000080000
#BPMCHANGE 140
#SCROLL 1.3
000000000000
#BPMCHANGE 135
#SCROLL 1.35
000000000000, //51

#BPMCHANGE 135
#SCROLL 1
1212,
11103000,
1212,
3011, //55

11101000,
11101000,
1212,
30303330,
#BPMCHANGE 178
#SCROLL 1.4
500000008000, //60

#SCROLL 1
#GOGOSTART
1130,
1140,
1130,
1114, //64

1130,
1140,
1130,
#GOGOEND
500000000000000000000000000008000000000000000000, //68

#GOGOSTART
11101110,
1214,
11101110,
2220, //72

1122,
1122,
11102220,
11101111, //76
#GOGOEND

#MEASURE 5/4
#SCROLL 1
0000
#BPMCHANGE 44.5
0, //77

#MEASURE 4/4
#BPMCHANGE 178
#GOGOSTART
10000111,
01111020,
10110000,
20220111, //81

00000111,
01111020,
10110105,
000000000000000000000008000000000000000000000000, //85

10101110,
10011020,
10101110,
10011020, //89

10101111,
30300011,
#GOGOEND
600000000000000000000000000000000000000000000008,
0, //93

#BPMCHANGE 130
1110,
1110,
1212,
11101110, //97

#BPMCHANGE 140
#GOGOSTART
22,
#BPMCHANGE 150
22,
#BPMCHANGE 160
2222,
#BPMCHANGE 170
22222220,
#GOGOEND
#BPMCHANGE 178
33,
3330, //103

10000111,
01111020,
#GOGOSTART
10110000,
7
#GOGOEND
8, //107

#GOGOSTART
10000111,
01111020,
10110105,
000000000000000000000008000000000000000000000000, //111

10101110,
10011020,
10101110,
10011020, //115

10101111,
30300011,
#GOGOEND
600000000000000000000000000000000000000000000008,
0, //119

#GOGOSTART
1111,
1120,
1111,
1120, //123

11102000,
11102000,
11113333,
500000000000000000000000000000000000000000000008, //127
#GOGOEND

0,
7,
8300,
0,
0, //132
#END


COURSE:Easy
LEVEL:3
BALLOON:4,114
SCOREINIT:8710
SCOREDIFF:0

#START
#MEASURE 1/32
#SCROLL 8
0,
#SCROLL 9
0,
#SCROLL 4.9
0,
#SCROLL 2
0,
#MEASURE 3/8
#SCROLL 2.4
0,

#MEASURE 1/32
#SCROLL 8
0,
#SCROLL 9
0,
#SCROLL 4.9
0,
#SCROLL 2
0,
#MEASURE 3/8
#SCROLL 2.4
0, //10

#MEASURE 1/32
#SCROLL 8
0,
#SCROLL 9
0,
#SCROLL 4.9
0,
#SCROLL 2
0,
#MEASURE 3/8
#SCROLL 2.4
0,

#MEASURE 1/32
#SCROLL 8
0,
#SCROLL 9
0,
#SCROLL 4.9
0,
#SCROLL 2
0,
#MEASURE 3/8
#SCROLL 2.4
0, //20

#MEASURE 1/32
#SCROLL 8
0,
#SCROLL 9
0,
#SCROLL 4.9
0,
#SCROLL 2
0,
#MEASURE 3/8
#SCROLL 2.4
0,

#MEASURE 1/32
#SCROLL 8
0,
#SCROLL 9
0,
#SCROLL 4.9
0,
#SCROLL 2
0,
#MEASURE 3/8
#SCROLL 2.4
0, //30

#MEASURE 1/32
#SCROLL 8
0,
#SCROLL 9
0,
#SCROLL 4.9
0,
#SCROLL 2
0,
#MEASURE 3/8
#SCROLL 2.4
0,

#MEASURE 1/32
#SCROLL 8
0,
#SCROLL 9
0,
#SCROLL 4.9
0,
#SCROLL 2
0,
#MEASURE 3/8
#SCROLL 2.4
0, //40

#MEASURE 4/4
#BPMCHANGE 178
#SCROLL 1
0, //41

11,
1,
11,
1, //45

11,
1,
11,
1, //49

5
#BPMCHANGE 176.2
#SCROLL 1.01
0
#BPMCHANGE 170
#SCROLL 1.05
0
0,
#BPMCHANGE 160
#SCROLL 1.1
000000000000
#BPMCHANGE 150
#SCROLL 1.2
000000080000
#BPMCHANGE 140
#SCROLL 1.3
000000000000
#BPMCHANGE 135
#SCROLL 1.35
000000000000, //51

#BPMCHANGE 135
#SCROLL 1
1,
13,
1,
3, //55

13,
13,
0,
3330,
#BPMCHANGE 178
#SCROLL 1.4
500000008000, //60

#SCROLL 1
#GOGOSTART
1100,
2200,
1100,
22, //64

1100,
2200,
1100,
#GOGOEND
500000000000000000000000000008000000000000000000, //68

#GOGOSTART
33,
3,
22,
2, //72

11,
22,
33,
3330, //76
#GOGOEND

#MEASURE 5/4
#SCROLL 1
0000
#BPMCHANGE 44.5
0, //77

#MEASURE 4/4
#BPMCHANGE 178
#GOGOSTART
1,
11,
1,
2,//81

1,
11,
20002005,
000000000000000000000008000000000000000000000000, //85

1,
2,
1,
2, //89

33,
3,
#GOGOEND
600000000000000000000000000000000000000000000008,
0, //93

#BPMCHANGE 130
1100,
1100,
11,
11, //97

#BPMCHANGE 140
#GOGOSTART
2,
#BPMCHANGE 150
2,
#BPMCHANGE 160
22,
#BPMCHANGE 170
22,
#GOGOEND
#BPMCHANGE 178
0,
0, //103

30000003,
0,
#GOGOSTART
11,
7
#GOGOEND
8, //107

#GOGOSTART
1,
11,
20002005,
000000000000000000000008000000000000000000000000, //111

1,
2,
1,
2, //115

33,
3,
#GOGOEND
600000000000000000000000000000000000000000000008,
0, //119

#GOGOSTART
11,
1,
11,
1, //123

1100,
2200,
1110,
500000000000000000000000000000000000000000000008, //127
#GOGOEND

0,
7,
8300,
0,
0, //132
#END