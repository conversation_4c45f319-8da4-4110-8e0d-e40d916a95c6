//TJADB Project
TITLE:Gunslinger Cinderella
TITLEJA:ガンスリンガーシンデレラ
SUBTITLE:--Cineraria Studio (TiS) feat. Rika Hisui
BPM:167
WAVE:Gunslinger Cinderella.ogg
OFFSET:-2.976
DEMOSTART:50.368

//Shinuchi: 2970/2220/1470/1110


COURSE:Oni
LEVEL:9
BALLOON:13,13
SCOREINIT:380,1150
SCOREDIFF:90

#START
0,
0,
1111,
10101011,

12121212,
12121211,
1120112011201120,
1111222211221212, //8

1120112010221020,
1120112212121020,
1120112010201120,
1212102212112020,

100200100200100000200000102010200200100100200000,
100100200100200000200000100100200200101010200000,
100100200200100100200200100100200100102020102020,
100100200200000200100100500000000000000000000008,
#BARLINEOFF
000
#SCROLL 3
3, //17

#BARLINEON
#SCROLL 1
1021102011201022,
1021102011201022,
1021102011201022,
1201202212102011,

1021102011201022,
1021102011201022,
1021102011201050,
000000000008000000000000100100000100100000200000, //25

1120102011201020,
1120112212021122,
1120102211201022,
1202102211201020,

1202102211201020,
30303303,
1120102011221122,
100000100000100000500000000000000000000008000000, //33

#GOGOSTART
1120112011210120,
1120112011212022,
1120112011202120,
7008,

1120112011210120,
1120112011212022,
1120112011212022,
1122112212121122, //41

1021021211221122,
1021021211121112,
1010221022203030,
400000000400000000600000000000000000000008000000,

1021021211221122,
1021021211121112,
1010221012201111,
500000000000000000000000000000000000000008000000, //49
#GOGOEND

32,
1010111020000010,
10121112,
0000101020201111,

22020202,
02020202,
12121111,
1210121212122222, //57

#MEASURE 3/4
110220,
101000222000,
110220,
101000222111,

#MEASURE 4/4
1010112020112212,
1010112220121020,
1010112220121020,
500000000000000000000000000000000000000000000008,
0, //66

0,
#BARLINEOFF
000
#SCROLL 4
3,
#BARLINEON
#SCROLL 1
#GOGOSTART
1120112011202120,
7008,

1120112011210120,
1120112011212022,
1120112011212022,
1122112212121122, //74

1021021211221122,
1021021211121112,
1010221022203030,
400000000400000000600000000000000000000008000000,

1021021211221122,
1021021211121112,
1010221012201111,
500000000000000000000000000000000000000008000000, //82
#GOGOEND

1120112010221020,
1120112212121020,
1120112010201120,
1212102212112020,

100200100200100000200000102010200200100100200000,
100100200100200000200000100100200200101010200000,
100100200200100100200200100100200100102020102020,
1122021211112211, //90

222122100000000200000000,
0,
0, //93
#END


COURSE:Hard
LEVEL:7
BALLOON:10,10
SCOREINIT:400,1540
SCOREDIFF:93

#START
0,
0,
1111,
10101011,

1111,
12121211,
1110111011101110,
500000000000000000000000000000000000000008000000, //8

1110111020111020,
2220102010201020,
1110111010201110,
2222200011111010,

2222200010111110,
2202200011111020,
1110111011111010,
500000000000000000000000000000000000000000000008,
0003, //17

12121122,
12121212,
12121122,
2202200020202011,

12121122,
12121212,
12121215,
000000000008000000000000100100000100100000200000, //25

12121212,
1020102022022000,
12121212,
1101102010201020,

2202202010201020,
30303303,
12121212,
100000100000100000500000000000000000000008000000, //33

#GOGOSTART
1110111010201020,
1110111010102020,
1010102011201120,
7008,

1110111010201020,
1110111010102020,
1020102011201120,
500000000000000000000000000000000008000000000000, //41

2002002022222220,
1001001011111110,
1010201022203030,
400000000400000000600000000000000000000008000000,

2002002022222220,
1001001011111110,
1010201010201110,
500000000000000000000000000000000000000008000000, //49
#GOGOEND

32,
1010111020000010,
10121112,
0000101020201110,

22020202,
02020202,
12121111,
1110111022222220, //57

#MEASURE 3/4
110220,
101000222000,
110220,
101000222011,

#MEASURE 4/4
1010111010222020,
1010202220020020,
1010202220020020,
500000000000000000000000000000000000000000000008,
0, //66

10101201,
01112030,
#GOGOSTART
1010102011201120,
7008,

1110111010201020,
1110111010102020,
1020102011201120,
500000000000000000000000000000000008000000000000, //74

2002002022222220,
1001001011111110,
1010201022203030,
400000000400000000600000000000000000000008000000,

2002002022222220,
1001001011111110,
1010201010201110,
500000000000000000000000000000000000000008000000, //82
#GOGOEND

1110111020111020,
2220102010201020,
1110111010201110,
2222200011111010,

2222200010111110,
2202200011111020,
1110111011111010,
100100100100000200200200500000000000000000000008, //90

0300,
0,
0, //90
#END


COURSE:Normal
LEVEL:6
BALLOON:7,7
SCOREINIT:460,2460
SCOREDIFF:113

#START
0,
0,
1111,
10101011,

1111,
10101011,
11111111,
500000000000000000000000000000000008000000000000, //8

12121212,
11000000,
12121212,
11100000,

11102020,
22202020,
11102220,
500000000000000000000000000000000000000000000008, 
0003, //17

10101122,
10121212,
10101122,
12,

10101122,
10101212,
10101105,
000000000008000000000000000000000000000000000000, //25

11101110,
11102220,
11101110,
11102220,

11101110,
30303303,
11101110,
500000000000000000000000000000000000000008000000, //33

#GOGOSTART
10101202,
12121022,
12121212,
7008,

10101202,
12121022,
12121212,
500000000000000000000000000000000008000000000000, //41

1001001020000000,
1001001020002020,
11210033,
400000000400000000600000000000000000000008000000,

1001001020000000,
1001001020002020,
11212021,
500000000000000000000000000000000000000008000000, //49
#GOGOEND

32,
10112001,
10120112,
00112211,

22020202,
02020202,
22221111,
100000100000100000100000500000000000000008000000, //57

#MEASURE 3/4
110220,
110210,
110220,
110210,

#MEASURE 4/4
33044000,
3030004030040040,
3030004030040040,
500000000000000000000000000000000000000000000008,
0, //66

10101101,
01111030,
#GOGOSTART
12121212,
7008,

10101202,
12121022,
12121212,
500000000000000000000000000000000008000000000000, //74

1001001020000000,
1001001020002020,
11210033,
400000000400000000600000000000000000000008000000,

1001001020000000,
1001001020002020,
11212021,
500000000000000000000000000000000000000008000000, //82
#GOGOEND

12121212,
11000000,
12121212,
11102020,

22202020,
11102020,
11102220,
500000000000000000000000000000000000000000000008, //90

0300,
0,
0, //93
#END


COURSE:Easy
LEVEL:5
BALLOON:5,5
SCOREINIT:360,3320
SCOREDIFF:88

#START
0,
0,
11,
11,

1111,
10101011,
10111011,
500000000000000000000000000008000000000000000000, //8

10101110,
22000000,
10101110,
22200000,

11100000,
22200000,
11102220,
500000000000000000000000000000000000000000000008,
0003, //17

10101110,
10101110,
1110,
12,

10101110,
10101110,
10101005,
000000000008000000000000000000000000000000000000, //25

10101110,
1122,
10101110,
1122,

10101110,
34,
10101110,
500000000000000000000000000008000000000000000000, //33

#GOGOSTART
11101110,
10222000,
11101110,
7008,

11101110,
10222000,
11101110,
500000000000000000000000000008000000000000000000, //41

1001001010000000,
1001001010000000,
1212,
3003003000000000,

1001001010000000,
2002002020000000,
1212,
500000000000000000000000000008000000000000000000, //49
#GOGOEND

32,
10102001,
1500,
000000000000000000000000000000000008000000000000,

02020202,
02020202,
5,
000000000000000000000000000008000000000000000000, //57

#MEASURE 3/4
110220,
110220,
110220,
110220,

#MEASURE 4/4
33044000,
3030004040040040,
3030004040040040,
500000000000000000000000000000000000000000000008,
0, //66

10101101,
01111030,
#GOGOSTART
11101110,
7008,

11101110,
10222000,
11101110,
500000000000000000000000000008000000000000000000, //74

1001001010000000,
2002002020000000,
1212,
3003003000000000,

1001001010000000,
2002002020000000,
1212,
500000000000000000000000000008000000000000000000, //82
#GOGOEND

10101110,
22000000,
10101110,
22200000,

11100000,
22200000,
11102220,
500000000000000000000000000000000000000008000000, //90

0300,
0,
0, //93
#END