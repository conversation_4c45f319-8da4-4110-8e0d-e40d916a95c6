//TJADB Project
TITLE:<PERSON><PERSON> no First Date
TITLEJA:どん子のファーストデート
SUBTITLE:--
SUBTITLEJA: 
BPM:190
WAVE:<PERSON><PERSON> no First Date.ogg
OFFSET:-2.829
DEMOSTART:34.661

//Shinuchi: 5260/4130/3140/2270/1230
//AC15 Score for former Ura DP Chart (now Ura Branch N and E): 410,1260/105
//Go Go Time (GGT) positions based on NS2 chart.
//AC16 GGT is in #67-87 instead of #69-89 (All Charts)
//All DP charts are AC16/NS2 version. P1-P2 are reversed before AC16(15?).

COURSE:Edit
LEVEL:8
BALLOON:5,5,5,5,5,5,5,5,5,5,5,5,5,5
SCOREINIT:400,1240
SCOREDIFF:100

#START
500008000000000000000000000000000000000000000000,
0,

#BRANCHSTART r,1,2
#N
1022202011102010,
0011201011102000,
1022202011102010,
0012202022201010,

3000202220100010,
0022201000102011,
1000202220100010,
0011201000112000, //n10

1000202220100010,
0022201000102011,
1000202220100010,
0010222020202220,

1011201011102010,
1011201011102010,
1011201011102010,
1010221022102210, //n18

1011201011102010,
1011201011102010,
1011201011102010,
3000302210201010,

1022202011102010,
0011201011102000,
1022202011102010,
0022102010201022, //n26

100000200000100000500000000000000000000008000000,
0022102010201020,
100000200200100000500000000000000000000008000000,
0022102010221020,

7878,
7878,
100000200200100000500000000000000000000008000000,
0022102010201222, //n34

100000200200100000500000000000000000000008000000,
0022102010221020,
7878,
7000800012221020,
1022102010221020,
3000302210221222,

10050000,
000008000000000000000000000000000000000000000000,
1022202011102010,
0012201020201010, //n44

1022202011102010,
0011201011102000,
1022202011102010,
0022201020102010,

3000202220100010,
0022201000102011,
1000202220100010,
0011201000112000, //n52

1000202220100010,
0022201000102011,
1000202220100010,
0010222020202220,

1011201011102010,
1011201011102010,
1011201011102010,
1010221022102210, //n60

1011201011102010,
1011201011102010,
1011201011102010,
3000302220201010,

1022202011102010,
0011201011102000,
1022202011102010,
0022202020202020, //n68

#GOGOSTART
1022201010222010,
1022201010222010,
1022201010222010,
1022201011102010,

1011201010112010,
1011201010112010,
1022201010222010,
1011201020102011, //n76

1022201010222010,
1022201010221020,
1022201010222010,
1022201010221020,
1022102010221020,
3000302220201010, //n82

1022202011102010,
0011202012221020,
100000000000000000500000000000000000000000000008,
0000000022221010,

1022202011102010,
0011201030203020,
300000200200200000600000000000000000000008000000,
#GOGOEND
0000000011202220, //n90

#BARLINEOFF
#SCROLL 10
03,
0,
0,
0,
0, //n95

#E
1022202011102010,
0011201011102000,
1022202011102010,
0012202022201010,

3000202220100010,
0022201000102011,
1000202220100010,
0011201000112000, //e10

1000202220100010,
0022201000102011,
1000202220100010,
0010222020202220,

1011201011102010,
1011201011102010,
1011201011102010,
1010221022102210, //e18

1011201011102010,
1011201011102010,
1011201011102010,
3000302210201010,

1022202011102010,
0011201011102000,
1022202011102010,
0022202020202020, //e26

1022201010222010,
1022201010222010,
1022201010222010,
1022201011102010,

1011201010112010,
1011201010112010,
1022201010222010,
1022201010201022, //e34

1022201010222010,
1022201010221020,
1022201010222010,
1022201010221020,
1022102010221020,
3000302220201010,

1022202011102010,
0011201011102000,
1022202011102010,
0012201020201010, //e44

1022202011102010,
0011201011102000,
1022202011102010,
0022201020102010,

3000202220100010,
0022201000102011,
1000202220100010,
0011201000112000, //e52

1000202220100010,
0022201000102011,
1000202220100010,
0010222020202220,

1011201011102010,
1011201011102010,
1011201011102010,
1010221022102210, //e60

1011201011102010,
1011201011102010,
1011201011102010,
3000302220201010,

1022202011102010,
0011201011102000,
1022202011102010,
0022102010201022, //e68

#GOGOSTART
100000200000100000500000000000000000000008000000,
0022102010201020,
100000200200100000500000000000000000000008000000,
0022102010221020,

7878,
7878,
100000200200100000500000000000000000000008000000,
0022102010201222, //e76

100000200200100000500000000000000000000008000000,
0022102010221020,
7878,
7000800012221020,
1022102010221020,
3000302210221222, //e82

100000000000000000500000000000000000000000000008,
0000000011102000,
1022202011102010,
0011202012221020,

100000000000000000500000000000000000000000000008,
00003232,
300000200200200000600000000000000000000008000000,
#GOGOEND
0011202000000000, //e90

#BARLINEOFF
#SCROLL 10
03,
0,
0,
0,
0, //e95

#M
1022202011102010,
0011201011102000,
1022202011102010,
0012202022201010,

3000202220100010,
0022201000102011,
1000202220100010,
0011201000112000, //m10

1000202220100010,
0022201000102011,
1000202220100010,
0010222020202220,

1011201011102010,
1011201011102010,
1011201011102010,
1010221022102210, //m18

1011201011102010,
1011201011102010,
1011201011102010,
3000302210201010,

1022202011102010,
0011201011102000,
1022202011102010,
0012202010201010, //m26

1022201010222010,
1022201022102010,
1022201010222010,
1022201011102010,

1011201010112010,
1011201010112010,
1022201010222010,
1011201020102011, //m34

1022201010222010,
1022201010221020,
1022201010222010,
1022201010221020,
1022102010221020,
3000302220201010,

1022202011102010,
0011201011102000,
1022202011102010,
0012201020201010, //m44

1022202011102010,
0011201011102000,
1022202011102010,
0022201020102010,

3000202220100010,
0022201000102011,
1000202220100010,
0011201000112000, //m52

1000202220100010,
0022201000102011,
1000202220100010,
0010222020202220,

1011201011102010,
1011201011102010,
1011201011102010,
1010221022102210, //m60

1011201011102010,
1011201011102010,
1011201011102010,
3000302220201010,

1022202011102010,
0011201011102000,
1022202011102010,
0012201020201010, //m68

#GOGOSTART
1022201010222010,
1022201022102010,
1022201010222010,
1022201011102010,

1011201010112010,
1011201010112010,
1022201010222010,
1011201020102011, //m76

1022201010222010,
1022201010221020,
1022201010222010,
1022201010221020,
1022102010221020,
3000302220201010, //m82

1022202011102010,
0011201011102000,
1022202011102010,
0011201022221010,

1022202011102010,
0011201011102000,
1022202022102010,
#GOGOEND
01221222, //m90

#BARLINEOFF
#SCROLL 10
000000000000000000000000500008000000000000000000,
0,
0,
0,
0, //m95
#BRANCHEND
#END


COURSE:Oni
LEVEL:4

STYLE:SINGLE
BALLOON:
SCOREINIT:610,2350
SCOREDIFF:170

#START
0,
0,

30011101,
00201220,
30011101,
00201220,

10102201,
02010212,
10201101,
00210120, //10

10102201,
02010212,
10201102,
00201012,

10120211,
0111,
10210122,
0111, //18

10220121,
00101120,
10001112,
10201112,

30002002,
00202220,
20022202,
00221120, //26

10220020,
10221120,
10220020,
10221120,

11201120,
10221022,
10220020,
10221120, //34

10221020,
10221120,
10221022,
10001022,
10221010,
40401012,

10011102,
00202220,
10011101,
20201220, //44

10011102,
00202220,
10011101,
20201220,

10101221,
02010112,
10102201,
00210120, //52

10101221,
02010212,
10201102,
01201122,

10120212,
01201122,
10220022,
12101010, //60

10220021,
00201210,
12101112,
10201112,

30002002,
00202220,
20022202,
0111, //68

#GOGOSTART
10110022,
10121010,
10110022,
10121212,

11201120,
10201210,
10110020,
00212010, //76

10220010,
10121020,
10121210,
1011,
10101011,
00001012, //82

30011100,
10101220,
30011100,
10101220,

30011100,
1133,
30040000,
#GOGOEND
01121112, //90

04,
0,
0,
0,
0, //95
#END

STYLE:DOUBLE
BALLOON:
SCOREINIT:610,2350
SCOREDIFF:168

#START P1
0,
0,

30011101,
00201220,
30011101,
00201220,

10102201,
02010212,
10201101,
00210120, //10

10102201,
02010212,
10201102,
00201012,

10120211,
0111,
10210122,
0111, //18

10220121,
00101120,
10001112,
10201112,

30002002,
00202220,
20022202,
00101010, //26

10110022,
10121010,
10110022,
10121212,

11201120,
10201210,
10110020,
00212010, //34

10220010,
10121020,
10121210,
10001010,
10101011,
00001012,

10011102,
00202220,
10011101,
20201220, //44

10011102,
00202220,
10011101,
20201220,

10101221,
02010112,
10102201,
00210120, //52

10101221,
02010212,
10201102,
01201122,

10120212,
01201122,
10220022,
12101010, //60

10220021,
00201210,
12101112,
10201112,

30002002,
00202220,
20022202,
00221120, //68

#GOGOSTART
10220020,
10221120,
10220020,
10221120,

11201120,
10221022,
10220020,
10221120, //76

10221020,
10221120,
10221022,
10001022,
10221010,
40401012, //82

30011100,
10101220,
30011100,
10101220,

30011100,
1133,
30040000,
#GOGOEND
01121112, //90

04,
0,
0,
0,
0, //95
#END

BALLOON:
SCOREINIT:600,2350
SCOREDIFF:168

#START P2
0,
0,

30011101,
00201220,
30011101,
00201220,

10102201,
02010212,
10201101,
00210120, //10

10102201,
02010212,
10201102,
00201012,

10120211,
0111,
10210122,
0111, //18

10220121,
00101120,
10001112,
10201112,

30002002,
00202220,
20022202,
00221120, //26

10220020,
10221120,
10220020,
10221120,

11201120,
10221022,
10220020,
10221120, //34

10221020,
10221120,
10221022,
10001022,
10221010,
40401012,

10011102,
00202220,
10011101,
20201220, //44

10011102,
00202220,
10011101,
20201220,

10101221,
02010112,
10102201,
00210120, //52

10101221,
02010212,
10201102,
01201122,

10120212,
01201122,
10220022,
12101010, //60

10220021,
00201210,
12101112,
10201112,

30002002,
00202220,
20022202,
0111, //68

#GOGOSTART
10110022,
10121010,
10110022,
10121212,

11201120,
10201210,
10110020,
00212010, //76

10220010,
10121020,
10121210,
1011,
10101011,
00001012, //82

30011100,
10101220,
30011100,
10101220,

30011100,
1133,
30040000,
#GOGOEND
01121112, //90

04,
0,
0,
0,
0, //95
#END


COURSE:Hard
LEVEL:3

STYLE:SINGLE
BALLOON:
SCOREINIT:650,3260
SCOREDIFF:190

#START
0,
0,

30011101,
00102220,
30011101,
0100,

10102201,
01022000,
10102201,
0, //10

10102201,
01022000,
10101102,
0011,

10200011,
0202,
10200011,
0202, //18

10200011,
0202,
1202,
1211,

30000002,
0200,
20022202,
0200, //26

10220020,
10220020,
10220020,
10220020,

12,
1212,
10220020,
10220020, //34

10220020,
10220020,
10221022,
1011,
10101011,
0011,

10011101,
00102220,
10011102,
0200, //44

10011102,
00202220,
10011102,
0200,

10102201,
02011000,
10102201,
0, //52

10102201,
02011000,
10102202,
0011,

10200011,
0202,
10200011,
0202, //60

10200011,
0202,
1202,
1211,

30000002,
0200,
20022202,
0111, //68

#GOGOSTART
10110010,
0111,
10110010,
0121,

20110010,
10011010,
10110010,
0221, //76

10220010,
11,
1212,
1011,
10101011,
0011, //82

30011001,
00101110,
30011001,
00101110,

30011001,
0133,
30040000,
#GOGOEND
01112222, //90

04,
0,
0,
0,
0, //95
#END

STYLE:DOUBLE
BALLOON:
SCOREINIT:640,3260
SCOREDIFF:190

#START P1
0,
0,

30011101,
00102220,
30011101,
0100,

10102201,
01022000,
10102201,
0, //10

10102201,
01022000,
10101102,
0011,

10200011,
0202,
10200011,
0202, //18

10200011,
0202,
1202,
1211,

30000002,
0200,
20022202,
0111, //26

10110010,
0111,
10110010,
0121,

20110010,
10011010,
10110010,
0221, //34

10220010,
11,
1212,
1011,
10101011,
0011,

10011101,
00102220,
10011102,
0200, //44

10011102,
00202220,
10011102,
0200,

10102201,
02011000,
10102201,
0, //52

10102201,
02011000,
10102202,
0011,

10200011,
0202,
10200011,
0202, //60

10200011,
0202,
1202,
1211,

30000002,
0200,
20022202,
0200, //68

#GOGOSTART
10220020,
10220020,
10220020,
10220020,

12,
1212,
10220020,
10220020, //76

10220020,
10220020,
10221022,
1011,
10101011,
0011, //82

30011001,
00101110,
30011001,
00101110,

30011001,
0133,
30040000,
#GOGOEND
01112222, //90

04,
0,
0,
0,
0, //95
#END

BALLOON:
SCOREINIT:640,3260
SCOREDIFF:190

#START P2
0,
0,

30011101,
00102220,
30011101,
0100,

10102201,
01022000,
10102201,
0, //10

10102201,
01022000,
10101102,
0011,

10200011,
0202,
10200011,
0202, //18

10200011,
0202,
1202,
1211,

30000002,
0200,
20022202,
0200, //26

10220020,
10220020,
10220020,
10220020,

12,
1212,
10220020,
10220020, //34

10220020,
10220020,
10221022,
1011,
10101011,
0011,

10011101,
00102220,
10011102,
0200, //44

10011102,
00202220,
10011102,
0200,

10102201,
02011000,
10102201,
0, //52

10102201,
02011000,
10102202,
0011,

10200011,
0202,
10200011,
0202, //60

10200011,
0202,
1202,
1211,

30000002,
0200,
20022202,
0111, //68

#GOGOSTART
10110010,
0111,
10110010,
0121,

20110010,
10011010,
10110010,
0221, //76

10220010,
11,
1212,
1011,
10101011,
0011, //82

30011001,
00101110,
30011001,
00101110,

30011001,
0133,
30040000,
#GOGOEND
01112222, //90

04,
0,
0,
0,
0, //95
#END


COURSE:Normal
LEVEL:4

STYLE:SINGLE
BALLOON:
SCOREINIT:720,4240
SCOREDIFF:233

#START
0,
0,

10011001,
0100,
10011001,
0100,

10102201,
01010000,
10001101,
0, //10

10102201,
01010000,
10001102,
0011,

10000011,
0101,
10000011,
0101, //18

10000011,
0101,
11,
1110,

30000002,
0200,
20022002,
0200, //26

20220000,
20220000,
10220000,
10220000,

12,
1212,
10220000,
1212, //34

10220000,
1212,
11,
1011,
10101011,
0011,

10011001,
0100,
10011001,
0100, //44

10011002,
0200,
10011002,
0,

10102201,
02010000,
10002201,
0, //52

10102201,
02010000,
10002202,
0011,

20000011,
0201,
10000011,
0201, //60

10000011,
0201,
11,
1110,

30000002,
0200,
2,
0111, //68

#GOGOSTART
10110000,
0111,
10110000,
0111,

12,
11,
10110000,
0221, //76

10220000,
0111,
12,
1011,
10101011,
0011, //82

10011000,
3011,
10011000,
3011,

10011000,
3011,
10050000,
#GOGOEND
000008000000000000000000000000000000000000000000, //90

04,
0,
0,
0,
0, //95
#END

STYLE:DOUBLE
BALLOON:
SCOREINIT:720,4240
SCOREDIFF:230

#START P1
0,
0,

10011001,
0100,
10011001,
0100,

10102201,
01010000,
10001101,
0, //10

10102201,
01010000,
10001102,
0011,

10000011,
0101,
10000011,
0101, //18

10000011,
0101,
11,
1110,

30000002,
0200,
2,
0111, //26

10110000,
0111,
10110000,
0111,

12,
11,
10110000,
0221, //34

10220000,
0111,
11,
1011,
10101011,
0011,

10011001,
0100,
10011001,
0100, //44

10011002,
0200,
10011002,
0,

10102201,
02010000,
10002201,
0, //52

10102201,
02010000,
10002202,
0011,

20000011,
0201,
10000011,
0201, //60

10000011,
0201,
11,
1110,

30000002,
0200,
20022002,
0200, //68

#GOGOSTART
20220000,
20220000,
10220000,
10220000,

12,
1212,
10220000,
1212, //76

10220000,
1212,
12,
1011,
10101011,
0011, //82

10011000,
3011,
10011000,
3011,

10011000,
3011,
10050000,
#GOGOEND
000008000000000000000000000000000000000000000000, //90

04,
0,
0,
0,
0, //95
#END

BALLOON:
SCOREINIT:720,4240
SCOREDIFF:230

#START P2
0,
0,

10011001,
0100,
10011001,
0100,

10102201,
01010000,
10001101,
0, //10

10102201,
01010000,
10001102,
0011,

10000011,
0101,
10000011,
0101, //18

10000011,
0101,
11,
1110,

30000002,
0200,
20022002,
0200, //26

20220000,
20220000,
10220000,
10220000,

12,
1212,
10220000,
1212, //34

10220000,
1212,
11,
1011,
10101011,
0011,

10011001,
0100,
10011001,
0100, //44

10011002,
0200,
10011002,
0,

10102201,
02010000,
10002201,
0, //52

10102201,
02010000,
10002202,
0011,

20000011,
0201,
10000011,
0201, //60

10000011,
0201,
11,
1110,

30000002,
0200,
2,
0111, //68

#GOGOSTART
10110000,
0111,
10110000,
0111,

12,
11,
10110000,
0221, //76

10220000,
0111,
12,
1011,
10101011,
0011, //82

10011000,
3011,
10011000,
3011,

10011000,
3011,
10050000,
#GOGOEND
000008000000000000000000000000000000000000000000, //90

04,
0,
0,
0,
0, //95
#END


COURSE:Easy
LEVEL:4

STYLE:SINGLE
BALLOON:
SCOREINIT:600,5380
SCOREDIFF:215

#START
0,
0,

1,
1100,
1,
1100,

11,
1110,
11,
2, //10

11,
1110,
11,
2011,

1001,
0101,
1001,
0101, //18

1001,
0101,
11,
1011,

1,
2200,
2,
2200, //26

22,
2220,
12,
1220,

12,
12,
22,
0221, //34

1001,
1,
11,
1011,
1011,
0011,

11,
1100, 
11,
1100, //44

11,
2200,
11,
2,

11,
1220,
1220,
1, //52

11,
1220,
1220,
2011,

1001,
0101,
1001,
0101, //60

1001,
0101,
11,
1011,

1,
2200,
2,
0111, //68

#GOGOSTART
1100,
0111,
1100,
0112,

22,
21,
11,
0111, //76

0101,
0112,
22,
1011,
11,
1011, //82

13,
0011,
13,
0011,

13,
0011,
15,
#GOGOEND
000000000008000000000000000000000000000000000000, //90

04,
0,
0,
0,
0, //95
#END

STYLE:DOUBLE
BALLOON:
SCOREINIT:600,5380
SCOREDIFF:215

#START P1
0,
0,

1,
1100,
1,
1100,

11,
1110,
11,
2, //10

11,
1110,
11,
2011,

1001,
0101,
1001,
0101, ///18

1001,
0101,
11,
1011,

1,
2200,
2,
0111, //26

1100,
0111,
1100,
0112,

22,
21,
11,
0221, //34

1001,
1,
11,
1011,
1011,
0011,

11,
1100,
11,
1100, //44

11,
2200,
11,
2,

11,
1220,
1220,
1, //52

11,
1220,
1220,
2011,

1001,
0101,
1001,
0101, //60

1001,
0101,
11,
1011,

1,
2200,
2,
2200, //68

#GOGOSTART
22,
2220,
12,
1220,

12,
12,
22,
0111, //76

0101,
0112,
22,
1011,
11,
1011, //82

13,
0011,
13,
0011,

13,
0011,
15,
#GOGOEND
000000000008000000000000000000000000000000000000, //90

04,
0,
0,
0,
0, //95
#END

BALLOON:
SCOREINIT:600,5380
SCOREDIFF:215

#START P2
0,
0,

1,
1100,
1,
1100,

11,
1110,
11,
2, //10

11,
1110,
11,
2011,

1001,
0101,
1001,
0101, //18

1001,
0101,
11,
1011,

1,
2200,
2,
2200, //26

22,
2220,
12,
1220,

12,
12,
22,
0221, //34

1001,
1,
11,
1011,
1011,
0011,

11,
1100, 
11,
1100, //44

11,
2200,
11,
2,

11,
1220,
1220,
1, //52

11,
1220,
1220,
2011,

1001,
0101,
1001,
0101, //60

1001,
0101,
11,
1011,

1,
2200,
2,
0111, //68

#GOGOSTART
1100,
0111,
1100,
0112,

22,
21,
11,
0111, //76

0101,
0112,
22,
1011,
11,
1011, //82

13,
0011,
13,
0011,

13,
0011,
15,
#GOGOEND
000000000008000000000000000000000000000000000000, //90

04,
0,
0,
0,
0, //95
#END