//TJADB Project
TITLE:Aloft in the wind
TITLEJA:Aloft in the wind
SUBTITLE:--Qutabire
SUBTITLEJA:Qutabire
BPM:201
WAVE:Aloft in the wind.ogg
OFFSET:-1.633
DEMOSTART:62.230

COURSE:Edit
LEVEL:10
BALLOON:9
SCOREINIT:1280
SCOREDIFF:0

#START
#MEASURE 6/4
#SCROLL 0.5
100011100020102020102020,
101120001020102010002222,
100011100020100020102020,
101120102020100020202222, //4

100011100020100020102020,
112020112020100020102210,
100020101011200020101000,
102020101111201010002222, //8

100020101011200010001000,
101210100010200010002210,
100020101011200010001000,
101210100010200010201011, //12

202220101000200010002210,
101210100010200010002220,
102210100010200022201010,
101210100010200022201111, //16

#SCROLL 0.66
100011101020100010001122,
100010001020102220201111,
100011101020100011201000,
102210001020100010201111, //20

100011101020100010001020,
112010221020100010201122,
111011121010111011121010,
111211121111
#SCROLL 1.5
400000000000,

#SCROLL 0.75
300020301220301220302010,
100000200000100000400000222200100200,
300020101110201210221020,
101110202010201022201020, //28

#MEASURE 3/4
#SCROLL 1
5,
000000000008000000200000100100200000, 
102010201022,
101010201220, //32

112010121020,
102210201220,
101012101011,
202210221122, //36

101010221122,
102212221222,
121212122222,
708, //40

#SCROLL 1.1
#GOGOSTART
110020101120,
110020101120,
122020112010,
110010201222, //44

101210101120,
102120102220,
122012201220,
122112211222, //48

100210201220,
100210201220,
122020112010,
100210201222, //52

111211121112,
112211221122,
100200100200100200100200101010101010,
2, //56
#GOGOEND

#MEASURE 6/4
#SCROLL 0.75
101210300010101210300010,
101210300010401040104010,
101210300010101210300010,
100000100200100000300000000000100000400000100000400000100000400000202020, //60

101210300010101210300010,
101210300010400000000000,
100000200222100200200000100000100000,
100000200000200200100000100000101010200000000000000000000000000000000000, //64

#SCROLL 1
100000000000200200200000100000101010200000000000100000000000200000000000, 
100000100200100000101010100000100000200000000000100000000000200200200000,
100000200200200000200000100000101010200000000000100000000000200000000000,
100000100200100000102020100000100000200000000000100000200000100100100100, //68

100000000000200200200000100000101010200000000000100000000000200200200000, 
100000100200100000101010100000100000200000000000100000000000200200200000,
100000200200200000200000100000101010200000000000100000200200100000100000,
100000100200100000102020100000100000202020200000100000000000200000000000, //72

300221222121,
220000000220000000110000000220000000,
110000000000000000000000000000000000,
222000000000000000000000000000000000,
0, //77
#END


COURSE:Oni
LEVEL:7
BALLOON:6,6,45
SCOREINIT:2250
SCOREDIFF:0

#START
#MEASURE 6/4
#SCROLL 0.5
101102102011,
101011100011,
101102101020,
101011100200, //4

101102102011,
101101102020,
101221,
101110201020, //8

101110201010,
111010100010200010001010,
101110201010,
111010100010200000001010, //12

101110201010,
111010100010200010001010,
101110201010,
111010100010700000008000, //16

#SCROLL 0.66
101102101011,
100010001020100010001110,
101102101020,
101022101011, //20

101102101012,
101102201020,
111111111111,
500000000000000000000000000000000008000000000000000000000000000000000000, //24

#SCROLL 0.75
302122122300,
121421,
302122111400,
101111200111, //28

#MEASURE 3/4
#SCROLL 1
101012,
101012,
101012,
101212, //32

101202,
101012,
102101,
200010101110, //36

101111,
212121,
500000000000000000000000000008000000,
708, //40

#GOGOSTART
100011101020,
100011101020,
101202,
100011101000, //44

101202,
112,
121,
200022202020, //48

100011102020,
100011102020,
101202,
100011101000, //52

111111,
111011101110,
500000000000000000000000000000000008,
0, //56
#GOGOEND

#MEASURE 6/4
#SCROLL 0.75
111300111300,
111300404040,
111300111300,
111300404040, //60

111300111300,
111300400000,
101202,
500000000000000000000000000000000000000000000008000000000000000000000000, //64

#SCROLL 1
101110201010,
111010100010200010001010,
101110201010,
111010100010200010001010, //68

101110201010,
111010100010200010001000,
101110201010,
111010100010200000000000, //72

7,
0,
8,
2,
0, //77
#END


COURSE:Hard
LEVEL:6
BALLOON:48
SCOREINIT:3480
SCOREDIFF:0

#START
#MEASURE 6/4
#SCROLL 0.5
101101100000,
101011100000,
101101101020,
102011100000,

101101100000,
101101102020,
101201,
101210, //8

101201,
101100201000,
101200,
101100201000,

101200,
101100201000,
101200,
101100200000, //16

#SCROLL 0.66
101101100011,
101011100000,
101101101020,
101011100000,

101101100011,
101101101010,
101110101110,
500000000000000000000000000000000008000000000000000000000000000000000000, //24

#SCROLL 0.75
3333,
121420,
3314,
100110200220,

#MEASURE 3/4
#SCROLL 1
1,
100011,
101011,
101, //32

1,
101,
11,
1,

112,
112,
500000000000000000000000000000000008,
0, //40

#GOGOSTART
101012,
101012,
101101,
101,

101101,
112,
121,
2, //48

101012,
101012,
101101,
101,

111020,
111120,
500000000000000000000000000000000008,
0, //56
#GOGOEND

#MEASURE 6/4
#SCROLL 0.75
111300111300,
111300404040,
111300111300,
111300404040,

111300111300,
111300400000,
12,
500000000000000000000000000000000000000000000008000000000000000000000000, //64

#SCROLL 1
101200,
101100201000,
101200,
101100201000,

101200,
101100201000,
101200,
101100200000, //72

7,
0,
0,
8,
0, //77
#END


COURSE:Normal
LEVEL:5
BALLOON:34
SCOREINIT:5970
SCOREDIFF:0

#START
#MEASURE 6/4
#SCROLL 0.5
1120,
110100,
1120,
111100,

1120,
1110,
5,
000000000000000000000000000000000008000000000000000000000000000000000000, ///8

12,
1120,
12,
1120,

12,
1120,
12,
1120, //16

#SCROLL 0.66
1110,
111100,
1110,
111100,

1110,
2220,
111111,
500000000000000000000000000000000008000000000000000000000000000000000000, //24

#SCROLL 0.75
3330,
101400,
3310,
1120,

#MEASURE 3/4
#SCROLL 1
1,
101,
110,
101, //32

1,
1,
1,
1,

5,
0,
000000000000000000000000000000000008,
0, //40

#GOGOSTART
101,
101,
11,
101,

11,
110,
110,
2, //48

101,
101,
11,
101,

111,
111,
500000000000000000000000000000000008,
0, //56
#GOGOEND

#MEASURE 6/4
#SCROLL 0.75
1313,
100300404040,
1313,
100300404040,

1313,
100300400000,
12,
500000000000000000000000000000000000000000000008000000000000000000000000, //64

#SCROLL 1
12,
1120,
12,
1120,

101200,
1120,
101200,
1120, //72

7,
0,
0,
8,
0, //77
#END


COURSE:Easy
LEVEL:3
BALLOON:28
SCOREINIT:9450
SCOREDIFF:0

#START
#MEASURE 6/4
#SCROLL 0.5
12,
1,
12,
11,

12,
11,
5,
000000000000000000000000000000000008000000000000000000000000000000000000, //8

1,
11,
1,
12,

1,
11,
1,
12, //16

#SCROLL 0.67
11,
101100,
11,
101100,

11,
22,
101101,
500000000000000000000000000000000008000000000000000000000000000000000000, //24

#SCROLL 0.75
33,
1,
31,
1,

#MEASURE 3/4
#SCROLL 1
1,
0,
1,
1, //32

1,
1,
1,
1,

5,
0,
000000000000000000000000000000000008,
0, //40

#GOGOSTART
1,
1,
1,
1,

2,
2,
220,
2, //48

1,
1,
1,
1,

101,
101,
500000000000000000000000000000000008,
0, //56
#GOGOEND

#MEASURE 6/4
#SCROLL 0.75
1313,
1340,
1313,
1340,

1313,
1340,
1,
500000000000000000000000000000000000000000000008000000000000000000000000, //64

#SCROLL 1
1,
11,
1,
12,

1,
11,
1,
12, //72

7,
0,
0,
8,
0, //77
#END

