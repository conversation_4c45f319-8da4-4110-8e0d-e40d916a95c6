//TJADB Project
TITLE:Aosagi no Hi
TITLEJA:蒼鷺之火
SUBTITLE:--BlackY
SUBTITLEJA:BlackY
BPM:174
WAVE:A<PERSON><PERSON> no Hi.ogg
OFFSET:-1.639
DEMOSTART:89.914

//shinuchi: 4840/3100/2000/1390/1020

COURSE:Edit
LEVEL:10
BALLOON:20
SCOREINIT:390,1030
SCOREDIFF:93

#START
10110110,
1,
30110110,
30002202, //4

100000200200202020200000100000002000100020002000,
100000001000000010202020100000000000202020100000,
100000200200202020200000100100100000100000202020,
100000100000100000200200100020002000100020002000, //8

1001100110011022,
1010200211002012,
1010210020121020,
100100100000100200200200000200200200202020200000, //12

1201120120120122,
1201120120120122,
1222,
202020200200200000200100100100100000100000000000, //16

1222101121101220,
1122112211221120,
100200200000100100200000202020100000100200000200,
100100100000100200000200500000000008000000000000, //20

1222101121101220,
1122112211221120,
2111211121121121,
1121211211221122, //24

1021102112101210,
1021101021121120,
1021101021121120,
100100200200101010200200200200100100200200100000, //28

1212102112101210,
1021101021121120,
100200102020200000000000100200102020200000000000,
200200100100200200101010200200200200200200200000, //32

300
#SCROLL 0.66
000102222,
100000002000200200200200100000200000200200200200,
101010002000500008000000400000001000200020002000,
100020002000100020202000101010100200100100100100, //36

000100202020100020002020,
100010002000100000000100100000100000100010001000,
100000002000100020202000100010101000200020002000,
100000200200202020200000100200200000100200100000,
1010222270000000,
#SCROLL 0.86
0008, //42

102222100222,
100000002000101010000000100000002000200200200200,
100020001000200010002000100000002000100100100100,
100000002000100000100100100000001000100010001000,
#SCROLL 1.5
3,
#SCROLL 1.33
3, //48

#SCROLL 1
3,
2222,
200222,

200020002000220020002000, //52

11211212,
11212212,
1111221211112212,
500000000000000000000000000000000000000008000000, //56

1001100110011022,
1010100212021111,
1021021020120120,
1022111110120120, //60

1221221212212212,
100200200100200200100200100100200200101010101010,
100000202222,
100000200200202020200000100200200200100000000000, //64

#GOGOSTART
1122012011220120,
1122012022110210,
1221021012210210,
100200200100000200100000100000200200202020200000, //68

1122012011220120,
1122012022110210,
1221221210221111,
2212212212210212, //72

0210102210221220,
1122112211201200,
100100202020100100202020100100200200100100200200,
100000202020200200200000200200202020200000200000, //76

1122112111221121,
1122121211221212,
100100200200100100200200101010200200101010200200,
1004, //80
#GOGOEND

1021102112101210,
1021101021121120,
1021101021121120,
100100200200101010200200200200100100200200100000, //84

1212102112101220,
1021101021121120,
100200102020200000000000100200102020200000000000,
200200100100200200101010200200200200200200200000, //88

3222,
2222,
1222,
200000000000200000000022, //92

1,
0,
0,
0,
0,
0, //98
#END


COURSE:Oni
LEVEL:9
BALLOON:14,13,18,13
SCOREINIT:500,1460
SCOREDIFF:123

#START
10110110,
1,
30110110,
32, //4

100000200200200200200000200000000000100020002000,
100000001000000010000000200000200200200000000000,
1022222020001022,
200200100000102020100000, //8

1111,
1020100011001000,
1010110010011010,
700000000000000000000000000000000000000008000000, //12

1010210010012010,
1010210011221122,
2222,
2000100011101000, //16

1000102210221020,
1120112011201000,
1022102210221010,
1122120210000000, //20

1000102210221020,
1120112011201000,
1022102210210210,
2010100100112222, //24

1010111022101020,
1010111020120120,
1010111020120120,
1122112222101000, //28

1010111022101020,
1010111020120120,
1212100012121000,
7008, //32

300
#SCROLL 0.66
000102200,
1000222010002222,
100100400222,
100000000000100020002000100000000000100100100100, //36

000100202020100020002020,
102100100111,
102102100222,
1000200010020020,
1010222270000000,
#SCROLL 0.86
0008, //42

102222100222,
100000001000100000000000100000000000200200200200,
100000001000000010000000100000002000100100100100,
100100100111,
#SCROLL 1.5
3,
#SCROLL 1.33
3, //48

#SCROLL 1
3,
2222,
200222,
222222, //52

10211202,
10212210,
1111201011112010,
500000000000000000000000000000000000000008000000, //56

1111,
1020100011001111,
1001001020020020,
1020111110020020, //60

1021021010210210,
100000200100000200100000500000000000000000000008, 
0,
2000200020221000, //64

#GOGOSTART
1011022010110220,
1011022020220110,
1011022010110220,
1011022010222000, //68

1011022010110220,
1011022020220110,
1021021010001111,
2010010010010010, //72

0010202210110220,
1011022011201200,
1122112211221122,
1022202020222000, //76

1122112011221120,
1122121011221210,
1122112212221222,
1004, //80
#GOGOEND

1010111022101020,
1010111020120120,
1010111020120120,
1122112222101000, //84

1010111022101020,
1010111020120120,
1212100012121000, 
7008, //88

3222,
2222,
1222,
200000000000200000000022, //92

1,
0,
0,
0,
0,
0, //98
#END


COURSE:Hard
LEVEL:7
BALLOON:11,10,16,25,10
SCOREINIT:560,2120
SCOREDIFF:140

#START
10110010,
1,
30110010,
32, //4

100000000000200000200200200000000000100010001000,
111200,
1000202220001000,
200200100000101010100000, //8

1111,
1112,
11101111,
700000000000000000000000000000000000000008000000, //12

1000200010102220,
1000200010102220,
1,
2110, //16

1000100011101000,
1110111011101000,
1022202220222000,
1011100010000000, //20

1000100011101000,
1110111011101000,
1022202220020020,
000000500000000000000000000000000008000000000000, //24

1000111011101000,
1000111020020020,
1000111020020020,
1111101111101000, //28

1000111011101000,
1000111020020020,
12101210,
7008, //32

3
#SCROLL 0.66
012,
10201022,
14,
100111100100, //36

000111102020,
100100100111,
100100100111,
11,
1270,
#SCROLL 0.86
0008, //42

100222200222,
2112,
111100,
100100100111,
#SCROLL 1.5
3,
#SCROLL 1.33
3, //48

#SCROLL 1
3,
2222,
22,
22, //52

10101101,
10111110,
1110101011101010,
500000000000000000000000000000000000000008000000, //56

1111,
1000100010001110,
7,
000000000000000000000000080000000000000000000000, //60

1001001020020020,
100000000100000000100000500000000000000000000008, 
0,
0, //64

#GOGOSTART
1001022010010220,
1001022010020000,
1001022010010220,
1001022010000000, //68

1001022010010220,
1001022010020000,
5,
000000000000000000000008000000000000000000000000, //72

0010102011101000,
1010102011101000,
1011101110111011,
1000200020222000, //76

1111100011111000,
2222200022222000,
1111111022222220,
1004, //80
#GOGOEND

1000111011101000,
1000111020020020,
1000111020020020,
1111101111101000, //84

1000111011101000,
1000111020020020,
12101210,
7008, //88

3022,
2022,
1022,
22, //92

1,
0,
0,
0,
0,
0, //98
#END


COURSE:Normal
LEVEL:6
BALLOON:8,7,11,14,7
SCOREINIT:660,3310
SCOREDIFF:180

#START
1001,
1,
3,
3, //4

500000000000000000000000000008000000000000000000,
12,
500000000000000000000000000008000000000000000000,
0110, //8

1111,
1110,
10101110,
700000000000000000000000000000000000000008000000, //12

11201110,
11201110,
22,
2, //16

10101210,
10121210,
10201220,
11101000, //20

10101210,
10121210,
1222,
000000500000000000000000000000000008000000000000, //24

11101010,
11201010,
11201010,
11101000, //28

11101010,
11201010,
11101110,
7008, //32

3
#SCROLL 0.66
010,
11,
14,
1110, //36

0110,
1110,
1110,
11,
17,
#SCROLL 0.86
0008, //42

1212,
1110,
500000000000000000000008000000000000000000000000,
1110,
#SCROLL 1.5
3,
#SCROLL 1.33
3, //48

#SCROLL 1
3,
2220,
22,
2, //52

1110,
10101110,
11111111,
500000000000000000000000000000000000000008000000, //56

1111,
1110,
7,
000000000000000000000000080000000000000000000000, //60

1001000020020000,
100000000100000000000000500000000000000000000008, 
0,
0, //64

#GOGOSTART
11101110,
11102000,
11101110,
11101000, //68

11101110,
11102000,
5,
000000000000000000000008000000000000000000000000, //72

00101120,
11101120,
11221122,
10202220, //76

11221122,
12121212,
11112222,
1004, //80
#GOGOEND

11101010,
11201010,
11201010,
11101000, //84

11101010,
11201010,
11101110,
7008, //88

3022,
22,
1022,
22, //92

1,
0,
0,
0,
0,
0, //98
#END


COURSE:Easy
LEVEL:4
BALLOON:5,5,9,11,5
SCOREINIT:570,5340
SCOREDIFF:173

#START
1,
0,
3,
3, //4

500000000000000000000000000008000000000000000000,
02,
500000000000000000000000000008000000000000000000,
02, //8

11,
11,
10101110,
7008, //12

1110,
1110,
2,
2, //16

1011,
1110,
1111,
1110, //20

1011,
1110,
11,
000000500000000000000000000008000000000000000000, //24

1110,
1120,
1120,
1110, //28

1110,
1120,
33,
7008, //32

3
#SCROLL 0.66
000,
11,
04,
11, //36

01,
11,
1,
11,
17,
#SCROLL 0.86
0008, //42

11,
22,
500000000000000000000008000000000000000000000000,
11,
#SCROLL 1.5
3,
#SCROLL 1.33
3, //48

#SCROLL 1
3,
22,
2,
2, //52

1110,
1110,
10101110,
500000000000000000000000000008000000000000000000, //56

11,
11,
7,
000000000000000000000000080000000000000000000000, //60

12,
12,
0,
0, //64

#GOGOSTART
1111,
1120,
1111,
2220, //68

1111,
1120,
5,
000000000000000000000008000000000000000000000000, //72

0110,
1110,
10101110,
1120, //76

10101110,
10101110,
11101110,
1, //80
#GOGOEND

1110,
1120,
1120,
1110, //84

1110,
1120,
33,
7008, //88

3,
22,
1,
22, //92

1,
0,
0,
0,
0,
0, //98
#END