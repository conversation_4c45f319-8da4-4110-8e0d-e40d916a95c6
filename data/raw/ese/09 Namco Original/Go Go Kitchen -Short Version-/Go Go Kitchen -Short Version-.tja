//TJADB Project
TITLE:Go Go Kitchen -AC1 Audio/Chart-
TITLEJA:ゴー・ゴー・キッチン
SUBTITLE:--<PERSON>
SUBTITLEJA:AC1音源・譜面 太鼓の達人オリジナル曲
BPM:206
WAVE:Go Go Kitchen -Short Version-.ogg
OFFSET:-1.524
DEMOSTART:5.019

//Lost info: Existence of DP 2P chart differences in #N and #E?

COURSE:Hard
LEVEL:5

STYLE:SINGLE
BALLOON:
SCOREINIT:600
SCOREDIFF:120

#START
0,
0,
0, //3

1100101010101010,
0011001000101010,
0011002010101010,
0,

1100101010101010,
0011001000101010,
0011002010101010,
0, //11

#SECTION
#BRANCHSTART p,70,80
#N
10120101,
00001011,
10120101,
0,

10120101,
00001011,
10120101,
0, //n19

#E
10120101,
02201011,
10120101,
0,

10120101,
02201011,
10120101,
0, //e19

#M
10120101,
02201011,
10120101,
0,

10120101,
02201011,
10120101,
0, //m19

#SECTION
#BRANCHSTART p,70,80
#N
10111011,
0111,
10111011,
0111,

10111011,
0111,
10111011,
0111, //n27

#E
1000101010001111,
0111,
1000101010001111,
0111,

1000101010001111,
0111,
1000101010001111,
0111, //e27

#M
1000101010001111,
0111,
1000101010001111,
0111,

1000101010001111,
0111,
1000101010001111,
0111, //m27

#SECTION
#BRANCHSTART p,70,80
#N
1000101010001111,
0111,
1000101010001111,
0111,

1000101010001111,
0111,
1000101010001111,
0, //n35

#E
1000101010001111,
0111,
1000101010001111,
0111,

1000101010001111,
0111,
1000101010001111,
0, //e35

#M
1020101010201111,
0111,
1020101010201111,
0111,

1020101010201111,
0111,
1020101010201111,
0, //m35

#SECTION
#BRANCHSTART p,70,80
#N
11020102,
0002,
11020102,
0002,

11020102,
0002,
11020102,
0002, //n43

#E
10121102,
0202,
10121102,
0202,

10121102,
0202,
10121102,
0202, //e43

#M
10121102,
01120112,
10121102,
01120112,

10121102,
01120112,
10121102,
01120112, //m43

#SECTION
#BRANCHSTART p,70,80
#N
10121102,
0002,
10121102,
0002,

10121102,
0002,
10121102,
0, //n51

#E
11121102,
0202,
11121102,
0202,

11121102,
0202,
11121102,
0, //e51

#M
11121102,
01120112,
11121102,
01120112,

11121102,
01120112,
11121102,
0, //m51

#SECTION
#BRANCHSTART p,70,80
#N
1100200011002000,
0002,
1100200011002000,
0002,

1100200011002000,
0002,
1100200011002000,
0002, //n59

#E
1100200011002000,
00200220,
1100200011002000,
00200220,

1100200011002000,
00200220,
1100200011002000,
00200220, //e59

#M
1100200011002000,
00200120,
1100200011002000,
00200120,

1100200011002000,
00200120,
1100200011002000,
00200120, //m59

#SECTION
#BRANCHSTART p,70,80
#N
0000111000001110,
0202,
0000111000001110,
0202,

0000111000001110,
0202,
0000111000001110,
0, //n67

#E
0000111000001110,
00200220,
0000111000001110,
00200220,

0000111000001110,
00200220,
0000111000001110,
0, //e67

#M
0000111000001110,
00200120,
0000111000001110,
00200120,

0000111000001110,
00200120,
0000111000001110,
0, //m67

#SECTION
#BRANCHSTART p,70,80
#N
11110101,
00001110,
11110101,
00001110,

11110101,
00001110,
11110101,
0, //n75

#E
11120102,
01020110,
11120102,
01020110,

11120102,
01020110,
11120102,
01020000, //e75

#M
11120102,
01020110,
11120102,
01020110,

11120102,
01020110,
11120102,
01020000, //m75

#SECTION
#BRANCHSTART p,70,80
#N
1100200011002000,
1100200011002000,
1100200011002000,
1100200000000000,

1100200011002000,
1100200011002000,
1100200011002000,
1100200000000000, //n83

#E
1100110011002000,
1100110011002000,
1100110011002000,
1100110011002000,

1100110011002000,
1100110011002000,
1100110011002000,
1100110011002000, //e83

#M
1100120011001200,
1100120011001200,
1100120011001200,
1100120011001200,

1100120011001200,
1100120011001200,
1100120011001200,
1100120011001200, //m83

#SECTION
#BRANCHSTART p,70,80
#N
11110100,
11110100,
11110100,
11110100,

11110100,
11110100,
11110100,
11110100, //n91

11110100,
11110100,
0,
0,
0, //n96

#E
11110201,
11110201,
11110201,
11110201,

11110201,
11110201,
11110201,
11110201, //e91

11110201,
11110200,
0,
0,
0, //e96
 
#M
11121112,
11121112,
11121112,
11121112,

11121112,
11121112,
11121112,
11121112, //m91

11121112,
11121112,
0,
0,
0, //m96

#BRANCHEND
#END

STYLE:DOUBLE
BALLOON:
SCOREINIT:600
SCOREDIFF:120

#START P1
0,
0,
0, //3

1100101010101010,
0011001000101010,
0011002010101010,
0,

1100101010101010,
0011001000101010,
0011002010101010,
0, //11

#SECTION
#BRANCHSTART p,70,80
#N
10120101,
00001011,
10120101,
0,

10120101,
00001011,
10120101,
0, //n19

#E
10120101,
02201011,
10120101,
0,

10120101,
02201011,
10120101,
0, //e19

#M
10120101,
02201011,
10120101,
0,

10120101,
02201011,
10120101,
0, //m19

#SECTION
#BRANCHSTART p,70,80
#N
10111011,
0111,
10111011,
0111,

10111011,
0111,
10111011,
0111, //n27

#E
1000101010001111,
0111,
1000101010001111,
0111,

1000101010001111,
0111,
1000101010001111,
0111, //e27

#M
1000101010001111,
0111,
1000101010001111,
0111,

1000101010001111,
0111,
1000101010001111,
0111, //m27

#SECTION
#BRANCHSTART p,70,80
#N
1000101010001111,
0111,
1000101010001111,
0111,

1000101010001111,
0111,
1000101010001111,
0, //n35

#E
1000101010001111,
0111,
1000101010001111,
0111,

1000101010001111,
0111,
1000101010001111,
0, //e35

#M
1020101010201111,
0111,
1020101010201111,
0111,

1020101010201111,
0111,
1020101010201111,
0, //m35

#SECTION
#BRANCHSTART p,70,80
#N
11020102,
0002,
11020102,
0002,

11020102,
0002,
11020102,
0002, //n43

#E
10121102,
0202,
10121102,
0202,

10121102,
0202,
10121102,
0202, //e43

#M
10121102,
01120112,
10121102,
01120112,

10121102,
01120112,
10121102,
01120112, //m43

#SECTION
#BRANCHSTART p,70,80
#N
10121102,
0002,
10121102,
0002,

10121102,
0002,
10121102,
0, //n51

#E
11121102,
0202,
11121102,
0202,

11121102,
0202,
11121102,
0, //e51

#M
11121102,
01120112,
11121102,
01120112,

11121102,
01120112,
11121102,
0, //m51

#SECTION
#BRANCHSTART p,70,80
#N
1100200011002000,
0002,
1100200011002000,
0002,

1100200011002000,
0002,
1100200011002000,
0002, //n59

#E
1100200011002000,
00200220,
1100200011002000,
00200220,

1100200011002000,
00200220,
1100200011002000,
00200220, //e59

#M
1100200011002000,
00200120,
1100200011002000,
00200120,

1100200011002000,
00200120,
1100200011002000,
00200120, //m59

#SECTION
#BRANCHSTART p,70,80
#N
0000111000001110,
0202,
0000111000001110,
0202,

0000111000001110,
0202,
0000111000001110,
0, //n67

#E
0000111000001110,
00200220,
0000111000001110,
00200220,

0000111000001110,
00200220,
0000111000001110,
0, //e67

#M
0000111000001110,
00200120,
0000111000001110,
00200120,

0000111000001110,
00200120,
0000111000001110,
0, //m67

#SECTION
#BRANCHSTART p,70,80
#N
11110101,
00001110,
11110101,
00001110,

11110101,
00001110,
11110101,
0, //n75

#E
11120102,
01020110,
11120102,
01020110,

11120102,
01020110,
11120102,
01020000, //e75

#M
11120102,
01020110,
11120102,
01020110,

11120102,
01020110,
11120102,
01020000, //m75

#SECTION
#BRANCHSTART p,70,80
#N
1100200011002000,
1100200011002000,
1100200011002000,
1100200000000000,

1100200011002000,
1100200011002000,
1100200011002000,
1100200000000000, //n83

#E
1100110011002000,
1100110011002000,
1100110011002000,
1100110011002000,

1100110011002000,
1100110011002000,
1100110011002000,
1100110011002000, //e83

#M
1100120011001200,
1100120011001200,
1100120011001200,
1100120011001200,

1100120011001200,
1100120011001200,
1100120011001200,
1100120011001200, //m83

#SECTION
#BRANCHSTART p,70,80
#N
11110100,
11110100,
11110100,
11110100,

11110100,
11110100,
11110100,
11110100, //n91

11110100,
11110100,
0,
0,
0, //n96

#E
11110201,
11110201,
11110201,
11110201,

11110201,
11110201,
11110201,
11110201, //e91

11110201,
11110200,
0,
0,
0, //e96
 
#M
11121112,
11121112,
11121112,
11121112,

11121112,
11121112,
11121112,
11121112, //m91

11121112,
11121112,
0,
0,
0, //m96

#BRANCHEND
#END

BALLOON:
SCOREINIT:600
SCOREDIFF:120

#START P2
0,
0,
0, //3

1100101010101010,
0011001000101010,
0011002010101010,
0,

1100101010101010,
0011001000101010,
0011002010101010,
0, //11

#SECTION
#BRANCHSTART p,70,80
#N
10120101,
00001011,
10120101,
0,

10120101,
00001011,
10120101,
0, //n19

#E
10120101,
02201011,
10120101,
0,

10120101,
02201011,
10120101,
0, //e19

#M
10120101,
02201011,
10120101,
0,

10120101,
02201011,
10120101,
0, //m19

#SECTION
#BRANCHSTART p,70,80
#N
10111011,
0111,
10111011,
0111,

10111011,
0111,
10111011,
0111, //n27

#E
1000101010001111,
0111,
1000101010001111,
0111,

1000101010001111,
0111,
1000101010001111,
0111, //e27

#M
1000101010001111,
0111,
1000101010001111,
0111,

1000101010001111,
0111,
1000101010001111,
0111, //m27

#SECTION
#BRANCHSTART p,70,80
#N
1000101010001111,
0111,
1000101010001111,
0111,

1000101010001111,
0111,
1000101010001111,
0, //n35

#E
1000101010001111,
0111,
1000101010001111,
0111,

1000101010001111,
0111,
1000101010001111,
0, //e35

#M
1020101010201111,
0111,
1020101010201111,
0111,

1020101010201111,
0111,
1020101010201111,
0, //m35

#SECTION
#BRANCHSTART p,70,80
#N
11020102,
0002,
11020102,
0002,

11020102,
0002,
11020102,
0002, //n43

#E
10121102,
0202,
10121102,
0202,

10121102,
0202,
10121102,
0202, //e43

#M
10121102,
01120112,
10121102,
01120112,

10121102,
01120112,
10121102,
01120112, //m43

#SECTION
#BRANCHSTART p,70,80
#N
10121102,
0002,
10121102,
0002,

10121102,
0002,
10121102,
0, //n51

#E
11121102,
0202,
11121102,
0202,

11121102,
0202,
11121102,
0, //e51

#M
11121102,
01120112,
11121102,
01120112,

11121102,
01120112,
11121102,
0, //m51

#SECTION
#BRANCHSTART p,70,80
#N
1100200011002000,
0002,
1100200011002000,
0002,

1100200011002000,
0002,
1100200011002000,
0002, //n59

#E
1100200011002000,
00200220,
1100200011002000,
00200220,

1100200011002000,
00200220,
1100200011002000,
00200220, //e59

#M
1100200011002000,
00200120,
1100200011002000,
00200120,

1100200011002000,
00200120,
1100200011002000,
00200120, //m59

#SECTION
#BRANCHSTART p,70,80
#N
0000111000001110,
0202,
0000111000001110,
0202,

0000111000001110,
0202,
0000111000001110,
0, //n67

#E
0000111000001110,
00200220,
0000111000001110,
00200220,

0000111000001110,
00200220,
0000111000001110,
0, //e67

#M
0000111000001110,
00200120,
0000111000001110,
00200120,

0000111000001110,
00200120,
0000111000001110,
0, //m67

#SECTION
#BRANCHSTART p,70,80
#N
11110101,
00001110,
11110101,
00001110,

11110101,
00001110,
11110101,
0, //n75

#E
11120102,
01020110,
11120102,
01020110,

11120102,
01020110,
11120102,
01020000, //e75

#M
11120102,
01020110,
11120102,
01020110,

11120102,
01020110,
11120102,
01020000, //m75

#SECTION
#BRANCHSTART p,70,80
#N
1100200011002000,
1100200011002000,
1100200011002000,
1100200000000000,

1100200011002000,
1100200011002000,
1100200011002000,
1100200000000000, //n83

#E
1100110011002000,
1100110011002000,
1100110011002000,
1100110011002000,

1100110011002000,
1100110011002000,
1100110011002000,
1100110011002000, //e83

#M
1200110012001100,
1200110012001100,
1200110012001100,
1200110012001100,

1200110012001100,
1200110012001100,
1200110012001100,
1200110012001100, //m83

#SECTION
#BRANCHSTART p,70,80
#N
11110100,
11110100,
11110100,
11110100,

11110100,
11110100,
11110100,
11110100, //n91

11110100,
11110100,
0,
0,
0, //n96

#E
11110201,
11110201,
11110201,
11110201,

11110201,
11110201,
11110201,
11110201, //e91

11110201,
11110200,
0,
0,
0, //e96
 
#M
12111211,
12111211,
12111211,
12111211,

12111211,
12111211,
12111211,
12111211, //m91

12111211,
12111211,
0,
0,
0, //m96

#BRANCHEND
#END