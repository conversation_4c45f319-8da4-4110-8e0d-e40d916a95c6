//TJADB Project
TITLE:Shunyuu ～happy excursion～
TITLEJA:春遊 ～happy excursion～
SUBTITLE:--<PERSON><PERSON> Found Music Workshop
SUBTITLEJA:～happy excursion～
BPM:140
WAVE:Shunyuu happy excursion.ogg
OFFSET:-2.020
DEMOSTART:17.439

//shinuchi: 4830/4110/2050/1580/1240

COURSE:Edit
LEVEL:8
BALLOON:
SCOREINIT:390,1430
SCOREDIFF:95

#START
4,

1020202220222020,
1020202220222010,
1022202022202010,
1022202022202020,

1020202220222020,
1020202220222010,
1022202022202020,
3030303030302222, //9

#GOGOSTART
1011204011212030,
0040302210212010,
3011204011212030,
0040302210212222,

1011204011212030,
0040302210212010,
2212221222122212,
2030403040222010, //17

4022104012212030,
0040302210212010,
4022104012212030,
0040302210212222,

2022104012212030,
0040302210212010,
1011202120121010,
40403443,
4, //26
#GOGOEND

4000200020222020,
2022202000222020,
2022202020200020,
0000000000222020,

1022201010200020,
1022201010222020,
11211202,
4030304030304022, //34

1010201110102022,
1010201102102020,
1010201110102010,
1010201102112020,

1010201110102022,
1010201102102020,
1010201110102000,
43333444, //42

1021201202102012,
1021201201201211,
1021201202102022,
1011201211202222,

1021201202102012,
1021201201201211,
1021201202102011,
2211221122212222, //50

#GOGOSTART
1011204011212030,
0040302210212010,
3011204011212030,
0040302210212222,

1011204011212030,
0040302210212010,
2212221222122212,
2030403040222010, //58

4022104012212030,
0040302210212010,
4022104012212030,
0040302210212222,

2022104012212030,
0040302210212010,
1011202120121030,
0040403040222020, //66

30303034,
0020202210102000,
1011101040400030,
0010201012112222,

1212204011212030,
0022202020222020,
600000000000000000000000000000000000000000000008,
#GOGOEND
0,
0,
0, //76
#END


COURSE:Oni
LEVEL:7
BALLOON:
SCOREINIT:470,1750
SCOREDIFF:123

#START
0,

1202,
1202,
12202022,
12202022,

1202,
1202,
12202022,
3030303030302222, //9

#GOGOSTART
1011102010102030,
0011102010222010,
1011102010102030,
0011102010112222,

1011102010102030,
0011102010222010,
2211221122112211,
1010201020222010, //17

1022102010202030,
0011102010222010,
1022102010202030,
0011102010112222,

1022102010202030,
0011102010222010,
11212112,
30303333,
4, //26
#GOGOEND

1202,
1202,
10201120,
1000200000222020,

10211202,
1000201010222020,
11211202,
4030304030304011, //34

1000201010102022,
11210122,
10211202,
10210122,

1000201010102022,
11210122,
10211202,
43333444, //42

1011201120112020,
1011201120122010,
1011201220222010,
11200044,

1011201120112020,
1011201120122010,
11211121,
4040404040401111, //50

#GOGOSTART
1011102010102030,
0011102010222010,
1011102010102030,
0011102010112222,

1011102010102030,
0011102010222010,
2211221122112211,
1010201020222010, //58

1022102010202030,
0011102010222010,
1022102010202030,
0011102010112222,

1022102010202030,
0011102010222010,
11212114,
0020201020222000, //66

30303034,
0020201110102000,
1011101040400030,
0010201010112222,

1011102010102030,
0022201020201020,
600000000000000000000000000000000000000000000008,
#GOGOEND
0,
0,
0, //76
#END


COURSE:Hard
LEVEL:6
BALLOON:
SCOREINIT:500,2290
SCOREDIFF:133

#START
0,

1202,
1202,
12202020,
12202020,

1202,
1202,
12202020,
33333333, //9

#GOGOSTART
3011100010102010,
0010100010222000,
1011100010102010,
0010100010111000,

1011100010102010,
0010100010222000,
1111100011111000,
11212020, //17

1011100010202010,
0010100010222000,
1011100010202010,
0010100010111000,

1011100010202010,
0010100010222000,
11212020,
30303333,
4,//26
#GOGOEND

1202,
1202,
10202202,
1000200000222020,

10220020,
10220020,
10221120,
4030304030304011, //34

10201120,
11210120,
10201120,
1010201000111000,

10201120,
11210120,
10201120,
43343340, //42

1010101110102000,
1010101110202000,
1010101110202000,
11200044,

1010101110102000,
1010101110202000,
11211221,
44444040, //50

#GOGOSTART
1011100010102010,
0010100010222000,
1011100010102010,
0010100010111000,

1011100010102010,
0010100010222000,
1111100011111000,
11212020, //58

1011100010202010,
0010100010222000,
1011100010202010,
0010100010111000,

1011100010202010,
0010100010222000,
11212024,
0020400020222000, //66

30303034,
0020200010222000,
1011100040400060,
000000000000000000000008000000000000000000000000,

1011100010102010,
0020200020221000,
600000000000000000000000000000000000000000000008,
#GOGOEND
0,
0,
0, //76
#END


COURSE:Normal
LEVEL:5
BALLOON:8,8
SCOREINIT:750,4660
SCOREDIFF:238

#START
0,

1200,
1200,
1200,
1202,

1200,
1200,
1111,
600000000000000000000000000008000000000000000000, //9

#GOGOSTART
10101003,
01102000,
10101003,
01101000,

10101003,
01102000,
500000000008000000000000500000000008000000000000,
7008, //17

10101003,
01102000,
10101003,
01101000,

10101003,
01102000,
1111,
30303330,
4, //26
#GOGOEND

2200,
2200,
2220,
2,

1200,
1200,
1220,
500000000000000000000000000008000000000000000000, //34

1202,
1202,
10202220,
1202,

1202,
1202,
10202220,
500000000000000000000000000008000000000000000000, //42

11101110,
11101010,
11101110,
1,

11101110,
11101010,
11101110,
500000000000000000000000000008000000000000000000, //50

#GOGOSTART
10101003,
01102000,
10101003,
01101000,

10101003,
01102000,
500000000008000000000000500000000008000000000000,
7008, //58

10101003,
01102000,
10101003,
01101000,

10101003,
01102000,
10101004,
02202000, //66

10101003,
01101000,
10104406,
000000000000000000000008000000000000000000000000,

10101003,
01102220,
600000000000000000000000000000000000000000000008,
#GOGOEND
0,
0,
0, //76
#END


COURSE:Easy
LEVEL:4
BALLOON:5,5
SCOREINIT:530,5520
SCOREDIFF:170

#START
0,

1200,
1200,
1200,
1202,

1200,
1200,
1111,
600000000000000000000000000008000000000000000000, //9

#GOGOSTART
10101003,
0120,
10101003,
0110,

10101003,
0120,
500000000008000000000000500000000008000000000000,
7008, //17

10101003,
0120,
10101003,
0110,

10101003,
0120,
11,
3330,
4, //26
#GOGOEND

22,
22,
2220,
2,

22,
22,
2220,
500000000000000000000000000008000000000000000000, //34

1202,
1202,
1110,
1200,

1202,
1202,
1110,
500000000000000000000000000008000000000000000000, //42

10101110,
1110,
10101110,
1,
10101110,
1110,
10101110,
500000000000000000000000000008000000000000000000, //50

#GOGOSTART
10101003,
0120,
10101003,
0110,

10101003,
0120,
500000000008000000000000500000000008000000000000,
7008, //58

10101003,
0120,
10101003,
0110,

10101003,
0120,
10101004,
0220, //66

10101003,
0110,
10103306,
000000000000000000000008000000000000000000000000,

10101003,
0122,
600000000000000000000000000000000000000000000008,
#GOGOEND
0,
0,
0, //76
#END