//TJADB Project
TITLE:Cross Blue
TITLEJA:クロス・ブルー
SUBTITLE:--Masay<PERSON> feat. Sariyajin
BPM:180
WAVE:Cross Blue.ogg
OFFSET:-1.502
DEMOSTART:51.440

//Shinuchi: 4680/2680/1490/1020


COURSE:Oni
LEVEL:9
BALLOON:
SCOREINIT:340,1040
SCOREDIFF:80

#START
#MEASURE 5/4
11112020001000111120,
11112020001020111120,
11112020001000111120,
#MEASURE 6/4
111120100010001122111110,

#MEASURE 5/4
10002001201000100020,
10002001201000102000,
10002001201000100020,
10002001204000300022, //8

10002001201000100022,
11102001201000102022,
10002001201120100022,
11202001204000300022,

10002011102011102011,
10201011201000112022,
10002001201000100022,
11102001201000102222, //16

10002001201000100022,
11102001201000102022,
10002001201120100022,
11202001204000300022, 

10002011202011202011,
20201011203000300022,
10002001201002101120,
10102010221120112210, //24

#MEASURE 3/4
102000100011,
201120202010,
301120402210,
301120402210,

101120102010,
101120102010,
101120102210,
101120102210,
221022112011,
240300, //34

#GOGOSTART
100020012011,
201021022012,
100020112210,
201122102010,

100020012011,
201021022012,
100020112210,
201122102010, //42

100020012011,
201021022012,
100020112210,
201122102010,

100020012011,
201021022012,
112120112120,
112120112110, //50

100020012011,
201021022012,
100020112210,
201122102010,

100020012011,
201021022012,
111120111120,
111120102010, //58

100020012011,
201021022012,
100020112210,
201122102010,

100020012011,
201021022012,
221210221210,
221210221212, //66
#GOGOEND

101101,
210120,
102101,
210112,

101101,
210120,
120101,
210112, //74

100020012011,
201120102010,
100020012012,
201220102010,

102011,
200011102000,
111120001010,
221122112220,

302302,
300022112220,
312302,
221122112212, //86

#GOGOSTART
100020012011,
201021022012,
100020112210,
201122102010,

100020012011,
201021022012,
100020112210,
201122102010, //94

100020012011,
201021022012,
100020112210,
201122102010,

100020012011,
201021022012,
112120112120,
112120112110, //102

100020012011,
201021022012,
100020112210,
201122102010,

100020012011,
201021022012,
222210222210,
222210102010, //110

100020012011,
201021022012,
100020112210,
201122102010,

100020012011,
201021022021,
111220111220,
111220112210, //118
#GOGOEND

#MEASURE 5/4
30000000000000000011,
20100120102000112010,
10002001201000201022,
20120211221210300000,

#MEASURE 4/4
0,
0, //124
#END


COURSE:Hard
LEVEL:7
BALLOON:
SCOREINIT:390,1570
SCOREDIFF:95

#START
#MEASURE 5/4
3021030300,
3021040400,
3021030300,
#MEASURE 6/4
400000400000000000400000000000400000000000600000000000000008000000000000,

#MEASURE 5/4
1020210102,
1020210120,
1020210102,
1020230300, //8

1010210102,
1010210120,
1010210102,
1010230300,

1021121021,
1210210120,
1010210102,
1010210100,

1010210102,
1010210120,
1010210102,
2101030300,

1021121021,
1210210102,
1010210102,
10002000200011101000, //24

#MEASURE 3/4
500000000000000000000008000000000000,
020101,
303404,
322,

102120,
120210,
212120,
212121,
112112,
030300, //34

#GOGOSTART
112121,
112121,
112112,
112121,

500000000000000000000000000000000008,
002121,
221221,
221212,

112121,
112121,
112112,
112121,

500000000000000000000000000000000008,
002121,
221221,
221212, //50

112121,
112121,
112112,
112121,

500000000000000000000000000000000008,
002121,
221221,
221212,

112121,
112121,
112112,
112121,

500000000000000000000000000000000008,
002121,
221221,
112112, //66
#GOGOEND

110100,
200220,
110102,
020200,

110100,
200220,
120100,
212,

100020022010,
011020,
100020022010,
011021,

101020011010,
200110102001,
101020011010,
500000000000000000000008000000000000,

302302,
300022202220,
110110,
600000000000000000000000000008000000, //86

#GOGOSTART
112121,
112121,
112112,
112121,

111222,
112121,
221221,
221212,

112121,
112121,
112112,
112121,

111222,
112121,
211211,
211212, //102

112121,
112121,
112112,
112121,

111222,
112121,
221221,
221212,

112121,
112121,
112112,
112121,

111222,
112121,
221221,
101020101110, //118
#GOGOEND

#MEASURE 5/4
10001010101000200011,
1101010400,
10002000201000100022,
20201110104000300000,

#MEASURE 4/4
0,
0, //124
#END


COURSE:Normal
LEVEL:6
BALLOON:
SCOREINIT:530,2980
SCOREDIFF:148

#START
#MEASURE 5/4
1000010100,
1000010100,
1000010100,
#MEASURE 6/4
100000000000000000100000000000100000000000500000000000000000000008000000,

#MEASURE 5/4
1020010100,
1020010100,
1020010100,
1020030300, //8

11202,
11202,
11202,
1010200400,

11201,
11201,
11201,
1010200400,

11202,
11202,
1010200002,
0101030400,

11201,
1010200300,
11201,
01111, //24

#MEASURE 3/4
500000000000000000000000000000000008,
02,
33,
33,

122,
102,
122,
122,
122,
030300, //34

#GOGOSTART
102120,
121,
11,
122,

500000000000000000000000000000000008,
022,
11,
122,

102120,
121,
11,
122,

500000000000000000000000000000000008,
022,
33,
304, //50

102120,
121,
11,
122,

102120,
121,
11,
122,

102120,
121,
11,
122,

500000000000000000000000000000000008,
022,
33,
44, //66
#GOGOEND

11,
22,
100102,
020200,

11,
2,
010100,
2,

121,
101,
121,
101,

121,
212,
121,
500000000000000000000008000000000000,

33,
33,
33,
600000000000000000000000000008000000, //86

#GOGOSTART
102120,
121,
11,
122,

102120,
121,
11,
122,

102120,
121,
102120,
122,

500000000000000000000000000000000008,
022,
33,
304, //102

102120,
121,
11,
122,

102120,
121,
11,
122,

102120,
121,
11,
122,

500000000000000000000000000000000008,
022,
33,
600000000000000000000008000000000000, //118
#GOGOEND

#MEASURE 5/4
2022010100,
1011020200,
1022010100,
500000000000000008000000000000300000000000300000000000000000,

#MEASURE 4/4
0,
0, //124
#END


COURSE:Easy
LEVEL:4
BALLOON:3
SCOREINIT:530,5510
SCOREDIFF:170

#START
#MEASURE 5/4
10001,
10001,
10001,
#MEASURE 6/4
100101010000,

#MEASURE 5/4
500000000000000000000000000000000000000000000008000000000000,
10010,
500000000000000000000000000000000000000000000008000000000000,
1000000400, //8

11010,
11000,
11010,
1010000200,

1001001001,
0010010100,
11000,
11000,

11010,
11000,
11010,
0101000400,

1001001001,
0010030300,
11000,
01111, //24

#MEASURE 3/4
500000000000000000000000000000000008,
01,
33,
33,

110,
2,
101,
101,
101,
040400, //34

#GOGOSTART
101,
102,
2,
0,

500000000000000000000000000008000000,
010,
11,
0,

101,
102,
2,
0,

500000000000000000000000000008000000,
010,
33,
3, //50

101,
102,
2,
0,

500000000000000000000000000008000000,
010,
11,
1,

101,
102,
2,
0,

500000000000000000000000000008000000,
010,
33,
44, //66
#GOGOEND

11,
2,
11,
2,

11,
2,
11,
2,

1,
2,
1,
2,

101,
010,
101,
500000000000000000000008000000000000,

33,
33,
33,
700000000008000000, //86

#GOGOSTART
101,
102,
2,
0,

500000000000000000000000000008000000,
010,
11,
0,

101,
102,
2,
0,

500000000000000000000000000008000000,
010,
33,
3, //102

101,
102,
2,
0,

500000000000000000000000000008000000,
010,
11,
1,

101,
102,
2,
0,

500000000000000000000000000008000000,
010,
6,
000000000000000000000000000000000008, //118
#GOGOEND

#MEASURE 5/4
0000010100,
1000020200,
1010000005,
000000000000000008000000000000300000000000300000000000000000,

#MEASURE 4/4
0,
0, //124
#END