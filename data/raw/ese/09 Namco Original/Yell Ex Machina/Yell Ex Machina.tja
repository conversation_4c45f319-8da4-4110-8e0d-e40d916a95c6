//TJADB Project
TITLE:Yell Ex Machina!
TITLEJA:エール・エクス・マキナ！
SUBTITLE:--<PERSON><PERSON><PERSON> prod. <PERSON><PERSON><PERSON> (BNSI)
SUBTITLEJA:まきな prod. 坪井リヒト(BNSI)
BPM:176.5
WAVE:Yell Ex Machina.ogg
OFFSET:-1.113
DEMOSTART:68.703

//Shinuchi: 5110/3830/2330/1230

COURSE:Oni
LEVEL:9
BALLOON:39
SCOREINIT:410,1290
SCOREDIFF:100

#START
0, //1

2,
0,
2,
0,

20202221,
22210222,
20202221,
0, //9

#GOGOSTART
1210221022102222,
1210221022102222,
1210221022112111,
2000202210210210,

1210221022112222,
1210221022112222,
1202101202102111,
200000000000500000000000000000000008000000000000, //17
#GOGOEND

10002221,
0000002220202020,
1000000020222010,
0020202020202222,

1021012010210120,
1021012010210120,
1022122010221220,
3000400030221102, //25

1022102010221020,
0010201010112012,
1022102010221020,
0010201010112012,

1021202012221020,
1021202012121020,
2111211121112030,
0000222211112212, //33

12212212,
1020102220004012,
12212212,
1020102220004012,

10212012,
1000201020002012,
12212212,
0, //41

1020202010021020,
0021012010021020,
1020202010021020,
0021012010021020,

1020101020112010,
1020111020112010,
1112111211121112,
10202220,
22220220, //50

#GOGOSTART
3011202030122020,
3022202010221020,
1111201011122010,
1122201022221020,

3011202030122020,
3022202010221020,
1222201011222010,
1112211221121212, //58

1010001120102011,
2120112020112020,
1010001120102011,
2120112020112020,

1020122210201212,
1020122210201212,
1022102211221212,
#GOGOEND
1002, //66

2,
0,
#BPMCHANGE 160
2,
0001,

1,
0,
#GOGOSTART
1011101120222022,
1011101110221022, //74
#GOGOEND

1,
0,
#GOGOSTART
100000100000100022200020,
101000202100,
#GOGOEND

#BPMCHANGE 212
1,
000000
#GOGOSTART
25,
000000000000000000000000000008000000200000500000,
000000000000000000000000000008000000200000500000,
000000000000000000000000000008000000400000600000,
000000000000000000000000000008000000000000000000, //84
#GOGOEND

#BPMCHANGE 176.5
2,
0,
1111211121112111,
2000202010222222,

20022002,
20020020,
20022002,
2002002002002000, //92

#GOGOSTART
33,
30030222,
1111201011212010,
2011201122112000,
#GOGOEND

3000
#GOGOSTART
400030112020,
3012102010211050,
000000000000000000000000000008000000100200200200,
1022102211221212, //100

1020122210201212,
1020122210201212,
100200200200100000500000000000000000000000000008,
00013001,

6,
000000000000000000000008000000000000000000000000,
20202220,
22220220, //108

#BPMCHANGE 276.5
7,
0,
#BARLINEOFF
#BPMCHANGE 202.3
#MEASURE 3/4
0,
0,
#GOGOEND

8,
#SCROLL 3.67
3,
#SCROLL 1
0,
0,
#MEASURE 4/4
0, //117
#END


COURSE:Hard
LEVEL:6
BALLOON:9,12,12,17
SCOREINIT:570,2590
SCOREDIFF:150

#START
0,
2,
0,
2,
0,

20202220,
22220222,
20202222,
0, //9

#GOGOSTART
1110100010001000,
1110100010001000,
1110100010001000,
2000200010010010,

0010200011102000,
0010200011102000,
700000000000000000000000000000000000080000000000,
4400, //17
#GOGOEND

10000001,
0,
10000001,
0,

1001002010010020,
1001002010010020,
1001002010010020,
3430, //25

1000100010111020,
0010201010112000,
1000100010111020,
0010201010112000,

10221022,
12201220,
1110111011100030,
0400, //33

10012002,
02102040,
10012002,
02102040,

10012012,
10012020,
10012012,
0, //41

1020202010022020,
0010002010022020,
1020202010022020,
0010002010022020,

12112020,
12112020,
500000000000000000000000000000000000000000000008,
0,
0, //50

#GOGOSTART
3232,
30221122,
1110200011102000,
1110201020102000,

3232,
30210121,
1110200011102000,
1110201020102000, //58

10012010,
11102010,
10012010,
1010100020111000,

12101210,
1020101110201000,
7,
#GOGOEND
8002, //66

2,
0,
#BPMCHANGE 160
2,
0001,

1,
0,
#GOGOSTART
1000100010111010,
1011101000200020, //74
#GOGOEND

1,
0,
#GOGOSTART
100100100202,
101000202100,
#GOGOEND

#BPMCHANGE 212
1,
000000
#GOGOSTART
25,
000000000000000000000000000008000000200000500000,
000000000000000000000000000008000000200000500000,
000000000000000000000000000008000000400000600000,
000000000000000000000000000008000000000000000000, //84
#GOGOEND

#BPMCHANGE 176.5
2,
0,
7,
8,

2,
0,
2,
0, //92

#GOGOSTART
33,
30030020,
1110200011102000,
2010201020112000,
#GOGOEND

3
#GOGOSTART
432,
30230306,
000000000000000000000000000008000000000000000000,
1000202011102000, //100

12101210,
1020101110201000,
500000000000000000000000000000000000000000000008,
00013001,

9,
09008000,
0,
0, //108

#BPMCHANGE 276.5
0,
0,
#BARLINEOFF
#BPMCHANGE 202.3
#MEASURE 3/4
0,
0,
#GOGOEND

0,
#SCROLL 3.67
3,
#SCROLL 1
0,
0,
#MEASURE 4/4
0, //117
#END


COURSE:Normal
LEVEL:5
BALLOON:6,10,10,13
SCOREINIT:720,4430
SCOREDIFF:205

#START
0,
2,
0,
2,
0,

2220,
20020000,
20202002,
0, //9

#GOGOSTART
11101000,
11101000,
11101000,
2000200010010010,

0202,
0202,
700000000000000000000000000000000000080000000000,
4400, //17
#GOGOEND

10000001,
0,
10000001,
0,

10022002,
20022002,
20022000,
3430, //25

10101021,
0212,
10101021,
0212,

1210,
1210,
11101003,
0400, //33

10012000,
0024,
10012000,
0024,

10012000,
0022,
10012000,
0, //41

10002002,
01021000,
10002002,
01021000,

11102020,
11102020,
500000000000000000000000000000000000000000000008,
0,
0, //50

#GOGOSTART
33,
3,
10201120,
11202020,

33,
3,
10201120,
11202020, //58

10012000,
11102000,
10012000,
11102000,

10201120,
10201120,
7,
#GOGOEND
8002, //66

2,
0,
#BPMCHANGE 160
2,
0,

0,
0,
#GOGOSTART
1210,
1211, //74
#GOGOEND

0,
0,
#GOGOSTART
1110,
101000202000,
#GOGOEND

#BPMCHANGE 212
0,
000000
#GOGOSTART
05,
000000000000000000000000000008000000000000500000,
000000000000000000000000000008000000000000500000,
000000000000000000000000000008000000000000600000,
000000000000000000000000000008000000000000000000, //84
#GOGOEND

#BPMCHANGE 176.5
0,
0,
7,
8,

2,
0,
2,
0, //92

#GOGOSTART
33,
30030020,
10201120,
2220,
#GOGOEND

3
#GOGOSTART
030,
30030306,
000000000000000000000000000008000000000000000000,
10221020, //100

10201120,
10201120,
500000000000000000000000000000000000000000000008,
03,

9,
09008000,
0,
0, //108

#BPMCHANGE 276.5
0,
0,
#BARLINEOFF
#BPMCHANGE 202.3
#MEASURE 3/4
0,
0,
#GOGOEND

0,
#SCROLL 3.67
3,
#SCROLL 1
0,
0,
#MEASURE 4/4
0, //117
#END


COURSE:Easy
LEVEL:3
BALLOON:4,8,8,11
SCOREINIT:550,6050
SCOREDIFF:163

#START
0,
2,
0,
2,
0,

22,
2,
20002002,
0, //9

#GOGOSTART
1110,
1110,
1110,
0000000010010010,

0202,
0202,
700000000000000000000000000000000000080000000000,
4400, //17
#GOGOEND

10000001,
0,
10000001,
0,

11,
11,
11,
3430, //25

1110,
0202,
1110,
0202,

1110,
1110,
10101003,
0400, //33

12,
0022,
12,
0022,

12,
0022,
12,
0, //41

1,
01001000,
1,
01001000,

1120,
1120,
500000000000000000000000000000000000000000000008,
0,
0, //50

#GOGOSTART
33,
3,
1210,
1022,

33,
3,
1210,
1222, //58

12,
1120,
12,
1120,

1210,
1210,
7,
#GOGOEND
8002, //66

2,
0,
#BPMCHANGE 160
2,
0,

0,
0,
#GOGOSTART
1110,
2200, //74
#GOGOEND 

0,
0,
#GOGOSTART
1110,
1000000010010000,
#GOGOEND

#BPMCHANGE 212
0,
000000
#GOGOSTART
05,
000000000000000000000000000008000000000000500000,
000000000000000000000000000008000000000000500000,
000000000000000000000000000008000000000000600000,
000000000000000000000000000008000000000000000000, //84
#GOGOEND

#BPMCHANGE 176.5
0,
0,
7,
8,

2,
0,
2,
0, //92

#GOGOSTART
33,
30030000,
11,
2220,
#GOGOEND

3
#GOGOSTART
030,
30030306,
000000000000000000000000000008000000000000000000,
1210, //100

1212,
1212,
500000000000000000000000000000000000000000000008,
03,

9,
09008000,
0,
0, //108

#BPMCHANGE 276.5
0,
0,
#BARLINEOFF
#BPMCHANGE 202.3
#MEASURE 3/4
0,
0,
#GOGOEND

0,
#SCROLL 3.67
3,
#SCROLL 1
0,
0,
#MEASURE 4/4
0, //117
#END
