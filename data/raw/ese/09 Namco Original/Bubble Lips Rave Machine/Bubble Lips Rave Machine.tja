//TJADB Project
TITLE:Bubble Lips Rave Machine
TITLEJA:バブル・リップ・レイヴ・マシン
SUBTITLE:--<PERSON><PERSON>(BNSI) feat. Bonjour Suzuki
SUBTITLEJA:<PERSON><PERSON>(BNSI) feat. ボンジュール鈴木
BPM:160
WAVE:Bubble Lips Rave Machine.ogg
OFFSET:-1.561
DEMOSTART:25.565

//Slightly tweaked Oni #47 to replicate drumroll behaviour in NS2
//KFM #70 split into 2 stanzas to match with Oni barlines at #70-71.

COURSE:Oni
LEVEL:10
BALLOON:4,25,2,55
SCOREINIT:520,1350
SCOREDIFF:125

#START
0,
0,
0,
0, //4

2000200020220020,
2000200020220222,
2000200020220020,
2012012012112110, //8

3022021220221222,
2012122220101020,
1022021220221222,
2012122220012121, //12

100000100000200000100200100000202020100000200000,
200200100000200000100200100200100000101010101010,
1011201011201211,
101010100200101010201010100200200000700000000008, //16

#GOGOSTART
00202012,
000000200000100000100000100200102010100100200200,
1001201020121020,
000200100100200222200200, //20

1002001020021020,
000000200000100000200200000010202020001010200100,
1002001021021020,
202020101000202020101000200200100000200000000000, //24
#GOGOEND

7,
#SCROLL 0.7
000000008000,
22022022,
3332, //28

#SCROLL 1
2010201020212111,
2010201021212020,
2010201021121010,
100112100112100112100200, //32

300000000000200100000000400000000100000200101010,
2010201020121212,
2010201120121112,
1234, //36

#GOGOSTART
500000008000
#GOGOEND
#SCROLL 1.2
101010100100100200200100200200100200,
001010100200001010100200000200200100500008000000,
#SCROLL 1
202020202000100010001000100020200100200000100020,
210101210101200222200200, //40

100100100200101010100100100200200100200200100000,
200020100100200020100100200100200000300000400000,
200100000200101010100200100100100100202020100200,
101010100200101010200200101020200200700000800000, //44

5,
#SCROLL 0.7
0,
00000000000000000000000000000
#SCROLL 1
8
#SCROLL 0.7
000000000000100000,
21212205, //48

000000000000000000000000000000000000000008000000,
01112201,
11220101,
01120000, //52

#SCROLL 1
20020020,
0002002000202000,
20020020,
0002002000202000, //56

10102012,
1020101020011020,
10102011,
500000000000000000000000000000008000
#SCROLL 8
600000008000, //60

#GOGOSTART
#SCROLL 1.5
101010000100100000101010000000101000101010000000,
1221121210404000,
101010000100100000101010000000101000101010000000,
101010100200100200101010100000200200000200200200, //64

101010000100100000101010000000101000101010000000,
100200200100100200100100100000400000202000400000,
101010000100100000101010202000101000101010000000,
101010100200100200101010100000200200000200200200, //68
#GOGOEND

#SCROLL 1.3
2002002000202000,
#MEASURE 2/4
44,
#SCROLL 1.5
12222222, //71

#GOGOSTART
#MEASURE 4/4
101010000100100000101010000000101000101010200000,
101010200100100200100200100200200200202010201000,
101010000100100000101010000000101000101010200200,
1111121210222020, //75

101010000100100000101010000000101000101010202000,
101010100200100200101010102020200200200200200200,
#GOGOEND
#MEASURE 16/4
7008, //78
#END


COURSE:Hard
LEVEL:7
BALLOON:22,43
SCOREINIT:560,2070
SCOREDIFF:140

#START
0,
0,
0,
0,

00202022,
02222020,
20222202,
02212120, //8

3022022010220220,
1022022010100000,
1022022010220220,
1022022010010000,

1010201010020010,
2011011020110010,
21212121,
000000100000100000500000000000000000000000000008, //16

#GOGOSTART
00202011,
0010101022202020,
10112101,
0010101020222020,

10202012,
0010201022202020,
10202012,
0010201011102000, //24
#GOGOEND

7,
000000008000,
22022022,
1112, //28

21212112,
2010201020111000,
2010201020011010,
2010201020222000,

30103011,
21212112,
2010201020111010,
1234, //36

#GOGOSTART
3
#GOGOEND
500,
000000000000000000000000000008000000000000000000,
100000000000100010001000100000000100000000200000,
500000000000000008000000100000200200200000200000,

1110101010010010,
22222111,
2202201022022010,
1011101110111000, //44

5,
0,
000000000000000000000000000008000000000000100000,
11111105,

000000000000000000000000000000000000000008000000,
01111102,
22220101,
01110000, //52

20020020,
0002002000202000,
20020020,
0002002000202000,

10102012,
12102012,
10102011,
500000000000000000000000000000000008000000000000,

#GOGOSTART
3003003000303000,
1111101010404000,
3003003000303000,
1111101010200200,

3003003000303000,
1111101010404040,
3003003000303000,
1111101010220220, //68
#GOGOEND

2002002000202000,
#MEASURE 2/4
44,
#BARLINEOFF
4, //71

#BARLINEON
#MEASURE 4/4
#GOGOSTART
1101101100303000,
1111101010202020,
1101101100303000,
1111101010222020,

1101101100303000,
1111101022222000,
#GOGOEND
#MEASURE 16/4
7008, //78
#END


COURSE:Normal
LEVEL:6
BALLOON:29,29
SCOREINIT:780,3750
SCOREDIFF:228

#START
0,
0,
0,
0,

00202002,
02202020,
20202002,
02202000, //8

3001000010010000,
1001000010000000,
1001000010010000,
1001000010000000,

1000100010010000,
2002000020020000,
10101015,
000000000000000000000000000008000000000000000000, //16

#GOGOSTART
00202001,
01101010,
20202001,
01102020,

00202011,
01102020,
00202011,
01101110, //24
#GOGOEND

7,
0,
0,
8002, //28

11101002,
22202001,
11101010,
22202000,

3131,
11101002,
22202020,
3330, //36

#GOGOSTART
4
#GOGOEND
500,
000000000000000000000000000008000000000000000000,
1000100010010010,
1111,

2000200020020020,
2222,
1000100010010010,
1010100010010000, //44

5,
0,
000000000000000000000008000000000000000000100000,
11101105,

000000000000000000000000000008000000000000000000,
01001101,
00110101,
01010000, //52

20020020,
0002002000002000,
20020020,
0002002000002000,

10002001,
01002000,
12,
500000000000000000000000000008000000000000000000, //60

#GOGOSTART
1001001000101000,
10111000,
1001001000101000,
11111000,

1001001000101000,
20222000,
1001001000101000,
22202200, //68
#GOGOEND

500000000000000000000000000008000000000000000000,
#MEASURE 2/4
22,
#BARLINEOFF
2, //71

#BARLINEON
#MEASURE 4/4
#GOGOSTART
1001001000101000,
11111000,
2002002000202000,
22222000,

1001001000101000,
33303300,
#GOGOEND
#MEASURE 16/4
7008, //78
#END


COURSE:Easy
LEVEL:4
BALLOON:23,23
SCOREINIT:670,5860
SCOREDIFF:230

#START
0,
0,
0,
0,

00202002,
0222,
20202002,
0220, //8

3,
11,
1,
11,

1110,
22,
10101005,
000000000000000000000000000000000000000008000000, //16

#GOGOSTART
00202002,
0220,
10101001,
0110,

0220,
0111,
0220,
0111, //24
#GOGOEND

7,
0,
0,
8, //28

1110,
2220,
1110,
2220,

33,
1110,
1111,
3330, ///36

#GOGOSTART
4
#GOGOEND
500,
000000000000000000000000000008000000000000000000,
10101001,
0110,

2220,
1110,
1111,
1110, //44

5,
0,
000000000000000000000000000008000000000000000000,
10101005,

000000000000000000000000000000000000000000000008,
00000100,
00010001,
000000500000000000000000000000000008000000000000, //52

20020020,
0,
20020020,
0,

12,
12,
12,
500000000000000000000000000008000000000000000000, //60

#GOGOSTART
10010010,
1110,
20020020,
2220,

10010010,
1110,
20020020,
2220, //68
#GOGOEND

500000000000000000000000000008000000000000000000,
#MEASURE 2/4
22,
#BARLINEOFF
2, //71

#BARLINEON
#MEASURE 4/4
#GOGOSTART
10010010,
1110,
20020020,
2220,

10010010,
3330,
#GOGOEND
#MEASURE 16/4
7008, //78
#END