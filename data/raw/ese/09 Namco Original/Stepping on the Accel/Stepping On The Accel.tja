//TJADB Project
TITLE:Stepping On The Accel
TITLEJA:ステッピン・オン・ジ・アクセル
SUBTITLE:--valknee × <PERSON><PERSON> Okada (BNSI)
SUBTITLEJA:valknee × <PERSON><PERSON> Okada (BNSI)
BPM:172
WAVE:Stepping On The Accel.ogg
OFFSET:-1.921
DEMOSTART:22.848

//most hidden barlines ignored

COURSE:Oni
LEVEL:10
BALLOON:25,20,5,25
SCOREINIT:440,1180
SCOREDIFF:108

#START
0,
0,
0, //3

0,
0,
7,
#BARLINEOFF
#SCROLL 0.5
0000000800002000, //7

#BARLINEON
#SCROLL 1
1210,
1001001020000000,
1001001020002000,
1001001010201000, //11

101010101010101010200100,
101010101010101010200200,
100010001000100010001000100100100200200200200000,
#GOGOSTART
300030000300111100400000, //15
#GOGOEND

300000002000100000200000100100100100200000001020,
100000002000000000202020100000100100200000002000,
100000002000000000200000100200100100200000001020,
100100200000100100200000100100100000100200101010, //19

100000002000100000200000100100100100200000001020,
100000002000000000202020100000100100200000002000,
100000002000000000200000100200100100200000001020,
100200100200100000200000100100100100102020200000, //23

#GOGOSTART
200000000200000000200000000200000000400000000400000000400000000400000000600000000080000000000000,
#GOGOEND
100000001000000000100000101010100000200200200000,
300000002000000000200000100200100100200000001020,
100100200000100100200000100100100000100100202020, //27(+8)

200000002000100000200000100100100100200000001020,
100000002000000000202020100000100100200000001000,
200000002000000000200200100200100100200000200000,
500000000000000000000008000000400000300000000000, //31(+8)

#GOGOSTART
3021021201101220,
1021021202121220,
1021021201101221,
000100000100000200100000101010200200000200201010,
1021021021021012,
000100200000100200000200100000100200000100101020, //37(+8)

2021021021021021,
#MEASURE 8/4
0020100020100020100020100020100200100200100200100100100100100100,
#BARLINEOFF
#MEASURE 5/4
100100100100100100100100101010101010101010101010101010100100, //40(+21)
#GOGOEND

#BARLINEON
#SCROLL 1.2
3
#SCROLL 1
3
#SCROLL 0.8
3
#SCROLL 0.5
3, //41(+24)

#SCROLL 0.8
500000000000000000000008000000200000000000000100000200200000,
10220102201111011110,
500000000000000008000000100100200000000000000100000000200000,
100200200000200100200200000200100200200000200500008000000000, //45(+28)

10000202201000020220,
10102101021010120000,
10000122102002010000,
00002100003000030000, //49(+32)

30001222221000120220,
10110101101220210000,
10001222221000210220,
11202112021010101010, //53(+36)

#MEASURE 4/4
7,
#BARLINEOFF
#SCROLL 8
000000000000800000000000000000000000600000080000, //55(+36)

#BARLINEON
#SCROLL 1
200202222200,
200000200020202020200200,
200000000000200000002000100100100200200200200000,
#GOGOSTART
300030000300300300400000, //59(+36)

30000020100200201002002010020020,
10020020100200201020202010100200,
300000000000000010001000100000000200000000200000100000000200000000200000100000000200000000200000,
10020020100200201020200010102010, //63(+69)

10000020000200200002002000020020,
100000001000000000100000101010100000200200200000,
#GOGOEND
300000002000100000200200100200100100200000001020,
100010002000100000202020100000100100200100202000, //67(+78)

100100200000100100200000100100200100200200101020,
100000202000100100100100200000300000300000000000,
100000002000100200200100200100100100200000001010,
102120100200700000080000, //71(+78)

#GOGOSTART
3021021201101220,
1021021202121220,
1021021201101221,
000100000100000200100000101010200200000200201010,
1021012021012012,
200100200200100200200100200200100200000100101020, //77(+78)

2012021012021112,
#MEASURE 8/4
2020101010202020101010202020100200100200100200100100100100100100,
#BARLINEOFF
#MEASURE 5/4
100100100100100100100100101010101010101010101010101010100100, //80(+91)
#GOGOEND

#BARLINEON
#MEASURE 4/4
#SCROLL 1.5
7,
#BARLINEOFF
#SCROLL 0.5
0000000800002000,
#SCROLL 1
0,
0, //84(+91)
#END


COURSE:Hard
LEVEL:7
BALLOON:19,12,15,3,19
SCOREINIT:620,2260
SCOREDIFF:158

#START
0,
0,
0, //3

0,
0,
7,
#BARLINEOFF
#SCROLL 0.5
0000000800002000, //7

#BARLINEON
#SCROLL 1
1110,
1001001020000000,
1001001020002000,
1001001010101000, //11

111100100100,
111100100100,
100010001000100000000000100100100000100100100000,
#GOGOSTART
300000003000000000300000600008000000400000000000, //15
#GOGOEND

100020000200000100100000,
100020000200000100200000,
100020000200000100100000,
1010101011101000, //19

100020000200000100100000,
100020000200000100200000,
100020000200000100100000,
2020202022202000, //23

#GOGOSTART
7,
#GOGOEND
8000000010002220,
100020000200000100100000,
1010101011101000, //27(+8)

100020000200000200200000,
100020000200000200200000,
100020000200000200200000,
500000000000000008000000000000300000300000000000, //31(+8)

#GOGOSTART
1001001001101000,
1001001001110110,
1001001001101001,
0101001010111000,
2022022002002022,
0220020020020020, //37(+8)

1001001001001001,
#MEASURE 8/4
000000000000100000000000000000100000000000000000100000000000000000100000000000000000500000000000000000000000000000000000000000000000000000000000000000000000000000000000080000000000000000000000,
#BARLINEOFF
#MEASURE 5/4
600000000000000000000000000000000000000000000008000000000000, //40(+21)
#GOGOEND

#BARLINEON
#SCROLL 0.8
3333, //41(+24)

#SCROLL 0.8
500000000000000000000008000000200000000000000100000000000000,
10020100201002010000,
500000000000000000000008000000200000000000000100000000000000,
00020100201002010000, //45(+28)

10000100002000010010,
10010100101000020000,
1121,
0133, //49(+32)

30000202022000000000,
10010100101001010000,
10000202022000000000,
10010100101000000000, //53(+36)

#MEASURE 4/4
7,
#BARLINEOFF
#SCROLL 1
0800, //55(+36)

#BARLINEON
2222,
2222,
200020000000200000000000200200200000200000000000,
#GOGOSTART
300030000300300300400000, //59(+36)

3000100110011001,
1001100110101000,
1000100110011001,
1001100110102220, //63(+69)

1002200220022002,
2000000010102220,
#GOGOEND
100020100200100100200000,
100020000200100100200200, //67(+78)

100020000200100100200000,
1110111000303000,
100020000200100100200000,
101010100000700000080000, //71(+78)

#GOGOSTART
1001001001101000,
1001001001110110,
1001001001101001,
0101001010111000,
2022022002002022,
0220020020020020, //77(+78)

1001001001001001,
#MEASURE 8/4
000000000000100000000000000000100000000000000000100000000000000000100000000000000000500000000000000000000000000000000000000000000000000000000000000000000000000000000000080000000000000000000000,
#BARLINEOFF
#MEASURE 5/4
600000000000000000000000000000000000000000000008000000000000, //80(+91)
#GOGOEND

#BARLINEON
#MEASURE 4/4
7,
#BARLINEOFF
#SCROLL 0.5
0000000800002000,
#SCROLL 1
0,
0, //84(+91)
#END


COURSE:Normal
LEVEL:5
BALLOON:13,8,10,13
SCOREINIT:740,3820
SCOREDIFF:223

#START
0,
0,
0, //3

0,
0,
7,
#BARLINEOFF
0000000800002000, //7

#BARLINEON
1110,
10011000,
10011010,
10011000, //11

2220,
20022000,
20022020,
#GOGOSTART
600000000000000000000008000000000000400000000000, //15
#GOGOEND

10010110,
10010110,
10010110,
2222, //19

10010110,
10010110,
10010110,
2222, //23

#GOGOSTART
7,
#GOGOEND
8014,
11011010,
2220, //27(+8)

10010110,
10010110,
10010110,
500000000000000000000000000000000008000000000000, //31(+8)

#GOGOSTART
30000330,
30000330,
30000330,
0033,
40040040,
04004000, //37(+8)

40040040,
#MEASURE 8/4
000000000000500000000000000000000000000000000000000000000080000000000000000000000000500000000000000000000000000000000000000000000000000000000000000000000000000000000000080000000000000000000000,
#BARLINEOFF
#MEASURE 5/4
600000000000000000000000000000000000000000000008000000000000, //40(+21)
#GOGOEND

#BARLINEON
#SCROLL 0.8
3333, //41(+24)

#SCROLL 0.8
500000000000000000000000000008000000000000000100000000000000,
1121,
500000000000000000000000000008000000000000000100000000000000,
0111, //45(+28)

1212,
1210,
1212,
0033, //49(+32)

3022,
1122,
1022,
1120, //53(+36)

#MEASURE 4/4
7,
#BARLINEOFF
#SCROLL 1
0800, //55(+36)

#BARLINEON
2,
2,
2,
#GOGOSTART
30001120, //59(+36)

3111,
1110,
1111,
1110, //63(+69)

100000000000500000000000000000000008000000000000,
1014,
#GOGOEND
22,
2220, //67(+78)

2222,
2202,
22,
1110, //71(+78)

#GOGOSTART
30000330,
30000330,
30000330,
0033,
40040040,
04004000, //77(+78)

40040040,
#MEASURE 8/4
000000000000500000000000000000000000000000000000000000000080000000000000000000000000500000000000000000000000000000000000000000000000000000000000000000000000000000000000080000000000000000000000,
#BARLINEOFF
#MEASURE 5/4
600000000000000000000000000000000000000000000008000000000000, //80(+91)
#GOGOEND

#BARLINEON
#MEASURE 4/4
7,
#BARLINEOFF
0000000800002000,
0,
0, //84(+91)
#END


COURSE:Easy
LEVEL:4
BALLOON:10,6,8,8
SCOREINIT:640,5430
SCOREDIFF:200

#START
0,
0,
0, //3

0,
0,
7,
#BARLINEOFF
0000000800000000, //7

#BARLINEON
1110,
11,
11,
11, //11

2220,
22,
22,
#GOGOSTART
600000000000000008000000000000000000300000000000, //15
#GOGOEND

10010010,
10010010,
10010010,
1110, //19

10010010,
10010010,
10010010,
1110, //23

#GOGOSTART
7,
#GOGOEND
8004,
22,
2220, //27(+8)

10010010,
10010010,
10010010,
500000000000000000000000000008000000000000000000, //31(+8)

#GOGOSTART
3001,
3001,
3001,
01,
4004,
04, //37(+8)

4004,
#MEASURE 8/4
000000000000500000000000000000000000000000000000000000000080000000000000000000000000500000000000000000000000000000000000000000000000000000000000000000000000000000000000080000000000000000000000,
#BARLINEOFF
#MEASURE 5/4
600000000000000000000000000000000000000000000008000000000000, //40(+21)
#GOGOEND

#BARLINEON
#SCROLL 0.8
3333, //41(+24)

#SCROLL 0.8
500000000000000000000008000000000000000000000100000000000000,
0011,
500000000000000000000008000000000000000000000100000000000000,
0011, //45(+28)

12,
1011,
12,
0033, //49(+32)

32,
1111,
12,
1110, //53(+36)

#MEASURE 4/4
7,
#BARLINEOFF
#SCROLL 1
0800, //55(+36)

#BARLINEON
2,
2,
2,
#GOGOSTART
1004, //59(+36)

1111,
1110,
1111,
1110, //63(+69)

500000080000,
1004,
#GOGOEND
22,
2220, //67(+78)

2222,
2202,
22,
1110, //71(+78)

#GOGOSTART
3001,
3001,
3001,
01,
4004,
04, //77(+78)

4004,
#MEASURE 8/4
000000000000500000000000000000000000000000000000000000000080000000000000000000000000500000000000000000000000000000000000000000000000000000000000000000000000000000000000080000000000000000000000,
#BARLINEOFF
#MEASURE 5/4
600000000000000000000000000000000000000000000008000000000000, //80(+91)
#GOGOEND

#BARLINEON
#MEASURE 4/4
7,
#BARLINEOFF
0000000800002000,
0,
0, //84(+91)
#END