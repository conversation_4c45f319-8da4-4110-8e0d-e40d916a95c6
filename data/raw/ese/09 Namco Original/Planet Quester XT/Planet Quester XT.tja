//TJADB Project
TITLE:Planet Quester XT
TITLEJA:Planet Quester XT
SUBTITLE:--<PERSON> (BNSI)
SUBTITLEJA:濱本理央(BNSI)
BPM:208
WAVE:Planet Quester XT.ogg
OFFSET:-2.627
DEMOSTART:23.384

COURSE:Edit
LEVEL:10
BALLOON:
SCOREINIT:370,1030
SCOREDIFF:90

#START
#MEASURE 8/4
0,
10101020101010201000001000000011,
2221222122212212,
10101020101010201000001000000011,
2221222122212212,
1112111230000000, //6

#MEASURE 6/4
111010200010111010201020,
111010201010211010200020,
111010200010111010201020,
111010201010211010200020, //10

#MEASURE 8/4
11101020101020201010201010201011,
20202011202020112020201120201120,
11101020101020202222201010201011,
20202011202020112020201120201120, //14

11101020111010201111111020002222,
11101020101020201110201010201011,
20202011202020112020201120201120,
300000200200200200200000300000000000100100100000500000000000000000000000000000000000000008000000, //18

#MEASURE 6/4
#BPMCHANGE 268
111010200010111010201020,
111010102010211010200020,
111020100011211010201020,
111020201011211010200020,
#MEASURE 4/4
01221222,
#MEASURE 8/4
10200200, //24

#GOGOSTART
21111020001020101020101120001020,
21111020001020101020101120002020,
21111020001020101020101120001020,
21111020001020101022201120001020, //28

21111011200020101111101120002020,
21111011201010201111201120002020,
21111011200020101020101120001020,
21112020001020102121201021202000, //32
#GOGOEND

#MEASURE 6/4
#BPMCHANGE 208
111010200010111010201020,
111010201010211010200020,
111010200010111010201020,
111010201010211010200020,
#MEASURE 4/4
01221222, //37

#MEASURE 8/4
#BPMCHANGE 284
10000020001010000010001120000000,
10000020001010000020001120000000,
1000000000000001,
1, //41

#MEASURE 7/4
#GOGOSTART
2110102010102110102000201000,
#MEASURE 8/4
21101010201011102000121000111010,
#MEASURE 7/4
2110102010102110102000201000,
#MEASURE 8/4
21101010201011102000121010111010, //45

#MEASURE 7/4
2110102010102110102000201000,
#MEASURE 8/4
21101010201011102000121000111010,
#MEASURE 7/4
2110102010102110102000201000, //48
#GOGOEND

#MEASURE 6/4
#BPMCHANGE 208
111010200010111010201020,
#MEASURE 5/4
11101020101011201020,
#MEASURE 6/4
111010200010111010201020,
#MEASURE 5/4
11101020101021101020, //52

#MEASURE 8/4
#BPMCHANGE 264
#GOGOSTART
11101120102010201000221000201021,
20202011202020112020201120201120,
11101120102010201000221020000021,
20202011202020112020201120201120,
#GOGOEND
#BARLINEOFF
#MEASURE 1/4
#BPMCHANGE 33
#SCROLL 8
0, //57

#BARLINEON
#MEASURE 4/4
#BPMCHANGE 170
#SCROLL 1
3,
00000002,
3,
0000000000112010, //61

3,
00000002,
3,
500000000000000000000008000000000000000000000000, //65

#MEASURE 8/4
#BPMCHANGE 340
#SCROLL 0.75
#GOGOSTART
1121221121221121,
200200100100200100200200101010101010202020101010,
200200100100201010200200100100201010200200100100,
2122112122121121, //69

202020101010200100200200100100200100200200100100,
201010202020100100200100200200100200100100200100,
202020101010201010200200100100200100202020101010,
201010200200100100200100201020102010200200100100, //73
#GOGOEND

2212112212112212,
1100000000000000,
0, //76
#END


COURSE:Oni
LEVEL:9
BALLOON:27,15
SCOREINIT:530,1550
SCOREDIFF:130

#START
#MEASURE 8/4
#SCROLL 0.5
0,
1111222210010000,
2211221122112220,
1111222210010000,
2211221122112220,
1111222210000000, //6

#MEASURE 6/4
111200111202,
111210221100,
111200111202,
111210221100, //10

#MEASURE 8/4
1111222210010000,
2211221122112222,
1111222210010000,
2211221122112222, //14

500000000008000000000000500000000008000000000000500000000000000000000000000000000008000000000000,
1112122210010000,
2211221122112222,
100000200000000000000000300000000000000000000000600000000000000000000000000000000008000000000000, //18

#MEASURE 6/4
#BPMCHANGE 268
111200111202,
111210221100,
111200111202,
111210221120,
#MEASURE 4/4
01222222,
#MEASURE 8/4
1, //24

#GOGOSTART
3002001011112020,
1002001022112020,
1002001011112020,
1002001022112020, //28

1110201110022012,
1001201022112012,
1001201022112020,
1022001022112020, //32
#GOGOEND

#MEASURE 6/4
#BPMCHANGE 208
111200111202,
111210221101,
111200111202,
111210221101,
#MEASURE 4/4
01222222, //37

#MEASURE 8/4
#BPMCHANGE 284
7,
08,
1000000000000001,
1, //41

#MEASURE 7/4
#GOGOSTART
11121222102100,
#MEASURE 8/4
2211221122110000,
#MEASURE 7/4
11121222102100,
#MEASURE 8/4
2211221122110000, //45

#MEASURE 7/4
11121222102100,
#MEASURE 8/4
2211221122110000,
#MEASURE 7/4
11121222102100, //48
#GOGOEND

#MEASURE 6/4
#BPMCHANGE 208
111201111202,
#MEASURE 5/4
1112102211,
#MEASURE 6/4
111201111202,
#MEASURE 5/4
1112102211, //52

#MEASURE 8/4
#BPMCHANGE 264
#GOGOSTART
1112122210210001,
2211221122112222,
1112122210210001,
2211221122112222,
#GOGOEND
#BARLINEOFF
#MEASURE 1/4
#BPMCHANGE 33
#SCROLL 4
0, //57

#BARLINEON
#MEASURE 4/4
#BPMCHANGE 170
#SCROLL 1
3,
0001,
1,
22, //61

3,
02,
1,
2, //65

#MEASURE 8/4
#BPMCHANGE 340
#SCROLL 0.5
#GOGOSTART
1112012012012120,
1112012021212010,
1112012012012120,
1112012022112010, //69

1112012012012120,
1112012021212010,
1112012012012120,
1112012022222010, //73
#GOGOEND

7,
0800000000000000,
0, //76
#END


COURSE:Hard
LEVEL:6
BALLOON:15,25,11
SCOREINIT:680,2770
SCOREDIFF:188

#START
#MEASURE 8/4
#SCROLL 0.5
0,
1010101010010000,
22202220,
1010101010010000,
22202220,
11113000,

#MEASURE 6/4
1112,
1122,
1112,
1122, //10

#MEASURE 8/4
1110101110010000,
2220222022202020,
1110101110010000,
2220222022202020,

500000000008000000000000500000000008000000000000500000000000000000000000000000000008000000000000,
1110101110010000,
2220222022202020,
700000000000000000000000000000000000000000000000000000000000000000000008000000000000000000000000, //18

#MEASURE 6/4
#BPMCHANGE 268
1112,
1122,
1112,
1122,
#MEASURE 4/4
0,
#MEASURE 8/4
3, //24

#GOGOSTART
10011122,
10022212,
10011122,
10022212,

1110001110000022,
2002002020201001,
00021122,
10022020, //32
#GOGOEND

#MEASURE 6/4
#BPMCHANGE 208
1112,
1122,
1112,
1122,
#MEASURE 4/4
0,

#MEASURE 8/4
#BPMCHANGE 284
7,
08,
1000000000000001,
1, //41

#MEASURE 7/4
#GOGOSTART
10101011100100,
#MEASURE 8/4
2220222020020000,
#MEASURE 7/4
10101011100100,
#MEASURE 8/4
2220222020020000,

#MEASURE 7/4
10101011100100,
#MEASURE 8/4
2220222020020000,
#MEASURE 7/4
10101011100100, //48
#GOGOEND

#MEASURE 6/4
#BPMCHANGE 208
1112,
#MEASURE 5/4
1001002220,
#MEASURE 6/4
1112,
#MEASURE 5/4
1001002220,

#MEASURE 8/4
#BPMCHANGE 264
#GOGOSTART
1010101110010000,
2220222022202020,
1010101110010000,
2220222022202022,
#GOGOEND
#BARLINEOFF
#MEASURE 1/4
#BPMCHANGE 33
#SCROLL 4
0, //57

#BARLINEON
#MEASURE 4/4
#BPMCHANGE 170
#SCROLL 0.5
3,
0001,
1,
22,

3,
01,
1,
2, //65

#MEASURE 8/4
#BPMCHANGE 340
#GOGOSTART
1001001001002010,
0010010020010010,
1001001001002010,
0020010020020020,

1001001001002010,
0010010020010010,
1001001001002010,
0020010020202000, //73
#GOGOEND

900000000000000000090000,
0800000000000000,
0, //76
#END


COURSE:Normal
LEVEL:5
BALLOON:10,7
SCOREINIT:750,4080
SCOREDIFF:233

#START
#MEASURE 8/4
#SCROLL 0.5
0,
1110,
2222,
1110,
2222,
1130,

#MEASURE 6/4
1110,
1120,
1110,
1120, //10

#MEASURE 8/4
11111000,
22202220,
11111000,
22202220,

500000000000000000000000000000000000000000000000000000000000000000000008000000000000000000000000,
11111000,
22202220,
700000000000000000000000000000000000000000000000000000000000000000000008000000000000000000000000, //18

#MEASURE 6/4
#BPMCHANGE 268
1110,
1120,
1110,
1120,
#MEASURE 4/4
0,
#MEASURE 8/4
3, //24

#GOGOSTART
10011110,
10002220,
10011110,
10022220,

10011002,
2002002020202002,
00001111,
12, //32
#GOGOEND

#MEASURE 6/4
#BPMCHANGE 208
1110,
1120,
1110,
1120,
#MEASURE 4/4
0,

#MEASURE 8/4
#BPMCHANGE 284
3,
0,
1,
1, //41

#MEASURE 7/4
#GOGOSTART
1111100,
#MEASURE 8/4
22202000,
#MEASURE 7/4
1111100,
#MEASURE 8/4
22202000,

#MEASURE 7/4
1111100,
#MEASURE 8/4
22202000,
#MEASURE 7/4
1111100, //48
#GOGOEND

#MEASURE 6/4
#BPMCHANGE 208
1110,
#MEASURE 5/4
1001002000,
#MEASURE 6/4
1110,
#MEASURE 5/4
1001002000,

#MEASURE 8/4
#BPMCHANGE 264
#GOGOSTART
11111000,
22202220,
11111000,
22202222,
#GOGOEND
#BARLINEOFF
#MEASURE 1/4
#BPMCHANGE 33
#SCROLL 4
0, //57

#BARLINEON
#MEASURE 4/4
#BPMCHANGE 170
#SCROLL 0.5
3,
0001,
1,
22,

3,
01,
1,
2, //65

#MEASURE 8/4
#BPMCHANGE 340
#GOGOSTART
1001001001002000,
0010010020000000,
1001001001002000,
0020020020020000,

1001001001002000,
0010010020000000,
1001001001002000,
0020020020020000, //73
#GOGOEND

900000000000000000090000,
0800000000000000,
0, //76
#END


COURSE:Easy
LEVEL:3
BALLOON:7,6
SCOREINIT:720,6990
SCOREDIFF:308

#START
#MEASURE 8/4
#SCROLL 0.5
0,
1,
22,
1,
22,
1130,

#MEASURE 6/4
11,
1,
11,
1, //10

#MEASURE 8/4
1110,
2220,
1110,
2220,

500000000000000000000000000000000000000000000000000000000008000000000000000000000000000000000000,
1110,
2220,
70000800, //18

#MEASURE 6/4
#BPMCHANGE 268
11,
1,
11,
1,
#MEASURE 4/4
0,
#MEASURE 8/4
3, //24

#GOGOSTART
1011,
12,
1011,
12,

11,
2022,
1011,
12, //32
#GOGOEND

#MEASURE 6/4
#BPMCHANGE 208
11,
1,
11,
1,
#MEASURE 4/4
0,

#MEASURE 8/4
#BPMCHANGE 284
3,
0,
1,
1, //41

#MEASURE 7/4
#GOGOSTART
1010100,
#MEASURE 8/4
2220,
#MEASURE 7/4
1010100,
#MEASURE 8/4
2220,

#MEASURE 7/4
1010100,
#MEASURE 8/4
2220,
#MEASURE 7/4
1010100, //48
#GOGOEND

#MEASURE 6/4
#BPMCHANGE 208
11,
#MEASURE 5/4
1,
#MEASURE 6/4
11,
#MEASURE 5/4
1,

#MEASURE 8/4
#BPMCHANGE 264
#GOGOSTART
1110,
2222,
1110,
2222,
#GOGOEND
#BARLINEOFF
#MEASURE 1/4
#BPMCHANGE 33
#SCROLL 4
0, //57

#BARLINEON
#MEASURE 4/4
#BPMCHANGE 170
#SCROLL 0.5
3,
0,
1,
2,

3,
0,
1,
2, //65

#MEASURE 8/4
#BPMCHANGE 340
#GOGOSTART
10010010,
01002000,
10010010,
02002000,

10010010,
01002000,
10010010,
02002000, //73
#GOGOEND

900000000000000000090000,
0800000000000000,
0, //76
#END