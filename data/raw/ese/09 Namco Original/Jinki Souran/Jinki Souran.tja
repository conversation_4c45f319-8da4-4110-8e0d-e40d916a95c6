//TJADB Project
TITLE:<PERSON><PERSON>
TITLEJA:神鬼争乱
SUBTITLE:--<PERSON><PERSON> & <PERSON><PERSON><PERSON>
SUBTITLEJA:翡翠＆祇羽
BPM:176
WAVE:<PERSON><PERSON> Souran.ogg
OFFSET:-1.480
DEMOSTART:103.765


COURSE:Oni
LEVEL:10
BALLOON:10
SCOREINIT:430,1180
SCOREDIFF:108

#START
0,
0,
0,

200022100020000010200022,
102001101101,
200022100020000010200022,
102001100300, //7

200022100020100010200022,
100020100011100020100011,
200022100020100010200022,
100020100011100020100011,

200022100020100010200022,
100020100011100020100011,
200022100020100010200022,
100020100011100020100010, //15

303030222121,
300030003000202020221020,
303030112122,
300030003000201022201020,

300000100020100010200022,
100102101201,
100000100020100010200022,
700000000000000000800022, //23

100100122122,
100000100022100010200020,
101010202020101020101022,
101010202020101020102011,

200100222121,
100000100011200010201010,
222222200010201010201020,
221221304040, //31

#MEASURE 6/4
#GOGOSTART
301111201110,
201110201121,
300010101010200110101010,
200010101010200110201111,

100110101110201011101010,
200210102210201011102020,
100110101110201011112022,
100110201110211121112111, //39
#GOGOEND

#MEASURE 4/4
2,
0004,
3,
0004,

31213121,
31213141,
31213121,
301111411211,

300000100020100010200022,
100102101201,
100000100020100010200022,
100000100020100010111111, //51

200100122122,
100000100022100010200020,
100000102020101020101022,
101010202020101020102011,

200100222121,
100000100011200010201010,
211221211212,
202010202010300022202020, //59

#MEASURE 6/4
#GOGOSTART
301111201110,
201111201121,
300010101010200110101010,
200010101010200110201111,

100110101110201011101010,
200210102210201011102020,
100110201110201011112022,
100110201110211121112111, //67

202011112011211122111021,
200000100100200100100000200200200200100000000000400000300000000000202020,
100000000100100000200000100100100200100000100200100000200200100100202020,
100000000200100000200200100100200200100010002000100100200200101010202020,

100110211020122212221211,
200000200200100020202000100020202000300000000000202020100000000000300000,
400000202020101020222010102020101020,
300010002000300020002000300010002000100100100200100100200200101010202020, //75
#GOGOEND

#MEASURE 4/4
5,
0,
000000000000000000000000000000000000000000000008,
0,

30221020,
10212011,
30112021,
1000001110003000,

0,
0,
0, //86
#END


COURSE:Hard
LEVEL:8
BALLOON:8
SCOREINIT:460,1660
SCOREDIFF:118

#START
0,
0,
0,

202202000202,
202001100100,
202202000202,
202001100300, //7

202202001202,
202001100100,
202202001202,
202001100100,

202202001202,
202001100100,
202202001202,
202001102100, //15

333222,
303030222020,
303030202220,
303030222220,

300102200102,
100102200101,
100102200101,
7008, //23

200100222202,
100102100102,
100222202102,
100222200101,

200100222200,
100101101202,
222200201202,
222200304040, //31

#MEASURE 6/4
#GOGOSTART
301010201110,
201110201120,
301010201110,
201110201211,

300010101110200011101010,
200010101110200011102020,
100010101110200022202010,
100010201110201020102022, //39
#GOGOEND

#MEASURE 4/4
2,
0002,
1,
0002,

10201120,
10201120,
10201120,
101111101200,

300102200102,
100102200101,
100102200101,
100000000000100010000000100010000000100100100100, //51

100100222202,
100102200101,
100222202102,
100222200101,

200100222200,
100101201201,
102202201102,
222200300400, //59

#MEASURE 6/4
#GOGOSTART
301011201110,
201111201120,
301011201110,
201111201211,

300010101110200011101010,
200010101110200011102020,
100010101110200022202010,
100010201110201020102011, //67

100011102010201110101022,
200010101022200040300011,
100010201110101020102011,
100000000000200000200000100000100000100010001000100000200000100100100100,

100010111020100022201010,
200000000000200020002000200020002000100000000000200200200000000000300000,
400222102101111102,
100000001000100010001000200000100000100100100000100100100000100100100100, //75
#GOGOEND

#MEASURE 4/4
5,
0,
000000000000000000000000000000000000000000000008,
0,

3,
00002001,
30002001,
1000001110003000,

0,
0,
0, //86
#END


COURSE:Normal
LEVEL:7
BALLOON:7
SCOREINIT:530,2420
SCOREDIFF:145

#START
0,
0,
0,

2202,
200001100100,
2202,
200001100300, //7

200200002200,
200001100100,
200200002200,
200001100100,

200200002200,
200001100100,
200200002200,
200001100100, //15

333222,
333222,
333222,
303030202220,

300100100101,
100100100101,
1111,
7008, //23

100100202200,
100101100101,
100200200202,
2220,

100100202200,
100101100202,
202200200200,
202200303030, //31

#MEASURE 6/4
#GOGOSTART
301101,
200020200100,
301101,
200020202200,

300010101110,
200022201010,
100010101110,
200022202020, //39
#GOGOEND

#MEASURE 4/4
3,
0001,
1,
0001,

1212,
1212,
1212,
100100101100,

300100100101,
100100100101,
100100100101,
1120, //51

100100202200,
100101100101,
100200200202,
2220,

100100202200,
100101100202,
2222,
202200300300, //59

#MEASURE 6/4
#GOGOSTART
300011100010,
200022200100,
300011100010,
200022202200,

300011101110,
200022201010,
100011101110,
200022202220, //67

401110202022,
201112202102,
301110202222,
101110101011,

301122202011,
202020102103,
400202202200101101,
102121201111, //75
#GOGOEND

#MEASURE 4/4
5,
0,
000000000000000000000000000000000000000000000008,
0,

3,
00002001,
30002001,
10011030,

0,
0,
0, //86
#END


COURSE:Easy
LEVEL:5
BALLOON:5
SCOREINIT:540,4600
SCOREDIFF:185

#START
0,
0,
0,

2002,
01,
2002,
01, //7

2002,
21,
2002,
21,

2002,
21,
2002,
21, //15

32,
32,
32,
3022,

3001,
1001,
1001,
7008, //23

1011,
11,
2220,
22,

1011,
11,
2220,
2033, //31

#MEASURE 6/4
#GOGOSTART
31,
22,
31,
2021,

301100,
200020200100,
101100,
202220, //39
#GOGOEND

#MEASURE 4/4
3,
0,
1,
0,

11,
12,
11,
12,

3001,
1001,
1001,
12, //51

1011,
11,
2220,
22,

1011,
11,
2220,
2033, //59

#MEASURE 6/4
#GOGOSTART
31,
2021,
31,
200201,

301100,
200020200100,
101100,
202220, //67

301102,
200022200100,
300010100011,
101111,

300010100022,
200010100100,
302211,
101010101111, //75
#GOGOEND

#MEASURE 4/4
5,
0,
000000000000000000000000000000000000000000000008,
0,

3,
02,
3,
1013,

0,
0,
0, //86
#END