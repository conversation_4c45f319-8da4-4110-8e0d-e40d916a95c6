//TJADB Project
TITLE:Go Go Kitchen -New Audio/Chart-
TITLEJA:ゴーゴー・キッチン
SUBTITLE:--<PERSON>UBTITLEJA:新音源・譜面
BPM:206
WAVE:Go Go Kitchen.ogg
OFFSET:-1.520
DEMOSTART:0.385

//Shinuchi: 4080/2680/1800/1800/1040

//AC2/3 Chart uses the same old track as AC1.
//AC2/3 Donderful is the same as Hard chart. Easy/Normal charts do not exist in AC2/3.
//AC2/3 Hard chart ends at stanza #96 and has no notes in #94-96. 

COURSE:Edit
LEVEL:10
BALLOON:14
SCOREINIT:380,1070
SCOREDIFF:93

#START
0,
2222222222222000,
0, //3

1020112012201120,
1020102010201120,
1020112012201120,
0022002200220022,

1020112010201120,
1020102010201120,
1020112012201120,
0022102010221011, //11

2000111020100010,
2011100020001110,
2000111020100010,
21221120,

2000111020100010,
2011100020001110,
2000111020100010,
2010201022202220, //19

1022001100220030,
0010212010121000,
1011002200110040,
0020121010212000,

1022001100220030,
0010212020121000,
1011202210112040,
0020121020212000, //27

1022101120221030,
1011221022112020,
1011202210112040,
2022112011221022,

1021101220211030,
1022112011221022,
1012222111122070,
0000000000000800, //35

1000121000200020,
0011100010001110,
1000121000200020,
0011100010001110,

1000121000200020,
0011100010001110,
1000121000400040,
2222222222222222, //43

2020121020201020,
0011102010201110,
1020121020201020,
0011102010201110,

1020121020201020,
0011102010201110,
1020121020201020,
1011101010201020, //51

#SCROLL 0.5
1010202011201120,
#SCROLL 0.6
1011222020002000,
#SCROLL 0.7
1010202022102210,
#SCROLL 0.8
1022111020002000,

#SCROLL 0.9
1010202011201120,
#SCROLL 1
1011222020002000,
1010202022102210,
#SCROLL 0.5
1022111020002000, //59

#SCROLL 1.5
1111201022221020,
0011222020002000,
#SCROLL 0.5
2222102011112010,
#SCROLL 2
0022111020002000,

#SCROLL 0.5
1121201022121020,
#SCROLL 2
0011222022102210,
#SCROLL 0.5
2212102011212010,
#SCROLL 3
33404000, //67

#SCROLL 1
#GOGOSTART
1010101120100010,
2011111020100010,
2011101120100010,
2010111120100010,

2011101120100010,
2011111020100010,
2011101120100010,
2030302022102210, //75

2011201122102010,
2011211022102010,
2011201122102010,
2010211122102010,

2011201122102010,
2011211022102010,
2011201122102010,
2010211122102012, //83

2011201122102012,
2011211022102012,
2011201122102012,
2010211122102012,

2011201122102012,
2011211022102012,
2011201122102012,
2030302022102211, //91

2011201122102012,
2011211022102012,
21212303,
#BARLINEOFF
#SCROLL 0.7
00000101,

#SCROLL 0.6
00000202,
#SCROLL 0.5
00000202,
#SCROLL 3
00000101,
#BARLINEON
#SCROLL 1
2022101022102211, //99

2011201122102212,
2011211022102212,
2011201122102212,
21212101,
#GOGOEND

#BARLINEOFF
#SCROLL 4
00000202,
0,
0,
0, //107
#END


COURSE:Oni
LEVEL:7
BALLOON:
SCOREINIT:550,1800
SCOREDIFF:145

#START
0,
0,
0, //3

1100101010101010,
0011001000101010,
0011002010101010,
0,

1100101010101010,
0011001000101010,
0011002010101010,
0, //11

10120101,
02201011,
10120101,
0,

10120101,
02201011,
10120101,
0, //19

1000101010001111,
0111,
1000101010001111,
0111,

1000101010001111,
0111,
1000101010001111,
0111, //27

1020101010201111,
0111,
1020101010201111,
0111,

1020101010201111,
0111,
1020101010201111,
0, //35

10121102,
01120112,
10121102,
01120112,

10121102,
01120112,
10121102,
01120112, //43

11121102,
01120112,
11121102,
01120112,

11121102,
01120112,
11121102,
0, //51

1100200011002000,
00200120,
1100200011002000,
00200120,

1100200011002000,
00200120,
1100200011002000,
00200120, //59

1000111010001110,
00200120,
1000111010001110,
00200120,

1000111010001110,
00200120,
1000111010001110,
0, //67

#GOGOSTART
11120102,
01020110,
11120102,
01020110,

11120102,
01020110,
11120102,
01020000, //75

1100120011001200,
1100120011001200,
1100120011001200,
1100120011001200,

1100120011001200,
1100120011001200,
1100120011001200,
1100120011001200, //83

11121112,
11121112,
11121112,
11121112,

11121112,
11121112,
11121112,
11121112, //91

11121112,
11121112,
00000202,
00000202,

11110202,
00000202,
00000202,
00000202, //99

11110202,
11110202,
11110202,
11110202,
#GOGOEND

0,
0,
0,
0, //107
#END


COURSE:Hard
LEVEL:7
BALLOON:
SCOREINIT:460,1800
SCOREDIFF:123

#START
0,
0,
0, //3

1100101010101010,
0011001000101010,
0011002010101010,
0,

1100101010101010,
0011001000101010,
0011002010101010,
0, //11

#SECTION
#BRANCHSTART p,70,80
#N
10120101,
02201011,
10120101,
0,

10120101,
02201011,
10120101,
0, //n19

#E
10120101,
02201011,
10120101,
0,

10120101,
02201011,
10120101,
0, //e19

#M
10120101,
02201011,
10120101,
0,

10120101,
02201011,
10120101,
0, //m19

#SECTION
#BRANCHSTART p,70,80
#N
10111011,
0111,
10111011,
0111,

10111011,
00101022,
10111011,
00101022, //n27

#E
1000101010001111,
0111,
1000101010001111,
0111,

1000101010001111,
0111,
1000101010001111,
0111, //e27

#M
1000101010001111,
0111,
1000101010001111,
0111,

1000101010001111,
0111,
1000101010001111,
0111, //m27

#SECTION
#BRANCHSTART p,70,80
#N
1000101010001111,
0111,
1000101010001111,
0111,

1000101010001111,
0111,
1000101010001111,
0, //n35

#E
1000101010001111,
0111,
1000101010001111,
0111,

1000101010001111,
0111,
1000101010001111,
0, //e35

#M
1020101010201111,
0111,
1020101010201111,
0111,

1020101010201111,
0111,
1020101010201111,
0, //m35

#SECTION
#BRANCHSTART p,70,80
#N
11020102,
0002,
11020102,
0002,

11021102,
0202,
11021102,
0202, //n43

#E
10121102,
0202,
10121102,
0202,

10121102,
0202,
10121102,
0202, //e43

#M
10121102,
01120112,
10121102,
01120112,

10121102,
01120112,
10121102,
01120112, //m43

#SECTION
#BRANCHSTART p,70,80
#N
10121102,
0202,
10121102,
0202,

10121102,
00222220,
10121102,
0, //n51

#E
11121102,
0202,
11121102,
0202,

11121102,
0202,
11121102,
0, //e51

#M
11121102,
01120112,
11121102,
01120112,

11121102,
01120112,
11121102,
0, //m51

#SECTION
#BRANCHSTART p,70,80
#N
1100200011002000,
0202,
1100200011002000,
0202,

1100200011002000,
0202,
1100200011002000,
0202, //n59

#E
1100200011002000,
00200220,
1100200011002000,
00200220,

1100200011002000,
00200220,
1100200011002000,
00200220, //e59

#M
1100200011002000,
00200120,
1100200011002000,
00200120,

1100200011002000,
00200120,
1100200011002000,
00200120, //m59

#SECTION
#BRANCHSTART p,70,80
#N
0000111000001110,
0202,
0000111000001110,
0202,

0000111000001110,
0202,
0000111000001110,
0, //n67

#E
0000111000001110,
00200220,
0000111000001110,
00200220,

0000111000001110,
00200220,
0000111000001110,
0, //e67

#M
1000111010001110,
00200120,
1000111010001110,
00200120,

1000111010001110,
00200120,
1000111010001110,
0, //m67

#SECTION
#BRANCHSTART p,70,80
#N
#GOGOSTART
11110101,
00001110,
11110101,
00001110,

11110101,
00001110,
11110101,
0, //n75

#E
#GOGOSTART
11120102,
01020110,
11120102,
01020110,

11120102,
01020110,
11120102,
01020000, //e75

#M
#GOGOSTART
11120102,
01020110,
11120102,
01020110,

11120102,
01020110,
11120102,
01020000, //m75

#SECTION
#BRANCHSTART p,70,80
#N
1100200011002000,
1100200011002000,
1100200011002000,
1100200000000000,

1100200011002000,
1100200011002000,
1100200011002000,
1100200000000000, //n83

#E
1100110022002200,
1100110022002200,
1100110022002200,
1100110022002200,

1100120011001200,
1100120011001200,
1100120011001200,
1100120011001200, //e83

#M
1100120011001200,
1100120011001200,
1100120011001200,
1100120011001200,

1100120011001200,
1100120011001200,
1100120011001200,
1100120011001200, //m83

#SECTION
#BRANCHSTART p,70,80
#N
11110202,
11110202,
11110202,
11110202,

11110202,
11110202,
11110202,
11110202, //n91

11110202,
11110000,
00000202,
00000202,

11110202,
00000202,
00000202,
00000202, //n99

11110202,
11110202,
11110202,
11110202,
#GOGOEND

0,
0,
0,
0, //n107

#E
11110201,
11110201,
11110201,
11110201,

11110201,
11110201,
11110201,
11110201, //e91

11110201,
11110200,
00000202,
00000202,

11110202,
00000202,
00000202,
00000202, //e99

11110202,
11110202,
11110202,
11110202,
#GOGOEND

0,
0,
0,
0, //e107
 
#M
11121112,
11121112,
11121112,
11121112,

11121112,
11121112,
11121112,
11121112, //m91

11121112,
11121112,
00000202,
00000202,

11110202,
00000202,
00000202,
00000202, //m99

11110202,
11110202,
11110202,
11110202,
#GOGOEND

0,
0,
0,
0, //m107

#BRANCHEND
#END


COURSE:Normal
LEVEL:5
BALLOON:
SCOREINIT:500,2680
SCOREDIFF:138

#START
0,
0,
0, //3

10101011,
01010111,
01021111,
0,

10101011,
01010111,
01021111,
0, //11

10110101,
0011,
10110101,
0,

10110101,
0011,
10110101,
0, //19

10111011,
0,
10111011,
0,

10111011,
0,
10111011,
0, //27

10111011,
0202,
10111011,
0202,

10111011,
0202,
10111011,
0, //35

11020102,
0002,
11020102,
0002,

11020102,
0002,
11020102,
0, //43

11101102,
0002,
11101102,
0002,

11101102,
0002,
11101102,
0, //51

1100200011002000,
0202,
1100200011002000,
0202,

1100200011002000,
0202,
1100200011002000,
0, //59

0000111000001110,
0202,
0000111000001110,
0202,

0000111000001110,
0202,
0000111000001110,
0, //67

#GOGOSTART
11110101,
0022,
11110101,
0022,

11110101,
0022,
11110101,
0, //75

1100200011002000,
1100200000000000,
1100200011002000,
1100200000000000,

1100200011002000,
1100200000000000,
1100200011002000,
1100200000000000, //83

11110202,
11110000,
11110202,
11110000,

11110202,
11110000,
11110202,
11110000, //91

11110202,
11110000,
00000202,
00000202,

11110202,
00000202,
00000202,
00000202, //99

11110202,
11110000,
11110202,
11110202,
#GOGOEND

0,
0,
0,
0, //107
#END


COURSE:Easy
LEVEL:5
BALLOON:
SCOREINIT:480,4080
SCOREDIFF:148

#START
0,
0,
0, //3

10001011,
0,
01001011,
0,

10101011,
0,
01001011,
0, //11

10110101,
0,
10110101,
0,

10110101,
0,
10110105,
000000000000000000000000000000000008000000000000, //19

10101011,
0,
10101011,
0,

10101011,
0,
10101011,
0, //27

10101011,
0200,
10101011,
0200,

10101011,
0200,
10101011,
0, //35

10000101,
0,
10000101,
0,

10000101,
0,
10000101,
0, //43

11100101,
0,
11100202,
0,

11100101,
0,
11100202,
0, //51

11100000,
0202,
11100000,
0202,

11100000,
0202,
11100000,
0, //59

00110011,
0202,
00110011,
0202,

00110011,
0202,
00110011,
0, //67

#GOGOSTART
10000101,
02,
10000101,
02,

10000101,
02,
10000101,
0, //75

1202,
1200,
1202,
0200,

1202,
0200,
1202,
0200, //83

11110000,
11110000,
11110000,
11110000,

11110000,
11110000,
11110000,
11110000, //91

11110000,
11110000,
00000202,
00000202,

00000202,
00000202,
00000202,
0, //99

10100202,
02020000,
10100202,
02020000,
#GOGOEND

0,
0,
0,
0, //107
#END