//TJADB Project
TITLE:<PERSON><PERSON><PERSON>
TITLEJA:待ち受けプリンス
SUBTITLE:--<PERSON><PERSON><PERSON><PERSON> feat. 765PRO ALLSTARS/THE iDOLM@STER Shiny TV
SUBTITLEJA:「アイドルマスター」より
BPM:144
WAVE:<PERSON><PERSON><PERSON>.ogg
OFFSET:-1.928
DEMOSTART:54.837

//Shinuchi: 5020/4160/2420/1470

COURSE:Oni
LEVEL:9
BALLOON:4,4
SCOREINIT:500,1650
SCOREDIFF:125

#START
2022002022202022,
2022002022202222,
2022202220222022,
200200200200002020202020700000000000800000000000, //4

#GOGOSTART
1210221022102210,
22102210
#SCROLL 3
00300000,
#SCROLL 1
2210201220122010,
4010401040102222, //8

1210221022102210,
22102210
#SCROLL 3
00300000,
#SCROLL 1
2210201220122010,
2211221122212022, //12
#GOGOEND

1010200210112020,
102210021110
#GOGOSTART
#SCROLL 3
4000,
#SCROLL 1
1210221022102210,
22102210
#SCROLL 3
00300000, //16
#GOGOEND

#SCROLL 1
1010200210112020,
102210021210
#GOGOSTART
#SCROLL 3
4000,
#SCROLL 1
2210201220122010,
2211221122212022, //20
#GOGOEND

1011201120112011,
23030303,
2011202120112021,
23232323, //24

2011201120112011,
23030303,
600000000000000000000000000000000008000000000000,
4030003040222222, //28

1120200011202000,
1120200011202000,
1122112212121210,
#BARLINEOFF
#SCROLL 3
#GOGOSTART
600000000000000000000008
#GOGOEND
#SCROLL 1
000000000000000000000000, //32

#BARLINEON
#GOGOSTART
1201201120303000,
1201201120303000,
102120121010
#SCROLL 3
4000,
#SCROLL 1
1120112011201120, //36

1201201120303000,
1201201120303000,
102120121010
#SCROLL 3
4000,
#SCROLL 1
1210201210303000, //40
#GOGOEND

#SCROLL 3
500000000000000000000000000008000000000000300000,
#SCROLL 1
0, //42

100200200000100000202020100200000200100000200100,
13030303,
222222111111,
13030303, //46

100000200100200000202020201010100100100000200100,
13030303,
1211112112111121,
100300000300400222200000, //50

1120200011202000,
1120200011202000,
1122112212121210,
#SCROLL 3
#GOGOSTART
600000000000000000000008
#GOGOEND
#SCROLL 1
000000000000000000000000, //54

2002002020000000,
2002002020000000,
2002002020002000,
22222000, //58

1001201020002000,
1001201020002000,
500000000000000000000000000000000000000000000008,
#BARLINEOFF
#SCROLL 0.5
0708, //62

#BARLINEON
#SCROLL 1
#GOGOSTART
600000000000000000000008000000300000300000000000,
1221201120303000,
122120121010
#SCROLL 3
4000,
#SCROLL 1
1122112211221122, //66

1221201120303000,
1221201120303000,
122120121010
#SCROLL 3
4000,
#SCROLL 1
1210201210303000, //70
#GOGOEND

#SCROLL 3
500000000000000000000000000008000000000000300000,
#BARLINEOFF
#SCROLL 1
0,
#BARLINEON
#SCROLL 0.5
20000022,
#BARLINEOFF
#SCROLL 1
0,
0, //75
#END


COURSE:Hard
LEVEL:5
BALLOON:4
SCOREINIT:540,3120
SCOREDIFF:158

#START
0,
0,
0,
0, //4

#GOGOSTART
11212121,
21210300,
21212121,
4444, //8

11212121,
21210300,
21212121,
3344, //12
#GOGOEND

11200121,
2000200011104000,
#GOGOSTART
11212121,
21210300, //16
#GOGOEND

11200121,
2000200022204000,
#GOGOSTART
21212121,
3344, //20
#GOGOEND

01121121,
23030303,
01121121,
23232323, //24

01121121,
23030303,
600000000000000000000000000000000008000000000000,
43034000, //28

1212,
1212,
12121212,
#BARLINEOFF
#SCROLL 2
#GOGOSTART
600000000000000000000008
#GOGOEND
#SCROLL 1
000000000000000000000000, //32

#BARLINEON
#GOGOSTART
1001002020303000,
1001002020303000,
1001002010104000,
34343434, //36

1001002020303000,
1001002020303000,
1001002010104000,
11221330, //40
#GOGOEND

500000000000000000000000000008000000000000100000,
0, //42

0,
03030303,
0,
03030303, //46

0,
03030303,
0,
03034000, //50

1212,
1212,
12121212,
#SCROLL 2
#GOGOSTART
600000000000000000000008
#GOGOEND
#SCROLL 1
000000000000000000000000, //54

2002002020000000,
2002002020000000,
2002002020002000,
22222000, //58

1001001020000000,
1001001020000000,
500000000000000000000000000000000000000000000008,
#BARLINEOFF
#SCROLL 0.5
0708, //62

#BARLINEON
#SCROLL 1
#GOGOSTART
600000000000000000000008000000300000300000000000,
1001002020303000,
1001002010104000,
34343434, //66

1001002020303000,
1001002020303000,
1001002010104000,
11221330, //70
#GOGOEND

500000000000000000000000000008000000000000100000,
0,

20000022,
0,
0, //75
#END


COURSE:Normal
LEVEL:5
BALLOON:5,4
SCOREINIT:790,6800
SCOREDIFF:328

#START
0,
0,
0,
0, //4

#GOGOSTART
5,
000000000000000008000000000000300000000000000000,
500000000000000000000000000000000008000000000000,
4444, //8

5,
000000000000000008000000000000300000000000000000,
500000000000000000000000000000000008000000000000,
3344, //12
#GOGOEND

1100,
1114,
#GOGOSTART
5,
000000000000000008000000000000300000000000000000, //16
#GOGOEND

1100,
1114,
#GOGOSTART
500000000000000000000000000000000008000000000000,
3344, //20
#GOGOEND

0,
03030303,
0,
03030303, //24

0,
03030303,
0,
03034000, //28

1212,
1212,
12121212,
#BARLINEOFF
#GOGOSTART
9009
#GOGOEND
8000, //32

#BARLINEON
#GOGOSTART
00000330,
00000330,
0004,
34343434, //36

00000330,
00000330,
0004,
00000330, //40
#GOGOEND

500000000000000000000000000008000000000000100000,
0, //42

0,
03030303,
0,
03030303, //46

0,
03030303,
0,
03034000, //50

1212,
1212,
12121212,
#GOGOSTART
600000000000000000000008
#GOGOEND
000000000000000000000000, //54

2,
2,
22,
2220, //58

12,
12,
500000000000000000000000000000000000000000000008,
#BARLINEOFF
#SCROLL 0.5
0708, //62

#BARLINEON
#SCROLL 1
#GOGOSTART
600000000000000008000000000000300000300000000000,
00000330,
0004,
34343434, //66

00000330,
00000330,
0004,
00000330, //70
#GOGOEND

500000000000000000000000000008000000000000100000,
0,

20000022,
0,
0, //75
#END


COURSE:Easy
LEVEL:3
BALLOON:5
SCOREINIT:530,8610
SCOREDIFF:255

#START
0,
0,
0,
0, //4

#GOGOSTART
5,
000000000000000008000000000000300000000000000000,
500000000000000000000000000000000008000000000000,
4444, //8

5,
000000000000000008000000000000300000000000000000,
500000000000000000000000000000000008000000000000,
3344, //12
#GOGOEND

0202,
0204,
#GOGOSTART
5,
000000000000000008000000000000300000000000000000, //16
#GOGOEND

0202,
0204,
#GOGOSTART
500000000000000000000000000000000008000000000000,
3344, //20
#GOGOEND

0,
03030303,
0,
03030303, //24

0,
03030303,
0,
03034000, //28

11,
11,
1111,
#BARLINEOFF
#GOGOSTART
9009
#GOGOEND
8000, //32

#BARLINEON
#GOGOSTART
00000330,
00000330,
0004,
3333, //36

00000330,
00000330,
0004,
00000330, //40
#GOGOEND

500000000000000000000000000008000000000000100000,
0, //42

0,
03030303,
0,
03030303, //46

0,
03030303,
0,
03034000, //50

11,
11,
1111,
#GOGOSTART
600000000000000000000008
#GOGOEND
000000000000000000000000, //54 c

2,
2,
2,
22, //58

1,
1,
11,
#BARLINEOFF
#SCROLL 0.5
0, //62

#BARLINEON
#SCROLL 1
#GOGOSTART
600000000000000008000000000000300000300000000000,
00000330,
0004,
3333, //66

00000330,
00000330,
0004,
00000330, //70
#GOGOEND

500000000000000000000000000008000000000000100000,
0,
20000022,
0,
0, //75
#END