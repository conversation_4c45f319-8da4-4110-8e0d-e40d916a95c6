//TJADB Project
TITLE:Yo<PERSON> Made Ato 3-by<PERSON>
TITLEJA:夜明けまであと3秒
SUBTITLE:--<PERSON><PERSON>/Synchronica
SUBTITLEJA:「シンクロニカ」より
BPM:177
WAVE:Yoake Made Ato 3-byou.ogg
OFFSET:-1.643
DEMOSTART:36.897

//Shinuchi: 5440/3940/1770/1190

COURSE:Oni
LEVEL:9
BALLOON:5,4
SCOREINIT:420,1230
SCOREDIFF:100

#START
2022,
22,
2022,
22, //4

200000202200,
22,
222200222200,
#MEASURE 6/4
222222200000000000, //8

#MEASURE 4/4
#BPMCHANGE 182
111222101022,
112112112112,
111222101022,
112112112112, //12

111222101022,
111222111222,
111212111212,
121222121222, //16

1
#SCROLL 1.1
1
#SCROLL 1.2
1
#SCROLL 1.3
1,
#BARLINEOFF
#SCROLL 1.4
1
#SCROLL 1.5
1
#SCROLL 1.6
1
#SCROLL 1.7
1,
#SCROLL 1.8
1
#SCROLL 1.9
1
#SCROLL 2
1
#SCROLL 2.1
1,
#SCROLL 2.2
1
#SCROLL 2.3
1
#SCROLL 2.4
1
#SCROLL 2.5
1,
#SCROLL 2.6
11
#SCROLL 2.7
11
#SCROLL 2.8
11
#SCROLL 2.9
11,
#SCROLL 3
11
#SCROLL 3.1
11
#SCROLL 3.2
11
#SCROLL 3.3
11,
#SCROLL 3.4
1111111111111111,
#SCROLL 3.5
500000000000000000000000000000000008000000000000, //24

#BARLINEON
#MEASURE 4/4
#SCROLL 1
1001001001002000,
1234, //26

1010201210102011,
1022112010212010,
2010201211202010,
101010100100200000200100100000100100200100100100, //30

2010201210102010,
1022121011212010,
2010201121201011,
2000102000112000, //34

123
#SCROLL 2
4,
#SCROLL 1
1011202010221020,
1010201212102010,
4000104011104010, //38

4000002212102010,
1020102121102000,
1110201110201111,
2011212122121211,
200000000000000000000022,
2020102010221000, //44

111100000000000000000000,
0,
222200000000000000000000,
1122101121104000, //48

101010100000200200100000200000200100100000200000,
2110202110202110,
202020200000200200100000200000200100100000200000,
2110221211221212, //52

101010100000200200100000200200200100100000200200,
2110222110222110,
202020200000200200100000100100000100100000100100,
0110110110201100, //56

101010100000200200100000200200200100100000200200,
2110112122112121,
202020200000200200100000100100000100100000100100,
0110110110101200, //60

1101101101101200,
1101101101101200,
1101121101121101,
#MEASURE 6/4
121121127000800000000000, //64

#MEASURE 4/4
1022,
22,
2022,
22, //68

200000202200,
22,
222200222200,
222222222222, //72

202020222020222020222020,
202220202220200000000000, 
0,
123
#SCROLL 4
4, //76


#SCROLL 1
#GOGOSTART
1010201210102011,
202010202010202010200100,
2010201211202010,
101010100100200000200100100000100000400000000000, //80

2011111111102010,
1022121011212010,
2010201121201011,
2000102000112000, //84

123
#SCROLL 5
4,
#SCROLL 1
1011202010221020,
1010201212102010,
4000104011104010, //88

4000002212102010,
1020102121102020,
1010201220102010,
1020102212112000, //92

1101101101101010,
1101101101101200,
1121101121101121,
1211270080000000, //96
#GOGOEND

0,
0,
0, //99
#END


COURSE:Hard
LEVEL:7
BALLOON:10,3,2
SCOREINIT:480,1860
SCOREDIFF:123

#START
2022,
22,
2022,
22,

200000202200,
22,
222200222200,
#MEASURE 6/4
222200400000000000, //8

#MEASURE 4/4
#BPMCHANGE 182
300200202022,
2212,
200200202022,
2212,

200200102022,
2212,
1212,
3434, //16

1
#SCROLL 1.05
1
#SCROLL 1.1
1
#SCROLL 1.15
1,
#BARLINEOFF
#SCROLL 1.2
1
#SCROLL 1.25
1
#SCROLL 1.3
1
#SCROLL 1.35
1,
#SCROLL 1.4
1
#SCROLL 1.45
1
#SCROLL 1.5
1
#SCROLL 1.55
1,
#SCROLL 1.6
1
#SCROLL 1.65
1
#SCROLL 1.7
1
#SCROLL 1.75
1,
#SCROLL 1.8
11111111,
11111111,
#SCROLL 1.85
500000000000000000000000000000000000000000008000,
7000000000000800, //24

#BARLINEON
#SCROLL 1
1001001001000000,
1230, //26

1010101110102000,
0022202020222000,
21211220,
1011102010102000,

1010101110102000,
2011101010222000,
2010201110201010,
20120120, //34

1230,
1011101020222020,
10210210,
30030040,

3000002220102000,
02121120,
11211211,
2010102011101111,

1,
01212110, //44

1100000000000000,
0,
2200000000000000,
1110101110102000,

1100201020011020,
0110200110200110,
2200201020011020,
0110111010101110, //52

1100201020011020,
0110200110200110,
2200111010010010,
0100100100200100,

1100201020011020,
0110111010101110,
2200111010010010,
0100100100102200, //60

1001001001002200,
1001001001002200,
1001001001001001,
#MEASURE 6/4
001001007000800000000000, //64

#MEASURE 4/4
1022,
22,
2022,
22,

200000202200,
22,
222200222200,
222200222200, //72

200222200200,
222222200000,
0,
1230, //76

#GOGOSTART
1010101110102000,
220220220200,
21211220,
1011102010102000,

1010101110102000,
2011101010222000,
2010201110201010,
20120120, //84

1234,
1011101020222020,
10210210,
30030040,

3000002220102010,
12121221,
2010102220102010,
1020102011102000, //92

1001001001001010,
1001001001002200,
1002001002001002,
0010070080000000,
#GOGOEND

0,
0,
0, //99
#END


COURSE:Normal
LEVEL:5
BALLOON:7,15,21,13,21,7,13,14,16,7,7,12
SCOREINIT:830,5180
SCOREDIFF:215

#START
2,
22,
2,
22,

2022,
22,
2222,
#MEASURE 6/4
224000, //8

#MEASURE 4/4
#BPMCHANGE 182
3220,
2210,
1220,
1210,

1210,
1210,
1212,
3434, //16

508000000000
#SCROLL 1.1
508000000000
#SCROLL 1.2
508000000000
#SCROLL 1.3
508000000000,
#BARLINEOFF
#SCROLL 1.4
508000000000
#SCROLL 1.5
508000000000
#SCROLL 1.6
508000000000
#SCROLL 1.7
508000000000,
#SCROLL 1.8
508000000000
#SCROLL 1.9
508000000000
#SCROLL 2
508000000000
#SCROLL 2.1
508000000000,
#SCROLL 2.2
508000000000
#SCROLL 2.3
508000000000
#SCROLL 2.4
508000000000
#SCROLL 2.5
508000000000, //20
#SCROLL 2.6
500008000000
#SCROLL 2.7
500008000000
#SCROLL 2.8
500008000000
#SCROLL 2.9
500008000000,
#SCROLL 3
500008000000
#SCROLL 3.1 
500008000000
#SCROLL 3.2
500008000000
#SCROLL 3.3
500008000000,
#SCROLL 3.4
500000000000000000000000000000000000000000008000,
#SCROLL 3.5
7000000000000800, //24

#BARLINEON
#SCROLL 1
600000000000000000000000008000000000000000000000,
3330, //26

10010010,
01001010,
10010010,
500000000000000000000000000000000008000000000000,

10010010,
7,
0008,
40040040, //34

3330,
1202,
1202,
30030040,

30022020,
02020290,
0,
0000000900000000,

8,
0, //44

1,
0,
2,
500000000000000000000000000008000000300000000000,

10011011,
01101100,
20011011,
01101100, //52

10011011,
01101100,
20017000,
000008,

10011011,
01101100,
20017000,
0, //60

0008,
7008,
7,
#MEASURE 6/4
008300000, //64

#MEASURE 4/4
1,
22,
2,
22,

2022,
22,
2220,
2222,

9,
09008000,
0,
3330, //76

#GOGOSTART
10010010,
4440,
10010010,
500000000000000000000000000000000008000000000000,

10010010,
7,
0008,
40040040, //84

3330,
1202,
1202,
30030040,

30022020,
02020220,
20022020,
02020220, //92

7008,
7008,
9,
908000,
#GOGOEND

0,
0,
0, //99
#END


COURSE:Easy
LEVEL:4
BALLOON:7,13,17,10,17,6,10,12,13,6,6,10
SCOREINIT:690,7260
SCOREDIFF:150

#START
2,
22,
2,
22,

2022,
22,
2220,
#MEASURE 6/4
224000, //8

#MEASURE 4/4
#BPMCHANGE 182
32,
21,
12,
11,

11,
11,
1210,
3430, //16

508000000000
#SCROLL 1.1
508000000000
#SCROLL 1.2
508000000000
#SCROLL 1.3
508000000000,
#BARLINEOFF
#SCROLL 1.4
508000000000
#SCROLL 1.5
508000000000
#SCROLL 1.6
508000000000
#SCROLL 1.7
508000000000,
#SCROLL 1.8
508000000000
#SCROLL 1.9
508000000000
#SCROLL 2
508000000000
#SCROLL 2.1
508000000000,
#SCROLL 2.2
508000000000
#SCROLL 2.3
508000000000
#SCROLL 2.4
508000000000
#SCROLL 2.5
508000000000,
#SCROLL 2.6
500008000000
#SCROLL 2.7
500008000000
#SCROLL 2.8
500008000000
#SCROLL 2.9
500008000000,
#SCROLL 3
500008000000
#SCROLL 3.1
500008000000
#SCROLL 3.2
500008000000
#SCROLL 3.3
500008000000,
#SCROLL 3.4
500000000000000000000000000000000000000000008000,
#SCROLL 3.5
7000000000000800, //24

#BARLINEON
#SCROLL 1
600000000000000000000000008000000000000000000000,
3330, //26

10010050,
000000000000000000000000000008000000000000000000,
10010010,
500000000000000000000000000008000000000000000000,

10010010,
7,
0008,
500000000000000000000000000008000000000000000000, //34

3330,
0101,
0101,
30030030,

3022,
000000500000000000000000000008000000900000000000,
0,
0000000900000000,

8,
0, //44

1,
0,
2,
500000000000000000000000000008000000000000000000,

10001001,
00100100,
20001001,
00100100, //52

10001001,
00100100,
27,
000008,

10001001,
00100100,
27,
0, //60

0008,
7008,
7,
#MEASURE 6/4
008300000, //64

#MEASURE 4/4
1,
22,
2,
22,

2022,
22,
2220,
2220, //72

9,
09008000,
0,
3330, //76

#GOGOSTART
10010050,
000000000000000000000000000008000000000000000000,
10010010,
500000000000000000000000000008000000000000000000,

10010010,
7,
0008,
40040040, //84

3330,
0101,
0101,
30030030,

3022,
02020000,
2022,
02020000, //92

7008,
7008,
9,
908000,
#GOGOEND

0,
0,
0, //99
#END