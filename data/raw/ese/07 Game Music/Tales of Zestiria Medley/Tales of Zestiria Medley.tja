//TJADB Project
TITLE:Tales of Zestiria Medley
TITLEJA:テイルズ オブ ゼスティリア メドレー
SUBTITLE:--<PERSON><PERSON><PERSON>ba/Tales of Zestiria
BPM:158
WAVE:Tales of Zestiria Medley.ogg
OFFSET:-1.832
DEMOSTART:3.350

COURSE:Oni
LEVEL:8
BALLOON:12,12,4,12,12,4
SCOREINIT:480,1410
SCOREDIFF:113

#START
1000100022221111,
1010210010102100,
1010210010102102,
1010210010102100,
1010210010101111,

11021102,
11021102,
1010210210202102,
1010202211102211, //9

1010210010102100,
1010210010102102,
1010210010102100,
1010212020202211,

11021102,
12021202,
11021102,
3004004030040040, //17

1111100020102000,
1111100010112022,
11211221,
41413141, //21

#GOGOSTART
600000000000000000000000000000000008000000000000,
600000000000000000000000000000000008000000000000,
600000000000000000000000000000000008000000000000,
600000000000000000000000000000000008000000000000,

7008,
2010210220102100,
7008,
2010210022221111, //29
#GOGOEND

1010210010102100,
1010210010102102,
1010210010102100,
1010210010101111,

11021102,
12021202,
1010210210102102,
1010221110111000, //37

#MEASURE 5/8
#BPMCHANGE 134
3,
30033,
3003003030,
3003003030, //41

#GOGOSTART
1020112020,
1020112020,
1020112020,
1020112011,

1020112020,
1020112020,
1020112020,
4040111122, //49

1020112020,
1020112020,
1020112020,
1020112011,

1020112020,
1020112020,
4004004040,
300000000600000000008000000000, //57
#GOGOEND

1012011020,
1011011020,
1012012020,
1011011022,

1012011020,
1011011020,
1012012020,
1011011022, //65

#GOGOSTART
1012011020,
1011011020,
1012012020,
4040221011,

1012011020,
1011011020,
1012012020,
4040221111, //73

2011201120,
2011201120,
2020112121,
2011201121,

2011201120,
2011201120,
2011201120,
2020221110,
#GOGOEND
#BPMCHANGE 135.69
0, //82

#MEASURE 4/4
#BPMCHANGE 137.86
#SCROLL 0.98
100111200221,
#BPMCHANGE 129.38
#SCROLL 1.04
122122,
#BPMCHANGE 134
#SCROLL 1.01
500000000000000008000000700000000000800000000000, //85

#BPMCHANGE 134.11
100111404040,
#BPMCHANGE 134.51
#SCROLL 1
221221,
#BPMCHANGE 132.99
#SCROLL 1.02
100111404040,
#BPMCHANGE 134.73
#SCROLL 1
221221, //89

#BPMCHANGE 132.88
#SCROLL 1.02
7008,
#BPMCHANGE 134.45
#SCROLL 1
7008,
#BPMCHANGE 134.36
100111101010,
#BPMCHANGE 134.16
#SCROLL 1.01
100111101010, //93

#BPMCHANGE 134.89
#SCROLL 1
100111404040,
#BPMCHANGE 132.78
#SCROLL 1.02
221221,
#BPMCHANGE 133.64
#SCROLL 1.01
100111404040,
#BPMCHANGE 133.69
221221, //97

#BPMCHANGE 138.51
#SCROLL 0.97
100111200221,
#BPMCHANGE 130.59
#SCROLL 1.03
122122,
#BPMCHANGE 134.45
#SCROLL 1
500000000000000008000000700000000000800000000000,
3,
0,
0, //103
#END


COURSE:Hard
LEVEL:6
BALLOON:10,10,3,10,10,3
SCOREINIT:490,1870
SCOREDIFF:115

#START
1000100022201110,
11201120,
11201122,
11201120,
11201110,

11021102,
11021102,
11201120,
1010200011102000, //9

11201120,
11201122,
11201120,
1010200011102000,

11021102,
11021102,
11021102,
3003003030030030, 

10201120,
10201120,
11201120,
4434, //21

#GOGOSTART
600000000000000000000000000000000008000000000000,
600000000000000000000000000000000008000000000000,
600000000000000000000000000000000008000000000000,
600000000000000000000000000000000008000000000000,

7008,
10010010,
7008,
1010200011102000, //29
#GOGOEND

11201120,
11201122,
11201120,
11201110,

11021102,
11021102,
11201120,
1010200011102000, //37

#MEASURE 5/8
#BPMCHANGE 134
3,
3,
3003003000,
3003003000,

#GOGOSTART
12122,
12122,
12122,
1020102220,

12122,
12122,
12122,
1020101110, //49

12122,
12122,
12122,
1020102220,

12122,
12122,
4004004040,
300000000600000000008000000000, //57
#GOGOEND

1001001010,
1001001000,
2002002020,
2002002000,

1001001010,
1001001000,
2002002020,
2002002000, //65

#GOGOSTART
1001001010,
1001001010,
2002002020,
2022022020,

1001001010,
1001001010,
2002002020,
4040101110, //73

21212,
21212,
22122,
21212,

21212,
21212,
21212,
2020111010,
#GOGOEND
#BPMCHANGE 135.69
0, //82

#MEASURE 4/4
#BPMCHANGE 137.86
#SCROLL 0.98
100111100111,
#BPMCHANGE 129.38
#SCROLL 1.04
122122,
#BPMCHANGE 134
#SCROLL 1.01
500000000000000008000000700000000000800000000000, //85

#BPMCHANGE 134.11
100100404040,
#BPMCHANGE 134.51
#SCROLL 1
221221,
#BPMCHANGE 132.99
#SCROLL 1.02
100100404040,
#BPMCHANGE 134.73
#SCROLL 1
221221, //89

#BPMCHANGE 132.88
#SCROLL 1.02
7008,
#BPMCHANGE 134.45
#SCROLL 1
7008,
#BPMCHANGE 134.36
100111101010,
#BPMCHANGE 134.16
#SCROLL 1.01
100111101010, //93

#BPMCHANGE 134.89
#SCROLL 1
100100404040,
#BPMCHANGE 132.78
#SCROLL 1.02
221221,
#BPMCHANGE 133.64
#SCROLL 1.01
100100404040,
#BPMCHANGE 133.69
221221, //97

#BPMCHANGE 138.51
#SCROLL 0.97
100111100111,
#BPMCHANGE 130.59
#SCROLL 1.03
122122,
#BPMCHANGE 134.45
#SCROLL 1
500000000000000008000000700000000000800000000000,
3,
0,
0, //103
#END


COURSE:Normal
LEVEL:4
BALLOON:6,6,6,6,7,7
SCOREINIT:790,4310
SCOREDIFF:210

#START
00001110,
10101110,
10201110,
10101110,
1210,

5,
000000000000000000000000000008000000000000000000,
5,
000000000000000000000000000008000000000000000000, //9

10101110,
10201110,
10101110,
1210,

5,
000000000000000000000000000008000000000000000000,
5,
000000000000000000000000000008000000000000000000,

1212,
1212,
7008,
7008, //21

#GOGOSTART
600000000000000000000000000000000008000000000000,
600000000000000000000000000000000008000000000000,
600000000000000000000000000000000008000000000000,
600000000000000000000000000000000008000000000000,

7008,
10010010,
7008,
10201120, //29
#GOGOEND

10101110,
10201110,
10101110,
1210,

5,
000000000000000000000000000008000000000000000000,
5,
000000000000000000000000000008000000000000000000, //37

#MEASURE 5/8
#BPMCHANGE 134
3,
3,
3,
3,

#GOGOSTART
10020,
10020,
10020,
10100,

10020,
10020,
10020,
1001001000,

10020,
10020,
10020,
10100,

10020,
10020,
3003003030,
300000000600000000008000000000, //57
#GOGOEND

1001000000,
1001000000,
2002000000,
2002000000,

1001000000,
1001000000,
2002000000,
2002000000, //65

#GOGOSTART
1001001000,
1001001000,
2002002000,
2002002000,

1001001000,
1001001000,
2002002000,
4001001000,

20202,
20202,
22022,
20202,

20202,
20202,
20202,
22000,
#GOGOEND
#BPMCHANGE 135.69
0, //82

#MEASURE 4/4
#BPMCHANGE 137.86
#SCROLL 0.98
3,
#BPMCHANGE 129.38
#SCROLL 1.04
22,
#BPMCHANGE 134
#SCROLL 1.01
500000000000000000000000000000000008000000000000, //85

#BPMCHANGE 134.11
3,
#BPMCHANGE 134.51
#SCROLL 1
22,
#BPMCHANGE 132.99
#SCROLL 1.02
3,
#BPMCHANGE 134.73
#SCROLL 1
22,

#BPMCHANGE 132.88
#SCROLL 1.02
7008,
#BPMCHANGE 134.45
#SCROLL 1
7008,
#BPMCHANGE 134.36
11,
#BPMCHANGE 134.16
#SCROLL 1.01
1, //93

#BPMCHANGE 134.89
#SCROLL 1
3,
#BPMCHANGE 132.78
#SCROLL 1.02
22,
#BPMCHANGE 133.64
#SCROLL 1.01
3,
#BPMCHANGE 133.69
22,

#BPMCHANGE 138.51
#SCROLL 0.97
3,
#BPMCHANGE 130.59
#SCROLL 1.03
22,
#BPMCHANGE 134.45
#SCROLL 1
500000000000000000000000000000000008000000000000,
3,
0,
0, //103
#END


COURSE:Easy
LEVEL:2
BALLOON:4,4,4,4,4,4
SCOREINIT:680,6490
SCOREDIFF:200

#START
0,
11,
11,
11,
1110,

5,
000000000000000000000000000008000000000000000000,
5,
000000000000000000000000000008000000000000000000, //9

11,
11,
11,
1110,

5,
000000000000000000000000000008000000000000000000,
5,
000000000000000000000000000008000000000000000000,

0202,
0202,
7008,
7008, //21

#GOGOSTART
600000000000000000000000000000000008000000000000,
600000000000000000000000000000000008000000000000,
600000000000000000000000000000000008000000000000,
600000000000000000000000000000000008000000000000,

7008,
11,
7008,
1110, //29
#GOGOEND

11,
11,
11,
1110,

5,
000000000000000000000000000008000000000000000000,
5,
000000000000000000000000000008000000000000000000, //37

#MEASURE 5/8
#BPMCHANGE 134
3,
3,
3,
3,

#GOGOSTART
10010,
10010,
10010,
1,

10010,
10010,
10010,
1,

10010,
10010,
10010,
1,

10010,
10010,
3,
3003000000, //57
#GOGOEND

1,
1,
1,
1,

1,
1,
2,
2, //65

#GOGOSTART
1001000000,
1001000000,
1001000000,
1001000000,

1001000000,
1001000000,
2002000000,
4,

20202,
00202,
02000,
2,

20202,
00202,
00202,
02000,
#GOGOEND
#BPMCHANGE 135.69
0, //82

#MEASURE 4/4
#BPMCHANGE 137.86
#SCROLL 0.98
3,
#BPMCHANGE 129.38
#SCROLL 1.04
11,
#BPMCHANGE 134
#SCROLL 1.01
500000000000000000000000000000000008000000000000, //85

#BPMCHANGE 134.11
3,
#BPMCHANGE 134.51
#SCROLL 1
11,
#BPMCHANGE 132.99
#SCROLL 1.02
3,
#BPMCHANGE 134.73
#SCROLL 1
11,

#BPMCHANGE 132.88
#SCROLL 1.02
7008,
#BPMCHANGE 134.45
#SCROLL 1
7008,
#BPMCHANGE 134.36
11,
#BPMCHANGE 134.16
#SCROLL 1.01
1, //93

#BPMCHANGE 134.89
#SCROLL 1
3,
#BPMCHANGE 132.78
#SCROLL 1.02
11,
#BPMCHANGE 133.64
#SCROLL 1.01
3,
#BPMCHANGE 133.69
11,

#BPMCHANGE 138.51
#SCROLL 0.97
3,
#BPMCHANGE 130.59
#SCROLL 1.03
11,
#BPMCHANGE 134.45
#SCROLL 1
500000000000000000000000000000000008000000000000,
3,
0,
0, //103
#END