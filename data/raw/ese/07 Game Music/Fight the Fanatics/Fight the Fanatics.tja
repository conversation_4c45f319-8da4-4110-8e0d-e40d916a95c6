//TJADB Project
TITLE:Fight the Fanatics
TITLEJA:Fight the Fanatics
SUBTITLE:--<PERSON><PERSON>/Puzzle & Dragons
SUBTITLEJA:「パズル＆ドラゴンズ」より
BPM:156
WAVE:Fight the Fanatics.ogg
OFFSET:-1.806
DEMOSTART:91.036

//AC14 score only

COURSE:Oni
LEVEL:8
BALLOON:2,2
SCOREINIT:440
SCOREDIFF:100

#START
1110201110102011,
1010201110102220,
1110201110102011,
1010201110222220,

3000400000007008,
0010101011112000,
3406,
000000000000000008000000200200200200200000000000, //8

1110201110102011,
1010201110102220,
1110201110102011,
1010201110102220,

1110201210102011,
1010201210102220,
1110201210102011,
1010200011102220, //16

1110201211102022,
1010201210102212,
1010201210102212,
1011200011102220,

1110202211102011,
2211221212112120,
1000201110102220,
1021012000202222, //24

#GOGOSTART
1110202210102012,
1010201120102212,
1010201210102212,
1011202210002222,

1110202210102012,
1010201210102212,
1010202210120210,
500000000000000008000000100100100000200200200000, //32

1110202210120210,
1011201210102212,
1010201210102012,
1110202211112110,

2111211121112112,
1001002010020020,
1000201250000000,
000000000000000000000000000000000008000000000000, //40
#GOGOEND

10210120,
0012,
10210120,
0011,

10210120,
0012,
34034034,
0030400000111000, //48

1000201000112000,
0000200011102000,
1000201000112000,
0000200011102000,

1000201000112000,
0000200011102000,
2222100040003000,
4004003040040030, 
3000201120002011,
2000200011112110, //58

#GOGOSTART
1110202211102022,
1110201120102212,
1010201210102212,
1011202210002222,

1110202211102022,
1110201210102212,
1010202211120210,
500000000000000008000000100100100000200200200000, //66

1110202211120210,
1011201210102212,
1010201210102012,
1110202211112110,

2111211121112112,
1001002010020020,
1000201250000000,
000000000000000000080000, //74
#GOGOEND

3000400000007008,
0010101011112000,
3406,
000000000000000000000000000000000008000000000000,
0, //79
#END


COURSE:Hard
LEVEL:7
BALLOON:
SCOREINIT:510
SCOREDIFF:120

#START
10201120,
1000201110102000,
10201120,
1000201110222220,

3306,
000000000000000000000000000000000008000000000000,
3306,
000000000000000000000000000000000008000000000000, //8

10201121,
1000200010102220,
10201121,
10201120,

10201121,
1000200010102220,
10201121,
1233, //16

10201121,
1000200010102220,
10201121,
1233,

1000201110002011,
11221122,
1000201110002000,
3003003000404000, //24

#GOGOSTART
1000201110002011,
1000201110102020,
1000201110102000,
3000300040002220,

1000201110002011,
1000201110102020,
1000201110020020,
500000000000000008000000100000100000200000200000, //32

1000201110020020,
500000000000000008000000100000100000200000200000,
1000201110002000,
1000201110000000,

1110222011102220,
3003004030040040,
30060000,
000000000000000000000000000000000008000000000000, //40
#GOGOEND

10010010,
0012,
10010010,
0011,

10010010,
0012,
34034034,
0030400000111000, //48

10210120,
0011,
10210120,
0012,

10210120,
0011,
2222100040003000,
4004003040040030,
3000201110002011,
1000200011111000, //58

#GOGOSTART
1000201110102011,
1010201110102020,
1000201110102000,
3340,

1000201110102011,
1010201110102020,
1000201110020020,
500000000000000008000000100000100000200000200000, //66

1000201110020020,
1000201110102020,
1000201110002011,
1000200011111000,

1110222011102220,
3003004030040040,
30060000,
000000000000000000000000000000000008000000000000, //74
#GOGOEND

3306,
000000000000000000000000000000000008000000000000,
3306,
000000000000000000000000000000000008000000000000,
0, //79
#END


COURSE:Normal
LEVEL:5
BALLOON:5,10,10
SCOREINIT:570
SCOREDIFF:130

#START
10201120,
1210,
10201120,
1210,

3306,
000000000000000000000000000000000008000000000000,
3306,
000000000000000000000000000000000008000000000000, //8

10201120,
1211,
10201121,
1211,

10201120,
1211,
10201121,
1233, //16

10201121,
10201120,
5,
000008000000000000000000300000000000300000000000,

12,
11201120,
7008,
600000000000000000000000000000000008000000000000, //24

#GOGOSTART
10211021,
10211020,
33,
3340,

10211021,
10211020,
36,
000000000000000008000000000000000000000000000000, //32

10211020,
10001120,
10211020,
10211020,

3344,
7,
000000080000000000600000000000000000000000000000,
000000000000000000000000000000000008000000000000, //40
#GOGOEND

30010010,
0011,
30010010,
0011,

30010010,
0011,
33033033,
03300000, //48

30010020,
0034,
30010020,
0034,

30010010,
0012,
0343,
600000000000000000000000000000000008000000000000,
33,
33, //58

#GOGOSTART
10211021,
10211020,
33,
3340,

10211021,
10211020,
36,
000000000000000008000000000000000000000000000000, //66

10211020,
10001120,
10211020,
10211020,

3344,
7,
000000080000000000600000000000000000000000000000,
000000000000000000000000000000000008000000000000, //74
#GOGOEND

3306,
000000000000000000000000000000000008000000000000,
3306,
000000000000000000000000000000000008000000000000,
0, //79
#END


COURSE:Easy
LEVEL:4
BALLOON:3,3,3,3,4,6,8,20,6
SCOREINIT:540
SCOREDIFF:100

#START
7008,
7008,
7008,
7008,

3306,
000000000000000000000000000008000000000000000000,
3306,
000000000000000000000000000008000000000000000000, //8

11,
1111,
11,
2222,

11,
1111,
11,
2222, //16

11,
1112,
5,
000008000000000000000000300000000000300000000000,

11,
1122,
7008,
600000000000000000000000000008000000000000000000, //24

#GOGOSTART
1110,
1120,
33,
4440,

1110,
1120,
36,
000000000000000008000000000000000000000000000000, //32

33,
3022,
1110,
2220,

3333,
7,
000000080000000000600000000000000000000000000000,
000000000000000000000000000008000000000000000000, //40
#GOGOEND

0,
0033,
0,
0033,

0,
0033,
9, //internally kusudama (9)? but no 9s in iOS
000009000000000800000000, //48

0,
0033,
3,
0033,

3,
0033,
0333,
7,
0,
00000800, //58

#GOGOSTART
1110,
1120,
33,
4440,

1110,
1120,
36,
000000000000000008000000000000000000000000000000,

33,
3022,
1110,
2220,

3333,
7,
000000080000000000600000000000000000000000000000,
000000000000000000000000000008000000000000000000,
#GOGOEND

3306,
000000000000000000000000000008000000000000000000,
3306,
000000000000000000000000000008000000000000000000,
0, //79
#END