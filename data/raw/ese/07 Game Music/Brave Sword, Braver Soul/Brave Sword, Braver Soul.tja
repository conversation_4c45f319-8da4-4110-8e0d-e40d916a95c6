//TJADB Project
TITLE:Brave Sword, Braver Soul
TITLEJA:Brave Sword, Braver Soul
SUBTITLE:--<PERSON><PERSON><PERSON><PERSON>/SOULCALIBUR Ⅱ
SUBTITLEJA:「ソウルキャリバーII」より
BPM:150
WAVE:Brave Sword, Braver Soul.ogg
OFFSET:-7.480
DEMOSTART:51.480

//shinuchi: 6170/5020/2800/1890

COURSE:Oni
LEVEL:7

STYLE:SINGLE
BALLOON:4
SCOREINIT:600,2110
SCOREDIFF:158

#START
0,
3000001010201011,
1010201010201011,
1010201010201011,
1020111020101020,

3011102011101022,
1010201010201022,
1010201011101010,
200000100000100100100000500000000000000008000000, //9

3010222020101011,
1010102010201011,
1010222020101011,
100000200000100100100000500000000000000008000000,

1011101020202011,
1020111020101022,
1010201011101010,
4114, //17

40112011,
20112011,
1110101022201010,
#BPMCHANGE 149
22214000,

30112011,
#BPMCHANGE 150
2000101020111010,
#BPMCHANGE 151
400000000000400000000000500000000000000008000000,
#BPMCHANGE 150
400000000000400000000000500000000000000008000000,

400000000000400000000000600000000000000008000000,
31111111,
30001122, //28

#GOGOSTART
1011101030111010,
3011101010102011,
12121212,
700000008000100100200200,

12022202,
22022202,
12121210,
30041122, //36

1022202010202011,
1022202010101011,
12121212,
3022222010102020,

3022202010202011,
1022202030003000,
4334,

30110110,
1010001010222022,
10140140,
#BPMCHANGE 150.68
14014400, //47
#GOGOEND

#BPMCHANGE 149.32
1020102110102020,
#BPMCHANGE 150
12123300,
1020102110102020,
12123322, 

1020102110102020,
12123322,
3030102110102020,
12123322, //55

4000222011101110,
2220222030302220,
3030222011101110,
2220222030300000,

1020102110102020,
1020102030302222,
#BPMCHANGE 150.96
10110150,
000008000000100000200000100000100000100000200000,
3,
0, //65
#END

STYLE:DOUBLE
BALLOON:4
SCOREINIT:600,2110
SCOREDIFF:158

#START P1
0,
3000001010201011,
1010201010201011,
1010201010201011,
1020111020101020,

3011102011101022,
1010201010201022,
1010201011101010,
200000100000100100100000500000000000000008000000, //9

3010222020101011,
1010102010201011,
1010222020101011,
100000200000100100100000500000000000000008000000,

1011101020202011,
1020111020101022,
1010201011101010,
4114, //17

40112011,
20112011,
1110101022201010,
#BPMCHANGE 149
22214000,

30112011,
#BPMCHANGE 150
2000101020111010,
#BPMCHANGE 151
400000000000400000000000500000000000000008000000,
#BPMCHANGE 150
400000000000400000000000500000000000000008000000,

400000000000400000000000600000000000000008000000,
31111111,
30001122, //28

#GOGOSTART
1011101030111010,
3011101010102011,
12121212,
700000008000100100200200,

12022202,
22022202,
12121210,
30041122, //36

1022202010202011,
1022202010101011,
12121212,
3022222010102020,

3022202010202011,
1022202030003000,
4334,

30110110,
1010001010222022,
10140140,
#BPMCHANGE 150.68
14014400, //47
#GOGOEND

#BPMCHANGE 149.32
1020102110102020,
#BPMCHANGE 150
12123300,
1020102110102020,
12123322, 

1020102110102020,
12123322,
3030102110102020,
12123322, //55

4000222011101110,
2220222030302220,
3030222011101110,
2220222030300000,

1020102110102020,
1020102030302222,
#BPMCHANGE 150.96
10110150,
000008000000100000200000100000100000100000200000,
3,
0, //65
#END

BALLOON:4
SCOREINIT:600,2110
SCOREDIFF:158

#START P2
0,
3000001010201011,
1010201010201011,
1010201010201011,
1020111020101020,

3011102011101022,
1010201010201022,
1010201011101010,
200000100000100100100000500000000000000008000000, //9

3010222020101011,
1010102010201011,
1010222020101011,
100000200000100100100000500000000000000008000000,

1011101020202011,
1020111020101022,
1010201011101010,
4114, //17

40112011,
20112011,
1110101022201010,
#BPMCHANGE 149
22214000,

30112011,
#BPMCHANGE 150
2000101020111010,
#BPMCHANGE 151
400000000000400000000000500000000000000008000000,
#BPMCHANGE 150
400000000000400000000000500000000000000008000000,

400000000000400000000000600000000000000008000000,
31111111,
30001122, //28

#GOGOSTART
12022202,
22022202,
12121212,
700000008000100100200200,

1011101030111010,
3011101010102011,
12121210,
30041122, //36

1022202010202011,
1022202010101011,
12121212,
3022222010102020,

3022202010202011,
1022202030003000,
4334,

30110110,
1010001010222022,
10140140,
#BPMCHANGE 150.68
14014400, //47
#GOGOEND

#BPMCHANGE 149.32
1020102110102020,
#BPMCHANGE 150
12123300,
1020102110102020,
12123322, 

1020102110102020,
12123322,
3030102110102020,
12123322, //55

4000222011101110,
2220222030302220,
3030222011101110,
2220222030300000,

1020102110102020,
1020102030302222,
#BPMCHANGE 150.96
10110150,
000008000000100000200000100000100000100000200000,
3,
0, //65
#END


COURSE:Hard
LEVEL:5
BALLOON:
SCOREINIT:660,3090
SCOREDIFF:190

//CS3/AC6 chart is slightly different, as marked by comments. Score 730/170
//In addition, CS3 also has a DP chart, 2P chart swaps #29-30 with #33-34.

#START
0,
3000001000101011,
1000101000101011,
1000101000101011,
11101000,

1011101010101000,
11011011,
21111011,
200000100000100000000000500000000000000008000000, //9

30202111, //CS3: 30202110,
11101011,
00202111, //CS3: 00202110,
100000100000100000000000500000000000000008000000,

1011101010101000,
11011011,
21111011,
2114, //17

40111011,
10111011,
22111011,
#BPMCHANGE 149
22111000,

30111011,
#BPMCHANGE 150
1000101010111010,
#BPMCHANGE 151
100000000000100000000000500000000000000008000000,
#BPMCHANGE 150
200000000000200000000000500000000000000008000000,

400000000000400000000000500000000000000008000000,
11111111,
10001122, //28

#GOGOSTART
13,
3000000010101011,
1111,
500000000000000008000000100000100000200000200000,

12022202,
22022202,
1111,
30012211, //36 //CS3: 20021122,

1000000010000011,
1000000010101011,
1111,
30001122,

3000000010000011,
1033,
4334,

30110110,
11012200,
10120120,
#BPMCHANGE 150.68
12012200, //47
#GOGOEND

#BPMCHANGE 149.32
12121122,
#BPMCHANGE 150
11003300,
12121122,
11003300,

12121122,
11003300,
12121122,
11003300, //55

4000111050000000,
000000000008000000000000300000300000000000000000,
0000222050000000,
000000000008000000000000300000300000000000000000,

12121122,
12121110,
#BPMCHANGE 150.96
10110150,
000008000000100000200000100000100000100000000000,
3,
0, //65
#END


COURSE:Normal
LEVEL:5
BALLOON:
SCOREINIT:960,5510
SCOREDIFF:373

#START
0,
30030000,
1111,
30030000,
11101000,

1011,
10111000,
3110,
3110, //9

30000100,
1111,
00000100,
11101000,

0111,
10111000,
3110,
3004, //17

41,
11,
1110,
#BPMCHANGE 149
11101000,

1110,
#BPMCHANGE 150
1110,
#BPMCHANGE 151
2200,
#BPMCHANGE 150
2200,

4400,
1110,
30001110, //28

#GOGOSTART
11,
1011,
1011,
500000000000000008000000100000100000100000000000,

11,
600000000000000000000000000000000008000000000000,
1110,
30001110, //36

11,
1011,
1011,
10001110,

11,
1011,
2112,

10010010,
01004000,
10010010,
#BPMCHANGE 150.68
01001100, //47
#GOGOEND

#BPMCHANGE 149.32
1212,
#BPMCHANGE 150
10001100,
1212,
10001100,

1212,
10001100,
1212,
10002200, //55

00225000,
000008000000000000000000100000100000000000000000,
00225000,
000008000000000000000000100000100000000000000000,

1212,
10001110,
#BPMCHANGE 150.96
10010010,
0111,
3,
0, //65
#END


COURSE:Easy
LEVEL:3
BALLOON:
SCOREINIT:670,6930
SCOREDIFF:298

#START
0,
30030000,
1110,
30030000,
1110,

11,
1110,
31,
32, //9

30000100,
1110,
00000100,
1110,

0101,
1110,
3,
3003, //17

31,
11,
12,
#BPMCHANGE 149
1110,

11,
#BPMCHANGE 150
11,
#BPMCHANGE 151
2200,
#BPMCHANGE 150
2200,

4400,
500000000000000000000000000008000000000000000000,
3011, //28

#GOGOSTART
11,
1011,
11,
1011,

11,
500000000000000000000000000000000008000000000000,
11,
3011, //36

11,
1011,
11,
1011,

11,
1011,
1111,

10010010,
01000000,
10010010,
#BPMCHANGE 150.68
01003000, //47
#GOGOEND

#BPMCHANGE 149.32
1110,
#BPMCHANGE 150
10001100,
1210,
10001100,

1110,
10001100,
1210,
10001100, //55

0500,
000008000000000000000000100000100000000000000000,
0500,
000008000000000000000000100000100000000000000000,

1210,
1011,
#BPMCHANGE 150.96
10010010,
0111,
3,
0, //65
#END