//TJADB Project
TITLE:<PERSON><PERSON><PERSON>! <PERSON><PERSON><PERSON>-dan
TITLEJA:檄！帝国華撃団
SUBTITLE:--<PERSON><PERSON>/<PERSON>
SUBTITLEJA:「サクラ大戦」より
BPM:142.87
WAVE:<PERSON>ek<PERSON>! <PERSON><PERSON><PERSON>-dan.ogg
OFFSET:-1.875
DEMOSTART:49.733

COURSE:Oni
LEVEL:7
BALLOON:6,26
SCOREINIT:720
SCOREDIFF:200

//iOS score: 650/170

#START
3003003003003003,
0000222011102220,
#BPMCHANGE 141.83
3003000000201010,
1223,

#BPMCHANGE 143.29
1001202010112020,
1010201010222220,
1001202010112020,
#BPMCHANGE 142.78
1010222010002000, //8

#BPMCHANGE 142.98
3030300022112010,
3030300011221020,
#BPMCHANGE 140.97
1001202010102000,
#BPMCHANGE 144.43
1011101170000080,

#BPMCHANGE 143.94
1001201010222000,
#BPMCHANGE 141.14
1010201010222000,
#BPMCHANGE 142.88
1001202010102010,
10211120, //16

11211122,
1000201110002011,
11221120,
#BPMCHANGE 144.57
1000201022221110,

#BPMCHANGE 141.6
1001201010222020,
1010201010222020,
#BPMCHANGE 143.03
1001202010112020,
1022102010122020, //24

11211122,
1000201110102010,
11221120,
1110111022221110,
#BPMCHANGE 143.02
2220222030003000, //29

#GOGOSTART
1110200010102010,
1120221010102000,
#BPMCHANGE 142.98
1001201011221020,
1022222030003000,

#BPMCHANGE 142.07
1110200010102010,
#BPMCHANGE 142.88
1010201011102220,
#BPMCHANGE 142.8
1001201010221020,
1010222030003000, //37

1110200010102010,
#BPMCHANGE 143.11
1120221010102000,
#BPMCHANGE 142.5
1001201011221020,
#BPMCHANGE 142.8
1022222030003000,

1110200010102010,
1010201011102220,
#BPMCHANGE 142.73
1001202030003000,
7,
#BPMCHANGE 145.37
000000000000000000000000080000000000000000000000, //46

#BPMCHANGE 141.97
3003003003003003,
0000222011102220,
#BPMCHANGE 142.98
3003000020101020,
#BPMCHANGE 141.97
43034043,
#GOGOEND
0,
0, //52
#END


COURSE:Hard
LEVEL:5
BALLOON:5,12
SCOREINIT:750
SCOREDIFF:225

//iOS score: 720/180

#START
3003003003003006,
000000000000000000000000000000008000000000000000,
#BPMCHANGE 141.83
1001000000201010,
1223,

#BPMCHANGE 143.29
10121012,
11211020,
10121012,
#BPMCHANGE 142.78
11211020, //8

#BPMCHANGE 142.98
33302020,
33302020,
#BPMCHANGE 140.97
11201120,
#BPMCHANGE 144.43
1011101170000080,

#BPMCHANGE 143.94
1001101010002000,
#BPMCHANGE 141.14
11211020,
#BPMCHANGE 142.88
1001101010002010,
10211120, //16

10211122,
1000201010002011,
11211120,
#BPMCHANGE 144.57
1000101011101010,

#BPMCHANGE 141.6
1001101010002000,
11211020,
#BPMCHANGE 143.03
1001101010002010,
1000201010011020, //24

10211122,
10211021,
11211120,
600000000000000000000000000000000000000000000008,
#BPMCHANGE 143.02
0033, //29

#GOGOSTART
30201121,
1000222010001000,
#BPMCHANGE 142.98
10211121,
1000222030003000,

#BPMCHANGE 142.07
30201121,
#BPMCHANGE 142.88
500000000000000008000000200000000000100000100000,
#BPMCHANGE 142.8
1000201010111010,
1000222030003000, //37

30201121,
#BPMCHANGE 143.11
1000222010001000,
#BPMCHANGE 142.5
10211121,
#BPMCHANGE 142.8
1000222030003000,

30201121,
500000000000000008000000200000000000100000100000,
#BPMCHANGE 142.73
10213030,
900000000000000000000000000000000000090000000000,
#BPMCHANGE 145.37
080000000000000000000000000000000000000000000000, //46

#BPMCHANGE 141.97
3003003003003006,
000000000000000000000000000000008000000000000000,
#BPMCHANGE 142.98
3003000020001000,
#BPMCHANGE 141.97
43034043,
#GOGOEND
0,
0, //52
#END


COURSE:Normal
LEVEL:3
BALLOON:7,9
SCOREINIT:900
SCOREDIFF:325

//iOS score: 850/230

#START
6,
000000000000000000000000000000000008000000000000,
#BPMCHANGE 141.83
1,
2223,

#BPMCHANGE 143.29
1112,
10111000,
1122,
#BPMCHANGE 142.78
10111000, //8

#BPMCHANGE 142.98
33300000,
44400000,
#BPMCHANGE 140.97
10201110,
#BPMCHANGE 144.43
7008,

#BPMCHANGE 143.94
1110,
#BPMCHANGE 141.14
10111000,
#BPMCHANGE 142.88
1122,
12, //16

10101110,
11,
10101110,
#BPMCHANGE 144.57
500000000000000000000008000000000000000000000000,

#BPMCHANGE 141.6
1110,
10111000,
#BPMCHANGE 143.03
1122,
12, //24

10101110,
11,
10101110,
600000000000000000000000000000000000000000000008,
#BPMCHANGE 143.02
0033, //29

#GOGOSTART
30001011,
1011,
#BPMCHANGE 142.98
10011010,
1033,

#BPMCHANGE 142.07
30001011,
#BPMCHANGE 142.88
1021,
#BPMCHANGE 142.8
10011010,
1033, //37

30001011,
#BPMCHANGE 143.11
1011,
#BPMCHANGE 142.5
10011010,
#BPMCHANGE 142.8
1033,

30001011,
1021,
#BPMCHANGE 142.73
1033,
900000000000000000000000000000000000090000000000,
#BPMCHANGE 145.37
080000000000000000000000000000000000000000000000, //46

#BPMCHANGE 141.97
6,
000000000000000000000000000000000008000000000000,
#BPMCHANGE 142.98
12,
#BPMCHANGE 141.97
33033033,
#GOGOEND
0,
0, //52
#END


COURSE:Easy
LEVEL:2
BALLOON:5,7
SCOREINIT:900
SCOREDIFF:553

//iOS score: 890/320

#START
6,
000000000000000000000000000008000000000000000000,
#BPMCHANGE 141.83
1,
22,

#BPMCHANGE 143.29
11,
1110,
11,
#BPMCHANGE 142.78
1110, //8

#BPMCHANGE 142.98
3300,
4400,
#BPMCHANGE 140.97
11,
#BPMCHANGE 144.43
70000800,

#BPMCHANGE 143.94
1,
#BPMCHANGE 141.14
1,
#BPMCHANGE 142.88
11,
1, //16

11,
1,
1011,
#BPMCHANGE 144.57
500000000000000000000008000000000000000000000000,

#BPMCHANGE 141.6
1,
1,
#BPMCHANGE 143.03
11,
1, //24

11,
1,
1011,
600000000000000000000000000000000000000000000008,
#BPMCHANGE 143.02
0033, //29

#GOGOSTART
31,
1011,
#BPMCHANGE 142.98
1011,
1033,

#BPMCHANGE 142.07
32,
#BPMCHANGE 142.88
2001,
#BPMCHANGE 142.8
1011,
1033, //37

31,
#BPMCHANGE 143.11
1011,
#BPMCHANGE 142.5
1011,
#BPMCHANGE 142.8
1033,

32,
2001,
#BPMCHANGE 142.73
1033,
900000000000000000000000000000000000090000000000,
#BPMCHANGE 145.37
080000000000000000000000000000000000000000000000, //46

#BPMCHANGE 141.97
6,
000000000000000000000000000008000000000000000000,
#BPMCHANGE 142.98
1,
#BPMCHANGE 141.97
30030030,
#GOGOEND
0,
0, //52
#END