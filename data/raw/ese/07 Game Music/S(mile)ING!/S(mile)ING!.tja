//TJADB Project
TITLE:S(mile)ING!
TITLEJA:S(mile)ING!
SUBTITLE:--<PERSON><PERSON> feat. <PERSON><PERSON><PERSON>/THE iDOLM@STER ONE FOR ALL
SUBTITLEJA:「アイドルマスター シンデレラガールズ」より
BPM:178
WAVE:S(mile)ING!.ogg
OFFSET:-1.604
DEMOSTART:51.477

//shinuchi: 6800/4480/2770/1830

COURSE:Oni
LEVEL:6
BALLOON:
SCOREINIT:540,1920
SCOREDIFF:138

#START
20202012,
02020200,
20202012,
02020220, //4

20202012,
02020220,
2000000020000022,
2020202022202220, //8

10121020,
10121022,
10122021,
0020202000222000, //12

10121022,
10120210,
10210021,
0020002000222000, //16

10121020,
10121022,
12202022,
02021122, //20

00201122,
00121210,
3000222000222000,
2000112011201120, //24

3000002220002010,
20212020,
3000002220002010,
20121020, //28

300000000000600000000000000008000000400000000000,
0436,
000008000000300000000000400000000000600000000000,
000000000008000000000000200000000000100100100000, //32

20221210,
10120120,
3000400011102000,
1205,
000000000000000000000000000000000008000000000000, //37

#GOGOSTART
3000222020001020,
10212010,
10222020,
1110222011102010, //41

20212120,
01210120,
1110200011105000,
000008000000200000000000100000200000100000000000, //45

3000222020001020,
10212010,
10222020,
1110222011102220,
1212,
1233, //51
#GOGOEND

30000222,
0020002000222000,
22120201,
02010233, //55

0022202000200010,
02210222,
12012020,
11201120, //59

10201120,
10210120,
10201120,
1000201000102220, //63

10121021,
01210120,
10121021,
20120050, 
000000000000000000000008000000000000000000000000,
500000000000000000000000000000000008000000000000, //69

#GOGOSTART
20022010,
20102210,
20012020,
2000200010102220, //73

1000202011102000,
0011102000201000,
1110222011102010,
20212120, //77

3000222020001020,
1000201022201020,
10212020,
1110222011102220,
1212,
1233, //83

4000222020001020,
1000201022201020,
20212020,
1110222011102220, //87

1212,
1233,
0,
0005,
0,
000000000000000000000000000000000000000000000008,
#GOGOEND
0, //94

1000200011102010,
0020201000222000,
1000200011102010,
02212121, //98

0300,
0,
0,
0, //102
#END


COURSE:Hard
LEVEL:4
BALLOON:
SCOREINIT:610,3150
SCOREDIFF:165

#START
20000002,
02020000,
20000002,
02020000,

20000002,
02020000,
2,
2, //8

1112,
10101022,
00022001,
02220220,

1112,
10110110,
10030002,
02020220, //16

1112,
10101011,
01102002,
02001111,

00201111,
0111,
30200200,
2, //24

30000022,
20222020,
30000022,
20222020,

1202,
0112,
0115,
000000000008000000000000200000000000000000000000, //32

2011,
10110000,
30401110,
1205,
000000000000000000000000000000000008000000000000, //37

#GOGOSTART
3002,
10201120,
10225000,
000008000000000000000000100000100000200000000000,

10201120,
01200020,
1112,
00202220,

3002,
10201120,
10225000,
000008000000000000000000100000100000200000000000,
3434,
3433, //51
#GOGOEND

30000222,
02020200,
11220102,
01020144,

02220202,
01020110,
500000000000000000000000000000000008000000000000,
3232, //59

31,
20020000,
31,
20020000,

10102022,
02220220,
10222020,
10010050,
000000000000000000000008000000000000000000000000,
500000000000000000000000000000000008000000000000, //69

#GOGOSTART
2001,
20102210,
20012020,
2212,

10201120,
01200010,
1121,
00102220, //77

3001,
20101120,
10225000,
000008000000000000000000100000100000200000000000,

3434,
3433,
4001,
20101120, //85

10225000,
000008000000000000000000100000100000200000000000,
3434,
3433,

0,
0005,
0,
000000000000000000000000000000000000000000000008,
#GOGOEND
0, //94

10102021,
02020000,
10102021,
02222222,

0300,
0,
0,
0, //102
#END


COURSE:Normal
LEVEL:4
BALLOON:16,16
SCOREINIT:780,5360
SCOREDIFF:245

#START
2,
0,
2,
0,

2,
0,
2,
2, //8

1110,
1111,
02,
0200,

1110,
10110110,
10010002,
02020000, //16

1110,
10101011,
0100,
00001011,

00001011,
0112,
3,
0112, //24

30000011,
12,
30000011,
12,

1202,
0111,
0115,
000000000008000000000000100000000000000000000000, //32

21,
10110000,
3400,
1205,
000000000000000000000000000000000008000000000000, //37

#GOGOSTART
3001,
1125,
0,
000008000000000000000000100000000000100000000000,

1022,
0201,
1112,
0222,

3001,
1125,
0,
000008000000000000000000200000000000200000000000,
33,
3033, //51

#GOGOEND
9,
09,
8,
00000044,

09000000,
000000000000090000000000,
8,
33, //59

3,
20020000,
3,
20020000,

1120,
1120,
1122,
10010050,
000000000000000000000008000000000000000000000000,
500000000000000000000000000000000008000000000000, //69

#GOGOSTART
2,
2,
22,
2200,

1112,
0201,
1111,
0222, //77

3001,
1125,
0,
000008000000000000000000200000000000200000000000,

33,
3033,
3001,
1125, //85

0,
000008000000000000000000200000000000200000000000,
33,
3033,

0,
0005,
0,
000000000000000000000000000000000000000000000008,
#GOGOEND
0, //94

10101011,
0,
10101011,
0,

0300,
0,
0,
0, //102
#END


COURSE:Easy
LEVEL:2
BALLOON:10,10
SCOREINIT:690,8830
SCOREDIFF:293

#START
1,
0,
2,
0,

2,
0,
2,
2, //8

1110,
11,
0,
0,

1110,
11,
1,
0, //16

1110,
11,
1,
0011,

0011,
0111,
3,
0, //24

3,
0,
3,
0,

1101,
0101,
0105,
000000000008000000000000000000000000000000000000, //32

22,
2,
3300,
1005,
000000000000000000000000000008000000000000000000, //37

#GOGOSTART
3,
1115,
0,
000008000000000000000000000000000000000000000000,

1111,
0,
1111,
0,

3,
1115,
0,
000008000000000000000000000000000000000000000000,
33,
3033, //51
#GOGOEND

9,
09,
8,
0004,

09000000,
000000000000090000000000,
8,
33, //59

3,
2,
3,
2,

1,
1,
1,
1005,
000000000000000000000008000000000000000000000000,
500000000000000000000000000008000000000000000000, //69

#GOGOSTART
2,
2,
2,
2200,

1111,
0,
1111,
0, //77

3,
1115,
0,
000008000000000000000000000000000000000000000000,

33,
3033,
3,
1115, //85

0,
000008000000000000000000000000000000000000000000,
33,
3033,

0,
0005,
0,
000000000000000000000000000000000000000000000008,
#GOGOEND
0, //94

11,
0,
11,
0,

0300,
0,
0,
0, //102
#END