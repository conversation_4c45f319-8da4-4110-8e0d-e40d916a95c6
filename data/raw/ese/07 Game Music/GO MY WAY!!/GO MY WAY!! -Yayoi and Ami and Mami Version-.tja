//TJADB Project
TITLE:GO MY WAY‼ -<PERSON><PERSON><PERSON> & <PERSON><PERSON> & <PERSON><PERSON> Version-
TITLEJA:GO MY WAY!!
SUBTITLE:--<PERSON><PERSON> feat. <PERSON><PERSON>/THE iDOLM@STER
SUBTITLEJA:別歌唱バージョン ‐ やよい・亜美・真美
BPM:180
WAVE:GO MY WAY!! -<PERSON><PERSON><PERSON> and <PERSON><PERSON> and <PERSON><PERSON> Version-.ogg
OFFSET:-1.499
DEMOSTART:67.658

COURSE:Edit
LEVEL:7
BALLOON:16,16,16,16,16,16
SCOREINIT:510,1680
SCOREDIFF:130

#START
0202,
00200220,
00200020,
22020220,

00200020,
20020000,
1,
1000000022222020,

#GOGOSTART
30200112,
10120121,
00200112,
12201220,

10200112,
10120121,
33033030,
2223,
#GOGOEND

#SECTION
#BRANCHSTART p,75,90
#N
11000120,
11000120,
11000120,
11201210,

11000120,
11000120,
11001100,
01221010, //n24

#E
11000120,
11000120,
11000120,
11221210,

11000120,
11000120,
11001100,
01221010, //e24

#M
11000120,
11200120,
11001120,
11221210,

11000120,
11200120,
11201120,
01221010, //m24

#SECTION
#BRANCHSTART p,75,90
#N
12021202,
12021202,
12021202,
11201210,

12021202,
12021202,
11101110,
01221040, //n32

#E
12021202,
12021120,
12021202,
11221210,

12021202,
12021122,
12201220,
01221040, //e32

#M
1020002010201120,
12021120,
1020002010201120,
11221210,

1020002010201120,
12021122,
12201220,
40221040, //m32

#SECTION
#BRANCHSTART p,75,90
#N
10210120,
10212220,
10210120,
1000201022202000,

10210120,
10212220,
10210122,
1000201022202000, //n40

#E
10210120,
1000201022202000,
10210120,
1000201022202020,

10210120,
1000201022202000,
10210122,
1000201022222020, //e40

#M
10210121,
1000201022202010,
10210120,
1000201022202020,

10210121,
1000201022202010,
10210122,
1000201022222020, //m40

#SECTION
#BRANCHSTART p,75,90
#N
10220120,
10221020,
10220120,
10221020,

12101210,
12121212,
10120120,
20121120,

11000011,
500000000000000008000000
#GOGOSTART
000000100000200000200000, //n50

#E
10220120,
10221120,
10220120,
10221120,

12121212,
12121212,
10120120,
20121120,

11000011,
500000000000000008000000
#GOGOSTART
000000100000200000200000, //e50

#M
10221120,
10221121,
10221120,
10221121,

12121212,
1120112011201120,
10120120,
20121120,

11000011,
500000000000000008000000
#GOGOSTART
000000100000200000200000, //m50

#SECTION
#BRANCHSTART p,75,90
#N
10220122,
10220122,
12101210,
1000201022202000,

12121212,
11020122,
12021202,
11020122, //n58
 
#E
10220122,
1110202000102020,
1020112010201120,
1010201022222020,

1020112010201120,
11020122,
1022102210221020,
11020122, //e58

#M
1000202011102020,
1110202011102220,
1020112010201120,
1010201022112020,

1020112010201120,
1010002000102220,
1022102210221020,
11020122, //m58

#SECTION
#BRANCHSTART p,75,90
#N
10220122,
10220122,
12101210,
1000201022202000,

12121212,
11020122,
12021202,
33,

7,
00080000,
#GOGOEND
33000033,
600000000000000008000000
#GOGOSTART
000000100000200000200000, //n70

#E
10220122,
1110202000102020,
1020112010201120,
1010201022222020,

1020112010201120,
11020122,
1113,
33,

7,
00080000,
#GOGOEND
33000033,
600000000000000008000000
#GOGOSTART
000000100000200000200000, //e70

#M
1000202011102020,
1110202011102220,
1020112010201120,
1010201022112020,

1020112010201120,
1010002000102220,
1020112010201120,
33,

7,
00080000,
#GOGOEND
33000033,
600000000000000008000000
#GOGOSTART
000000100000200000200000, //m70

#SECTION
#BRANCHSTART p,75,90
#N
10220122,
10220122,
12101210,
1000201022202000,

12121212,
11020122,
12021202,
11020122, //n78

#E
10220122,
1110202000102020,
1020112010201120,
1010201022222020,

1020112010201120,
11020122,
102210221022102,
11020122, //e78

#M
1000202011102020,
1110202011102220,
1020112010201120,
1010201022112020,

1020112010201120,
1010002000102220,
1022102210221020,
11020122, //m78

#SECTION
#BRANCHSTART p,75,90
#N
10220122,
10220122,
12101210,
1000201022202000,

12121212,
11020122,
12021202,
33,

7,
0000008000000211,
#GOGOEND
1,
0, //n90

#E
1020102010112020,
1020102010112020,
1020112010201120,
1010201022222020,

1020112010201120,
11020122,
1111,
33,

7,
0000008000000211,
#GOGOEND
1,
0, //e90

#M
1020112010201120,
1020112010201120,
1022102010221020,
1010201022112020,

1020112010201120,
11020122,
1111,
33,

7,
0000008000000211,
#GOGOEND
1,
0, //m90

#BRANCHEND
#END



COURSE:Oni
LEVEL:8
BALLOON:2,2,13,13,13
SCOREINIT:530,1610
SCOREDIFF:128

#START
2,
20000222,
0222,
22020002,

2222,
22020010,
1111,
1000000022222020, //8

#GOGOSTART
10201121,
1110201000102010,
10201121,
01211121,

10201121,
1110201000102222,
7080007080002000,
333
#SCROLL 3
4, //16
#GOGOEND

#SCROLL 1
11200120,
11200120,
11200120,
1110201011102000,

11200120,
11200120,
1111200011112000,
03303000, //24

1022102010221020,
1120102011201020,
1022102010221020,
1122102011221000,

1022102010221020,
1120102011201020,
1111200011112000,
033030
#SCROLL 3
40, //32

#SCROLL 1
500000000000000000000008000000000000400000000000,
500000000000000000000008000000000000400000000000,
500000000000000000000008000000000000400000000000,
500000000000000000000008000000000000400000000000,

500000000000000000000008000000000000400000000000,
500000000000000000000008000000000000400000000000,
500000000000000000000008000000000000400000000000,
500000000000000000000008000000000000400000000000, //40

10221121,
01221120,
10221121,
01221010,

12121212,
1120112011201120,
7008,
7008, //48

600000000000000000000000000008000000000000000000,
#SCROLL 3
40000000
#SCROLL 1
#GOGOSTART
00102011, //50

1010400000102011,
1010400000102012,
1010201110102012,
1010201000102011,

2010201120102011,
2010201000102022,
1011202210112022,
1120003000102011, //58

1010400000102011,
1010400000102012,
1010201110102012,
1010201000102011, 

2010201120102011,
2010201000102000,
1111200011112000,
1222200012222000, //66

7,
8,
#GOGOEND
600000000000000000000000000008000000000000000000,
#SCROLL 4
40000000
#SCROLL 1
#GOGOSTART
00102011, //70

1010400000102011,
1010400000102012,
1010201110102012,
1010201000102011,

2010201120102011,
2010201000102022,
1011202210112022,
1120003022222011, //78

2010401020102011,
2010401020102011,
2010201120102011,
2010201000102011,

2011201120112022,
1011201122102000,
1111200011112000,
3000222030002220, //86

6,
000000000000000000000008000000000000000200100100,
#GOGOEND
1,
0,
#END


COURSE:Hard
LEVEL:4
BALLOON:9,9,13
SCOREINIT:550,2560
SCOREDIFF:150

#START
2,
2,
22,
2,

22,
22020010,
1111,
11, //8

#GOGOSTART
0303,
0303,
0303,
0303,

0303,
0303,
11011010,
3334, //16
#GOGOEND

11200120,
11200120,
11200120,
11203030,

11200120,
11200120,
11201120,
03303000, //24

10201120,
10201120,
10201120,
1133,

10201120,
10201120,
11201120,
03303040, //32

500000000000000000000008000000000000400000000000,
500000000000000000000008000000000000400000000000,
500000000000000000000008000000000000400000000000,
500000000000000000000008000000000000400000000000,

500000000000000000000008000000000000400000000000,
500000000000000000000008000000000000400000000000,
500000000000000000000008000000000000400000000000,
500000000000000000000008000000000000400000000000, //40

10221021,
01221020,
10221021,
01221010,

1111,
1222,
7008,
7008, //48

600000000000000000000000000008000000000000000000,
4000
#GOGOSTART
0121, //50

00400121,
00400121,
0222,
3333,

10201120,
11210120,
11201120,
11030121, //58

00400121,
00400121,
0122,
3333,

10201120,
11210120,
11201120,
11201120, //66

7,
8,
#GOGOEND
600000000000000000000000000008000000000000000000,
4000
#GOGOSTART
0121, //70

00400121,
00400121,
0222,
3333,

10201120,
11210120,
11201120,
11030121, //78

00400121,
00400121,
0222,
3333,

10201120,
11210120,
11201120,
33, //86

6,
000000000000000000000008000000000000000000000000,
#GOGOEND
0,
0,
#END


COURSE:Normal
LEVEL:3
BALLOON:6,6,8
SCOREINIT:570,3730
SCOREDIFF:193

#START
2,
2,
22,
2,

2,
2001,
1111,
11, //8

#GOGOSTART
0303,
0303,
0303,
0303,

0303,
0303,
0,
3333, //16
#GOGOEND

0202,
0202,
0202,
0233,

0202,
0202,
1111,
03303000, //24

1122,
1110,
1122,
1133,

1122,
1110,
1111,
03303040, //32

0004,
0004,
500000000000000000000008000000000000400000000000,
500000000000000000000008000000000000400000000000,

500000000000000000000008000000000000400000000000,
500000000000000000000008000000000000400000000000,
500000000000000000000008000000000000400000000000,
500000000000000000000008000000000000400000000000, //40

0303,
0404,
0303,
0404,

1111,
1222,
7008,
7008, //48

600000000000000000000000000008000000000000000000,
4
#GOGOSTART
0, //50

0401,
0401,
0101,
3333,

0202,
0202,
1111,
10030000, //58

0401,
0401,
0101,
3333,

0202,
0201,
1111,
11, //66

7,
8,
#GOGOEND
600000000000000000000000000008000000000000000000,
4
#GOGOSTART
0, //70

0401,
0401,
0101,
3333,

0202,
0202,
1111,
10030000, //78

0401,
0401,
0101,
3333,

0202,
0201,
1111,
33, //86

6,
000000000000000000000008000000000000000000000000,
#GOGOEND
0,
0,
#END


COURSE:Easy
LEVEL:3
BALLOON:4,4,6
SCOREINIT:450,4280
SCOREDIFF:155

#START
2,
2,
22,
2,

2,
2,
11,
11, //8

#GOGOSTART
0303,
0303,
0303,
0303,

0303,
0303,
0,
3333, //16
#GOGOEND

0002,
0002,
0002,
0033,

0002,
0002,
0202,
03303000, //24

0101,
0101,
0101,
0133,

0101,
0101,
0101,
03303040, //32

0004,
0004,
500000000000000000000008000000000000400000000000,
500000000000000000000008000000000000400000000000,

500000000000000000000008000000000000400000000000,
500000000000000000000008000000000000400000000000,
500000000000000000000008000000000000400000000000,
500000000000000000000008000000000000400000000000, //40

0303,
0404,
0303,
0404,

0111,
0222,
7008,
7008, //48

600000000000000000000000000008000000000000000000,
3
#GOGOSTART
0, //50

0401,
0401,
0101,
3333,

0202,
0202,
0101,
0101, //58

0401,
0401,
0101,
3333,

0202,
0202,
0101,
0101, //66

7,
8,
#GOGOEND
600000000000000000000000000008000000000000000000,
4
#GOGOSTART
0, //70

0401,
0401,
0101,
3333,

0202,
0202,
0101,
0101, //78

0401,
0401,
0101,
3333,

0202,
0202,
0111,
33, //86

6,
000000000000000000000008000000000000000000000000,
#GOGOEND
0,
0,
#END