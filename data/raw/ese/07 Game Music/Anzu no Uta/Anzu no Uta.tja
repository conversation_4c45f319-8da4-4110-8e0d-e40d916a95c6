//TJADB Project
TITLE:<PERSON><PERSON> no Uta
TITLEJA:あんずのうた
SUBTITLE:--<PERSON><PERSON><PERSON><PERSON> feat. <PERSON><PERSON><PERSON>/THE iDOLM@STER ONE FOR ALL
SUBTITLEJA:「アイドルマスター シンデレラガールズ」より
BPM:198.17
WAVE:<PERSON><PERSON> no Uta.ogg
OFFSET:-4.152
DEMOSTART:15.251

//Shinuchi: 4640/2840/2170/1730

COURSE:Oni
LEVEL:7
BALLOON:39,4,4,4
SCOREINIT:560,1860
SCOREDIFF:138

#START
#SCROLL 0.98
3,
#BPMCHANGE 201.06
#SCROLL 0.96
3,
#BPMCHANGE 188.46
#SCROLL 1.03
3,
#BPMCHANGE 194.61
#SCROLL 1
7, //4

0,
0,
0,
8,
0, //9

#BPMCHANGE 195
1022102010200020,
1022102010002000,
1022102010200020,
1022102010201110, //13

1022102010200020,
1022102010002000,
1022102010201120,
500000000000000000000000000000000008000000000000, //17

1221
#GOGOSTART
4121,
#GOGOEND
0121
#GOGOSTART
4141,
#GOGOEND
12212121,
#GOGOSTART
41414141, //21
#GOGOEND

1221
#GOGOSTART
4121,
#GOGOEND
0121
#GOGOSTART
4141,
#GOGOEND
12212121,
000000100000200000100000500000000000000008000000, //25

100000000000200000100000000000500000000008000000,
100000000000200000100000000000500008000000200000,
100000000000200000100000000000500000000008000000,
10212000, //29

#BARLINEOFF
22,
20002022,
#BARLINEON
22224343, //32

3022102010200020,
1022102010002000,
1022102010200020,
1022102011201110, //36

1022102010200020,
1022102011102000,
1022102010201120,
500000000000000000000000000000000008000000000000, //40

10012102,
01212002,
10102101,
01011021, //44

10012102,
0010201020002220,
5,
0, //48

0,
000000000000000000000000000000000000000000000008,
0,
0, //52

#BRANCHSTART p, -2, -1
#N
#GOGOSTART
12210212,
1022100040004000,
12212212,
1022100040004000, //n56

1022201020102010,
0022201022102000,
33034043,
000000600000000000000000000000000000000008000000, //n60

12210212,
1022100040004000,
1020201020221020,
1022100040004000, //n64

1022201022102010,
0022201022102000,
34034043,
03307080, //n68

33,
3033,
#GOGOEND
0, //n71

#E
#GOGOSTART
12210212,
1022100040004000,
12212212,
1022100040004000, //e56

1022201020102010,
0022201022102000,
33034043,
000000600000000000000000000000000000000008000000, //e60

12210212,
1022100040004000,
1020201020221020,
1022100040004000, //e64

1022201022102010,
0022201022102000,
34034043,
03307080, //e68

33,
3033,
#GOGOEND
0, //e71

#M
#GOGOSTART
12210212,
1022100040004000,
12212212,
1022100040004000, //m56

1022201020102010,
0022201022102000,
33034043,
000000600000000000000000000000000000000008000000, //m60

12210212,
1022100040004000,
1020201020221020,
1022100040004000, //m64

1022201022102010,
0022201022102000,
34034043,
03307080, //m68

33,
3033,
#GOGOEND
0, //m71

#BRANCHSTART p, 101, 102
#N
0, //n72

600000000000000008000000100000200000100000200000,
600000000000000008000000100000200000100000200000,
1022102010201110,
1022102010221020, //n76

3020102010201110,
3020102010201120,
3022102010201110,
3022102010201120, //n80

#E
0, //e72

600000000000000008000000100000200000100000200000,
600000000000000008000000100000200000100000200000,
1022102010201110,
1022102010221020, //e76

3020102010201110,
3020102010201120,
3022102010201110,
3022102010201120, //e80

#M
0, //m72

600000000000000008000000100000200000100000200000,
600000000000000008000000100000200000100000200000,
1022102010201110,
1022102010221020, //m76

3020102010201110,
3020102010201120,
3022102010201110,
3022102010201120, //m80

#SECTION
#BRANCHSTART p, 101, 102
#N
3111,
3111,
6,
000000000000000000000000000000000000000000000008,
0,
0, //n86

#E
3111,
3111,
6,
000000000000000000000000000000000000000000000008,
0,
0, //e86

#M
3111,
3111,
6,
000000000000000000000000000000000000000000000008,
0,
0, //m86

#BRANCHSTART r, -1, 1
#N
0,
#BARLINEOFF
0,
0,
0002, //n90

#BARLINEON
#MEASURE 1/8
#SCROLL 0.75
0,
0,
0,
#MEASURE 1/4
0,
#MEASURE 1/8
0,
0,
#MEASURE 1/8
0,

#BARLINEOFF
#MEASURE 1/4
0,
#BARLINEON
0,
#GOGOSTART
0,
0,
#GOGOEND

#MEASURE 1/8
0,
0,
0,
#MEASURE 1/4
0,
#MEASURE 1/8
0,
0,
0,

#BARLINEOFF
0,
#BARLINEON
0,
#MEASURE 1/4
0,
#GOGOSTART
0,
0,
#GOGOEND

0,
#MEASURE 1/8
0,
0,
#MEASURE 1/4
0,
#MEASURE 1/8
0,
0,

#BARLINEOFF
0,
#BARLINEON
#MEASURE 1/4
0,
#MEASURE 1/8
0,
#MEASURE 2/4
0,

#MEASURE 1/8
0,
#MEASURE 1/4
0,
#MEASURE 1/8
0,
#MEASURE 1/4
0,
#GOGOSTART
0,
#GOGOEND

#BARLINEOFF
#MEASURE 4/4
#SCROLL 1
0, //n126

#BARLINEON
#GOGOSTART
12210212,
1022100040004000,
12212212,
1022100040004000, //n130

1022201020102010,
0022201022102000,
33034043,
03402020, //n134

33,
3033,
#GOGOEND
0,
0, //n138

#GOGOSTART
3,
#BARLINEOFF
0,
#GOGOEND
0,
0, //n142

#SCROLL 0.5
0002000000000000, //n143

#E
0,
#BARLINEOFF
0,
0,
0002, //e90

#BARLINEON
#MEASURE 1/8
#SCROLL 0.75
0,
0,
0,
#MEASURE 1/4
0,
#MEASURE 1/8
0,
0,
#MEASURE 1/8
0,

#BARLINEOFF
#MEASURE 1/4
0,
#BARLINEON
0,
#GOGOSTART
0,
0,
#GOGOEND

#MEASURE 1/8
0,
0,
0,
#MEASURE 1/4
0,
#MEASURE 1/8
0,
0,
0,

#BARLINEOFF
0,
#BARLINEON
0,
#MEASURE 1/4
0,
#GOGOSTART
0,
0,
#GOGOEND

0,
#MEASURE 1/8
0,
0,
#MEASURE 1/4
0,
#MEASURE 1/8
0,
0,

#BARLINEOFF
0,
#BARLINEON
#MEASURE 1/4
0,
#MEASURE 1/8
0,
#MEASURE 2/4
0,

#MEASURE 1/8
0,
#MEASURE 1/4
0,
#MEASURE 1/8
0,
#MEASURE 1/4
0,
#GOGOSTART
0,
#GOGOEND

#BARLINEOFF
#MEASURE 4/4
#SCROLL 1
0, //e126

#BARLINEON
#GOGOSTART
12210212,
1022100040004000,
12212212,
1022100040004000, //e130

1022201020102010,
0022201022102000,
33034043,
03402020, //e134

33,
3033,
#GOGOEND
0,
0, //e138

#GOGOSTART
3,
#BARLINEOFF
0,
#GOGOEND
0,
0, //e142

#SCROLL 0.5
0002000000000000, //e143

#M
0,
#BARLINEOFF
0,
0,
0002, //m90

#BARLINEON
#MEASURE 1/8
#SCROLL 0.75
2,
2,
2,
#MEASURE 1/4
2,
#MEASURE 1/8
2,
2,
#MEASURE 1/8
2,

#BARLINEOFF
#MEASURE 1/4
0,
#BARLINEON
2,
#GOGOSTART
4,
4,
#GOGOEND

#MEASURE 1/8
2,
2,
2,
#MEASURE 1/4
2,
#MEASURE 1/8
2,
2,
2,

#BARLINEOFF
0,
#BARLINEON
2,
#MEASURE 1/4
2,
#GOGOSTART
4,
4,
#GOGOEND

2,
#MEASURE 1/8
2,
2,
#MEASURE 1/4
2,
#MEASURE 1/8
2,
2,

#BARLINEOFF
0,
#BARLINEON
#MEASURE 1/4
2,
#MEASURE 1/8
2,
#MEASURE 2/4
2,

#MEASURE 1/8
2,
#MEASURE 1/4
2,
#MEASURE 1/8
2,
#MEASURE 1/4
2,
#GOGOSTART
4,
#GOGOEND

#BARLINEOFF
#MEASURE 4/4
#SCROLL 1
0, //m126

#BARLINEON
#GOGOSTART
1020201020221020,
1022100040004000,
1022201020201020,
1122100040004000, //m130

1022221020112010,
0011221022102000,
33034043,
03402020, //m134

33,
3033,
#GOGOEND
0,
0, //m138

#GOGOSTART
3,
#BARLINEOFF
0,
#GOGOEND
0,
0, //m142

#SCROLL 0.5
0002000000000000, //m143
#END


COURSE:Hard
LEVEL:6
BALLOON:30,3,3,3
SCOREINIT:550,2400
SCOREDIFF:143

#START
#SCROLL 0.98
3,
#BPMCHANGE 201.06
#SCROLL 0.96
3,
#BPMCHANGE 188.46
#SCROLL 1.03
3,
#BPMCHANGE 194.61
#SCROLL 1
7, //4

0,
0,
0,
8,
0, //9

#BPMCHANGE 195
12121201,
01121020,
12121201,
01121200, //13

12121202,
02121020,
10102205,
000000000000000000000000000000000008000000000000, //17

1121
#GOGOSTART
4021,
#GOGOEND
0121
#GOGOSTART
4040,
#GOGOEND
11212121,
#GOGOSTART
4444, //21
#GOGOEND

1121
#GOGOSTART
4021,
#GOGOEND
0121
#GOGOSTART
4040,
#GOGOEND
11212121,
000000100000200000000000500000000000000008000000, //25

100000000000200000100000000000500000000008000000,
100000000000200000100000000000500000000008000000,
100000000000200000100000000000500000000008000000,
10212000, //29

#BARLINEOFF
22,
20002022,
#BARLINEON
22224444, //32

32121201,
01121020,
12121201,
01121200, //36

12121202,
02121020,
10102105,
000000000000000000000000000000000008000000000000, //40

10012102,
01122001,
10102201,
01011011, //44

10011102,
01122020,
5,
0, //48

0,
000000000000000000000000000000000000000000000008,
0,
0, //52

#BRANCHSTART p, -2, -1
#N
#GOGOSTART
11210112,
01104040,
11210112,
02104040, //e56

10112021,
02021020,
33033044,
000000600000000000000000000000000000000008000000, //e60

11210112,
01104040,
11210212,
12104040, //e64

10222021,
02221020,
34034044,
03307080, //e68

33,
3033,
#GOGOEND
0, //e71

#E
#GOGOSTART
11210112,
01104040,
11210112,
02104040, //e56

10112021,
02021020,
33033044,
000000600000000000000000000000000000000008000000, //e60

11210112,
01104040,
11210212,
12104040, //e64

10222021,
02221020,
34034044,
03307080, //e68

33,
3033,
#GOGOEND
0, //e71

#M
#GOGOSTART
11210112,
01104040,
11210112,
02104040, //m56

10112021,
02021020,
33033044,
000000600000000000000000000000000000000008000000, //m60

11210112,
01104040,
11210212,
12104040, //m64

10222021,
02221020,
34034044,
03307080, //m68

33,
3033,
#GOGOEND
0, //m71

#BRANCHSTART p, 101, 102
#N
0, //n72

600000000000000008000000100000200000100000000000,
600000000000000008000000100000200000100000000000,
12121211,
12121212, //n76

600000000000000008000000100000200000100000100000,
600000000000000008000000100000200000100000100000,
12121211,
12121222, //n80

#E
0, //e72

600000000000000008000000100000200000100000000000,
600000000000000008000000100000200000100000000000,
12121211,
12121212, //e76

600000000000000008000000100000200000100000100000,
600000000000000008000000100000200000100000100000,
12121211,
12121222, //e80

#M
0, //m72

600000000000000008000000100000200000100000000000,
600000000000000008000000100000200000100000000000,
12121211,
12121212, //m76

600000000000000008000000100000200000100000100000,
600000000000000008000000100000200000100000100000,
12121211,
12121222, //m80

#SECTION
#BRANCHSTART p, 101, 102
#N
3111,
3111,
6,
000000000000000000000000000000000000000000000008,
0,
0, //n86

#E
3111,
3111,
6,
000000000000000000000000000000000000000000000008,
0,
0, //e86

#M
3111,
3111,
6,
000000000000000000000000000000000000000000000008,
0,
0, //m86

#BRANCHSTART r, -1, 1
#N
0,
#BARLINEOFF
0,
0,
0002, //n90

#BARLINEON
#MEASURE 1/8
#SCROLL 0.75
0,
#BARLINEOFF
0,
#BARLINEON
0,
#MEASURE 1/4
0,
#MEASURE 1/8
0,
#BARLINEOFF
0,
#BARLINEON
#MEASURE 1/8
0,

#BARLINEOFF
#MEASURE 1/4
0,
#BARLINEON
0,
#GOGOSTART
0,
0,
#GOGOEND

#MEASURE 1/8
0,
#BARLINEOFF
0,
#BARLINEON
0,
#MEASURE 1/4
0,
#MEASURE 1/8
0,
#BARLINEOFF
0,
#BARLINEON
0,

#BARLINEOFF
0,
#BARLINEON
0,
#MEASURE 1/4
0,
#GOGOSTART
0,
0,
#GOGOEND

0,
#MEASURE 1/8
0,
#BARLINEOFF
0,
#BARLINEON
#MEASURE 1/4
0,
#BARLINEOFF
#MEASURE 1/8
0,
#BARLINEON
0,

#BARLINEOFF
0,
#BARLINEON
#MEASURE 1/4
0,
#MEASURE 1/8
0,
#MEASURE 2/4
0,

#MEASURE 1/8
0,
#MEASURE 1/4
0,
#MEASURE 1/8
0,
#MEASURE 1/4
0,
#GOGOSTART
0,
#GOGOEND

#BARLINEOFF
#MEASURE 4/4
#SCROLL 1
0, //n126

#BARLINEON
#GOGOSTART
11210212,
02104040,
11210212,
12104040, //n130

10221021,
02021020,
33034044,
03302020, //n134

33,
3033,
#GOGOEND
0,
0, //n138

#GOGOSTART
3,
#BARLINEOFF
0,
#GOGOEND
0,
0, //n142

#SCROLL 0.5
0002000000000000, //n143

#E
0,
#BARLINEOFF
0,
0,
0002, //e90

#BARLINEON
#MEASURE 1/8
#SCROLL 0.75
0,
#BARLINEOFF
0,
#BARLINEON
0,
#MEASURE 1/4
0,
#MEASURE 1/8
0,
#BARLINEOFF
0,
#BARLINEON
#MEASURE 1/8
0,

#BARLINEOFF
#MEASURE 1/4
0,
#BARLINEON
0,
#GOGOSTART
0,
0,
#GOGOEND

#MEASURE 1/8
0,
#BARLINEOFF
0,
#BARLINEON
0,
#MEASURE 1/4
0,
#MEASURE 1/8
0,
#BARLINEOFF
0,
#BARLINEON
0,

#BARLINEOFF
0,
#BARLINEON
0,
#MEASURE 1/4
0,
#GOGOSTART
0,
0,
#GOGOEND

0,
#MEASURE 1/8
0,
#BARLINEOFF
0,
#BARLINEON
#MEASURE 1/4
0,
#BARLINEOFF
#MEASURE 1/8
0,
#BARLINEON
0,

#BARLINEOFF
0,
#BARLINEON
#MEASURE 1/4
0,
#MEASURE 1/8
0,
#MEASURE 2/4
0,

#MEASURE 1/8
0,
#MEASURE 1/4
0,
#MEASURE 1/8
0,
#MEASURE 1/4
0,
#GOGOSTART
0,
#GOGOEND

#BARLINEOFF
#MEASURE 4/4
#SCROLL 1
0, //e126

#BARLINEON
#GOGOSTART
11210212,
02104040,
11210212,
12104040, //e130

10221021,
02021020,
33034044,
03302020, //e134

33,
3033,
#GOGOEND
0,
0, //e138

#GOGOSTART
3,
#BARLINEOFF
0,
#GOGOEND
0,
0, //e142

#SCROLL 0.5
0002000000000000, //e143

#M
0,
#BARLINEOFF
0,
0,
0002, //m90

#BARLINEON
#MEASURE 1/8
#SCROLL 0.75
2,
#BARLINEOFF
0,
#BARLINEON
2,
#MEASURE 1/4
2,
#MEASURE 1/8
2,
#BARLINEOFF
0,
#BARLINEON
#MEASURE 1/8
2,

#BARLINEOFF
#MEASURE 1/4
0,
#BARLINEON
2,
#GOGOSTART
4,
4,
#GOGOEND

#MEASURE 1/8
2,
#BARLINEOFF
0,
#BARLINEON
2,
#MEASURE 1/4
2,
#MEASURE 1/8
2,
#BARLINEOFF
0,
#BARLINEON
2,

#BARLINEOFF
0,
#BARLINEON
2,
#MEASURE 1/4
2,
#GOGOSTART
4,
4,
#GOGOEND

2,
#MEASURE 1/8
2,
#BARLINEOFF
0,
#BARLINEON
#MEASURE 1/4
2,
#BARLINEOFF
#MEASURE 1/8
0,
#BARLINEON
2,

#BARLINEOFF
0,
#BARLINEON
#MEASURE 1/4
2,
#MEASURE 1/8
2,
#MEASURE 2/4
2,

#MEASURE 1/8
2,
#MEASURE 1/4
2,
#MEASURE 1/8
2,
#MEASURE 1/4
2,
#GOGOSTART
4,
#GOGOEND

#BARLINEOFF
#MEASURE 4/4
#SCROLL 1
0, //m126

#BARLINEON
#GOGOSTART
11210212,
0011100040004000,
11210212,
1011100040004000, //m130

10221021,
0022202010102000,
33034044,
03302020, //m134

33,
3033,
#GOGOEND
0,
0, //m138

#GOGOSTART
3,
#BARLINEOFF
0,
#GOGOEND
0,
0, //m142

#SCROLL 0.5
0002000000000000, //m143
#END


COURSE:Normal
LEVEL:5
BALLOON:30,2,2,2
SCOREINIT:530,3580
SCOREDIFF:145

#START
#SCROLL 0.98
3,
#BPMCHANGE 201.06
#SCROLL 0.96
3,
#BPMCHANGE 188.46
#SCROLL 1.03
3,
#BPMCHANGE 194.61
#SCROLL 1
7, //4

0,
0,
0,
8,
0, //9

#BPMCHANGE 195
1110,
1110,
1110,
11101000, //13

1110,
1110,
10101105,
000000000000000000000000000000000008000000000000, //17

11
#GOGOSTART
40,
#GOGOEND
11
#GOGOSTART
44,
#GOGOEND
1120,
#GOGOSTART
4444, //21
#GOGOEND

11
#GOGOSTART
40,
#GOGOEND
11
#GOGOSTART
44,
#GOGOEND
1120,
100000000000200000000000500000000008000000000000, //25

10100100,
10100100,
10100100,
1120, //29

#BARLINEOFF
22,
2022,
#BARLINEON
2244, //32

3110,
1110,
1110,
11101000, //36

1110,
1110,
10102205,
000000000000000000000000000000000008000000000000, //40

10011000,
1120,
1120,
1110, //44

10011000,
1120,
5,
0, //48

0,
000000000000000000000000000000000000000000000008,
0,
0, //52

#BRANCHSTART p, -2, -1
#N
#GOGOSTART
10101110,
1144,
10101110,
1144, //n56

10111001,
01011000,
33033033,
000000600000000000000000000000000008000000000000, //n60

10101110,
1144,
10102220,
1144, //n64

10111002,
02202000,
33033044,
03307080, //n68

33,
3033,
#GOGOEND
0, //n71

#E
#GOGOSTART
10101110,
1144,
10101110,
1144, //e56

10111001,
01011000,
33033033,
000000600000000000000000000000000008000000000000, //e60

10101110,
1144,
10102220,
1144, //e64

10111002,
02202000,
33033044,
03307080, //e68

33,
3033,
#GOGOEND
0, //e71

#M
#GOGOSTART
10101110,
1144,
10101110,
1144, //m56

10111001,
01011000,
33033033,
000000600000000000000000000000000008000000000000, //m60

10101110,
1144,
10102220,
1144, //m64

10111002,
02202000,
33033044,
03307080, //m68

33,
3033,
#GOGOEND
0, //m71

#BRANCHSTART p, 101, 102
#N
0, //n72

500000000000000008000000100000000000100000000000,
500000000000000008000000100000000000100000000000,
10101110,
11102220, //n76

500000000000000008000000200000000000200000000000,
500000000000000008000000200000000000200000000000,
10101110,
11102220, //n80

3111,
3111,
5,
000000000000000000000000000000000000000000000008,
0,
0, //n86

#E
0, //e72

500000000000000008000000100000000000100000000000,
500000000000000008000000100000000000100000000000,
10101110,
11102220, //e76

500000000000000008000000200000000000200000000000,
500000000000000008000000200000000000200000000000,
10101110,
11102220, //e80

3111,
3111,
5,
000000000000000000000000000000000000000000000008,
0,
0, //e86

#M
0, //m72

500000000000000008000000100000000000100000000000,
500000000000000008000000100000000000100000000000,
10101110,
11102220, //m76

500000000000000008000000200000000000200000000000,
500000000000000008000000200000000000200000000000,
10101110,
11102220, //m80

3111,
3111,
5,
000000000000000000000000000000000000000000000008,
0,
0, //m86

#BRANCHSTART r, -2, -1
#N
0,
#BARLINEOFF
0,
0,
#SCROLL 0.75
0002, //n90

#BARLINEON
#MEASURE 1/8
0,
#BARLINEOFF
0,
0,
#MEASURE 1/4
0,
#MEASURE 1/8
0,
0,
#MEASURE 1/8
0,

#BARLINEON
#MEASURE 1/4
0,
#BARLINEOFF
0,
#GOGOSTART
2,
2,
#GOGOEND

#BARLINEON
#MEASURE 1/8
0,
#BARLINEOFF
0,
0,
#MEASURE 1/4
0,
#MEASURE 1/8
0,
0,
0,

#BARLINEON
0,
#BARLINEOFF
0,
#MEASURE 1/4
0,
#GOGOSTART
2,
2,
#GOGOEND

#BARLINEON
0,
#BARLINEOFF
#MEASURE 1/8
0,
0,
#MEASURE 1/4
2,
#MEASURE 1/8
0,
0,

#BARLINEON
0,
#BARLINEOFF
#MEASURE 1/4
0,
#MEASURE 1/8
0,
#MEASURE 2/4
2,

#BARLINEON
#MEASURE 1/8
2,
#BARLINEOFF
#MEASURE 1/4
0,
#MEASURE 1/8
2,
#MEASURE 1/4
0,
#GOGOSTART
4,
#GOGOEND

#BARLINEOFF
#MEASURE 4/4
#SCROLL 1
0, //n126

#BARLINEON
#GOGOSTART
10101110,
1144,
10102220,
1144, //n130

10111002,
02022000,
33033044,
03302020, //n134

33,
3033,
#GOGOEND
0,
0, //n138

#GOGOSTART
3,
0,
#GOGOEND
0,
#SCROLL 0.5
0, //n142

0002000000000000, //n143

#E
0,
#BARLINEOFF
0,
0,
#SCROLL 0.75
0002, //e90

#BARLINEON
#MEASURE 1/8
0,
#BARLINEOFF
0,
0,
#MEASURE 1/4
0,
#MEASURE 1/8
0,
0,
#MEASURE 1/8
0,

#BARLINEON
#MEASURE 1/4
0,
#BARLINEOFF
0,
#GOGOSTART
2,
2,
#GOGOEND

#BARLINEON
#MEASURE 1/8
0,
#BARLINEOFF
0,
0,
#MEASURE 1/4
0,
#MEASURE 1/8
0,
0,
0,

#BARLINEON
0,
#BARLINEOFF
0,
#MEASURE 1/4
0,
#GOGOSTART
2,
2,
#GOGOEND

#BARLINEON
0,
#BARLINEOFF
#MEASURE 1/8
0,
0,
#MEASURE 1/4
2,
#MEASURE 1/8
0,
0,

#BARLINEON
0,
#BARLINEOFF
#MEASURE 1/4
0,
#MEASURE 1/8
0,
#MEASURE 2/4
2,

#BARLINEON
#MEASURE 1/8
2,
#BARLINEOFF
#MEASURE 1/4
0,
#MEASURE 1/8
2,
#MEASURE 1/4
0,
#GOGOSTART
4,
#GOGOEND

#BARLINEOFF
#MEASURE 4/4
#SCROLL 1
0, //e126

#BARLINEON
#GOGOSTART
10101110,
1144,
10102220,
1144, //e130

10111002,
02022000,
33033044,
03302020, //e134

33,
3033,
#GOGOEND
0,
0, //e138

#GOGOSTART
3,
0,
#GOGOEND
0,
#SCROLL 0.5
0, //e142

0002000000000000, //e143

#M
0,
#BARLINEOFF
0,
0,
#SCROLL 0.75
0002, //m90

#BARLINEON
#MEASURE 1/8
0,
#BARLINEOFF
0,
0,
#MEASURE 1/4
0,
#MEASURE 1/8
0,
0,
#MEASURE 1/8
0,

#BARLINEON
#MEASURE 1/4
0,
#BARLINEOFF
0,
#GOGOSTART
2,
2,
#GOGOEND

#BARLINEON
#MEASURE 1/8
0,
#BARLINEOFF
0,
0,
#MEASURE 1/4
0,
#MEASURE 1/8
0,
0,
0,

#BARLINEON
0,
#BARLINEOFF
0,
#MEASURE 1/4
0,
#GOGOSTART
2,
2,
#GOGOEND

#BARLINEON
0,
#BARLINEOFF
#MEASURE 1/8
0,
0,
#MEASURE 1/4
2,
#MEASURE 1/8
0,
0,

#BARLINEON
0,
#BARLINEOFF
#MEASURE 1/4
0,
#MEASURE 1/8
0,
#MEASURE 2/4
2,

#BARLINEON
#MEASURE 1/8
2,
#BARLINEOFF
#MEASURE 1/4
0,
#MEASURE 1/8
2,
#MEASURE 1/4
0,
#GOGOSTART
4,
#GOGOEND

#BARLINEOFF
#MEASURE 4/4
#SCROLL 1
0, //m126

#BARLINEON
#GOGOSTART
10101110,
1144,
10102220,
1144, //m130

10111002,
02022000,
33033044,
03302020, //m134

33,
3033,
#GOGOEND
0,
0, //m138

#GOGOSTART
3,
0,
#GOGOEND
0,
#SCROLL 0.5
0, //m142

0002000000000000, //m143
#END


COURSE:Easy
LEVEL:3
BALLOON:17
SCOREINIT:480,6070
SCOREDIFF:160

#START
#SCROLL 0.98
3,
#BPMCHANGE 201.06
#SCROLL 0.96
3,
#BPMCHANGE 188.46
#SCROLL 1.03
3,
#BPMCHANGE 194.61
#SCROLL 1
7, //4

0,
0,
0,
8,
0, //9

#BPMCHANGE 195
11,
1110,
11,
1110, //13

11,
1110,
10002005,
000000000000000000000000000008000000000000000000, //17

1
#GOGOSTART
3,
#GOGOEND
10
#GOGOSTART
33,
#GOGOEND
11,
#GOGOSTART
3333, //21
#GOGOEND

1
#GOGOSTART
3,
#GOGOEND
10
#GOGOSTART
33,
#GOGOEND
11,
500000000000000000000000000008000000000000000000, //25

1,
11,
1,
12, //29

#BARLINEOFF
22,
22,
#BARLINEON
2022, //32

41,
1110,
11,
1110, //36

11,
1110,
10002005,
000000000000000000000000000008000000000000000000, //40

1,
12,
11,
1, //44

1,
12,
5,
0, //48

0,
000000000000000000000000000000000000000000000008,
0,
0, //52

#BRANCHSTART p, -2, -1
#N
#GOGOSTART
11,
1033,
22,
2033, //n56

11,
1110,
30030030,
000000600000000000000000000008000000000000000000, //n60

11,
1044,
22,
2033, //n64

11,
2220,
30030030,
03000000, //n68

600000000000000000000000000008000000000000000000,
33,
#GOGOEND
0, //n71

#E
#GOGOSTART
11,
1033,
22,
2033, //e56

11,
1110,
30030030,
000000600000000000000000000008000000000000000000, //e60

11,
1044,
22,
2033, //e64

11,
2220,
30030030,
03000000, //e68

600000000000000000000000000008000000000000000000,
33,
#GOGOEND
0, //e71

#M
#GOGOSTART
11,
1033,
22,
2033, //m56

11,
1110,
30030030,
000000600000000000000000000008000000000000000000, //m60

11,
1044,
22,
2033, //m64

11,
2220,
30030030,
03000000, //m68

600000000000000000000000000008000000000000000000,
33,
#GOGOEND
0, //m71

#BRANCHSTART p, 101, 102
#N
0, //n72

500000000000000000000000000008000000000000000000,
500000000000000000000000000008000000000000000000,
11,
1110, //n76

500000000000000000000000000008000000000000000000,
500000000000000000000000000008000000000000000000,
11,
2220, //n80

11,
11,
5,
000000000000000000000000000000000000000000000008,
0,
0, //n86

#E
0, //e72

500000000000000000000000000008000000000000000000,
500000000000000000000000000008000000000000000000,
11,
1110, //e76

500000000000000000000000000008000000000000000000,
500000000000000000000000000008000000000000000000,
11,
2220, //e80

11,
11,
5,
000000000000000000000000000000000000000000000008,
0,
0, //e86

#M
0, //m72

500000000000000000000000000008000000000000000000,
500000000000000000000000000008000000000000000000,
11,
1110, //m76

500000000000000000000000000008000000000000000000,
500000000000000000000000000008000000000000000000,
11,
2220, //m80

11,
11,
5,
000000000000000000000000000000000000000000000008,
0,
0, //m86

#BRANCHSTART r, -2, -1
#N
0,
#BARLINEOFF
0,
0,
#SCROLL 0.75
0, //n90

#BARLINEON
#MEASURE 1/8
0,
#BARLINEOFF
0,
0,
#MEASURE 1/4
0,
#MEASURE 1/8
0,
0,
#MEASURE 1/8
0,

#BARLINEON
#MEASURE 1/4
0,
#BARLINEOFF
0,
#GOGOSTART
2,
2,
#GOGOEND

#BARLINEON
#MEASURE 1/8
0,
#BARLINEOFF
0,
0,
#MEASURE 1/4
0,
#MEASURE 1/8
0,
0,
0,

#BARLINEON
0,
#BARLINEOFF
0,
#MEASURE 1/4
0,
#GOGOSTART
2,
2,
#GOGOEND

#BARLINEON
0,
#BARLINEOFF
#MEASURE 1/8
0,
0,
#MEASURE 1/4
0,
#MEASURE 1/8
0,
0,

#BARLINEON
2,
#BARLINEOFF
#MEASURE 1/4
0,
#MEASURE 1/8
0,
#MEASURE 2/4
0,

#BARLINEON
#MEASURE 1/8
2,
#BARLINEOFF
#MEASURE 1/4
0,
#MEASURE 1/8
0,
#MEASURE 1/4
0,
#GOGOSTART
4,
#GOGOEND

#BARLINEOFF
#MEASURE 4/4
#SCROLL 1
0, //n126

#BARLINEON
#GOGOSTART
11,
1044,
11,
2033, //n130

11,
2220,
30030030,
03000000, //n134

33,
3033,
#GOGOEND
0,
0, //n138

#GOGOSTART
3,
0,
#GOGOEND
0,
#SCROLL 0.5
0, //n142

000002000000000000000000, //n143

#E
0,
#BARLINEOFF
0,
0,
#SCROLL 0.75
0, //e90

#BARLINEON
#MEASURE 1/8
0,
#BARLINEOFF
0,
0,
#MEASURE 1/4
0,
#MEASURE 1/8
0,
0,
#MEASURE 1/8
0,

#BARLINEON
#MEASURE 1/4
0,
#BARLINEOFF
0,
#GOGOSTART
2,
2,
#GOGOEND

#BARLINEON
#MEASURE 1/8
0,
#BARLINEOFF
0,
0,
#MEASURE 1/4
0,
#MEASURE 1/8
0,
0,
0,

#BARLINEON
0,
#BARLINEOFF
0,
#MEASURE 1/4
0,
#GOGOSTART
2,
2,
#GOGOEND

#BARLINEON
0,
#BARLINEOFF
#MEASURE 1/8
0,
0,
#MEASURE 1/4
0,
#MEASURE 1/8
0,
0,

#BARLINEON
2,
#BARLINEOFF
#MEASURE 1/4
0,
#MEASURE 1/8
0,
#MEASURE 2/4
0,

#BARLINEON
#MEASURE 1/8
2,
#BARLINEOFF
#MEASURE 1/4
0,
#MEASURE 1/8
0,
#MEASURE 1/4
0,
#GOGOSTART
4,
#GOGOEND

#BARLINEOFF
#MEASURE 4/4
#SCROLL 1
0, //e126

#BARLINEON
#GOGOSTART
11,
1044,
11,
2033, //e130

11,
2220,
30030030,
03000000, //e134

33,
3033,
#GOGOEND
0,
0, //e138

#GOGOSTART
3,
0,
#GOGOEND
0,
#SCROLL 0.5
0, //e142

000002000000000000000000, //e143

#M
0,
#BARLINEOFF
0,
0,
#SCROLL 0.75
0, //m90

#BARLINEON
#MEASURE 1/8
0,
#BARLINEOFF
0,
0,
#MEASURE 1/4
0,
#MEASURE 1/8
0,
0,
#MEASURE 1/8
0,

#BARLINEON
#MEASURE 1/4
0,
#BARLINEOFF
0,
#GOGOSTART
2,
2,
#GOGOEND

#BARLINEON
#MEASURE 1/8
0,
#BARLINEOFF
0,
0,
#MEASURE 1/4
0,
#MEASURE 1/8
0,
0,
0,

#BARLINEON
0,
#BARLINEOFF
0,
#MEASURE 1/4
0,
#GOGOSTART
2,
2,
#GOGOEND

#BARLINEON
0,
#BARLINEOFF
#MEASURE 1/8
0,
0,
#MEASURE 1/4
0,
#MEASURE 1/8
0,
0,

#BARLINEON
2,
#BARLINEOFF
#MEASURE 1/4
0,
#MEASURE 1/8
0,
#MEASURE 2/4
0,

#BARLINEON
#MEASURE 1/8
2,
#BARLINEOFF
#MEASURE 1/4
0,
#MEASURE 1/8
0,
#MEASURE 1/4
0,
#GOGOSTART
4,
#GOGOEND

#BARLINEOFF
#MEASURE 4/4
#SCROLL 1
0, //m126

#BARLINEON
#GOGOSTART
11,
1044,
11,
2033, //m130

11,
2220,
30030030,
03000000, //m134

33,
3033,
#GOGOEND
0,
0, //m138

#GOGOSTART
3,
0,
#GOGOEND
0,
#SCROLL 0.5
0, //m142

000002000000000000000000, //m143
#END