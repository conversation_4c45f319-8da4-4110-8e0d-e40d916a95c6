//TJADB Project
TITLE:Ruma
TITLEJA:ルマ
SUBTITLE:--Rin<PERSON>/Strawberry Prince
SUBTITLEJA:莉犬 / すとぷり
BPM:154
WAVE:Ruma.ogg
OFFSET:-0.971
DEMOSTART:59.210


COURSE:Oni
LEVEL:7
BALLOON:
SCOREINIT:510,1640
SCOREDIFF:135

#START
3000300030030030,
3000300030112000,

1000100010010010,
1020102010221020,
1000100010010010,
1020102011221020, //6

1000100010010010,
1020102010221020,
1000100010010010,
1020102011221020,

1010100010010010,
0022202010002000,
1010100010020010,
0011101020002000, //14

1010100010010010,
0012102010001000,
1010100010020010,
0011101020002000,

5,
000000000000000000000000000000000008000000000000,
1000100010010010,
1020102011221020, //22

1010100010010010,
01112020,
1010100010020010,
0011101020002000,

1010100010010010,
0011202010001000,
1010100010020010,
0011101020002000, //30

3000000000000011,
2012012010111020,
1000100010222020,
1002102010101000,

3000000000000011,
2012012010111000,
3000000000000011,
1003003000000000, //38

#GOGOSTART
12121222,
1020102101101000,
1010110100222020,
1000101010210110,

12121222,
1020102101101000,
1010102201101020,
3030303030112000, //46
#GOGOEND

1000102010210010,
1020200020120120,
1000102010210010,
1020200020120120,

1001001000111010,
2002002000222000, //52

22222222,
2020202200000000,
0002,
3000000010210210,

30113444,
1020102101101020,
1010102201101020,
3030303030112000, //60

#GOGOSTART
1022102030404040,
1020102101101000,
1010110100222020,
1022101010210110,

1022102030404040,
1020102101101000,
1022202101111020,
3030303030112000,

1022201201111020,
3030303030112030,
3030303030112000, //71
#GOGOEND

1000100010010010,
1020102010221020,
1000100010010010,
1020102011221020,

1000100010010010,
1020102010221020,
1000100010010010,
1020102011221020, //79

3000300030030030,
3,
0, //82
#END


COURSE:Hard
LEVEL:4
BALLOON:
SCOREINIT:600,2710
SCOREDIFF:173

#START
3000300030030030,
3330,

1110,
10101110,
1110,
10101110, //6

1110,
10101110,
1110,
10101110,

1000100010010010,
02202020,
1000100010010010,
01112000, //14

1000100010010010,
02202020,
1000100010010010,
01112020,

5,
000000000000000000000000000000000008000000000000,
1110,
10101110, //22

1000100010010010,
02202020,
1000100010010010,
01112000,

1000100010010010,
02202020,
1000100010010010,
01112020, //30

3,
4004004000101000,
1111,
500000000000000000000000000008000000000000000000,

3,
4004004000101000,
3,
3003003000000000, //38

#GOGOSTART
10102222,
500000000000000000000000000008000000000000000000,
1010100100002000,
1000200010010000,

10102222,
500000000000000000000000000008000000000000000000,
1010100100202000,
33333000, //46
#GOGOEND

1110,
2220,
1110,
2220,

500000000000000008000000000000000000000000000000,
500000000000000008000000000000000000000000000000, //52

22,
2,
0002,
3000000010010000,

30104444,
500000000000000000000000000008000000000000000000,
1010100100202000,
33333000, //60

#GOGOSTART
10104444,
600000000000000000000000000008000000000000000000,
1010100100002000,
1000200010010000,

10104444,
600000000000000000000000000008000000000000000000,
1010100100202000,
33333000,

1010100100202000,
33333003,
33333020, //71
#GOGOEND

1110,
10101110,
1110,
10101110,

1110,
10101110,
1110,
10101110, //79

3000300030030030,
3,
0, //82
#END


COURSE:Normal
LEVEL:2
BALLOON:
SCOREINIT:720,5000
SCOREDIFF:260

#START
33,
3330,

11,
1,
11,
1110, //6

11,
1,
11,
1110,

11,
0110,
11,
0220, //14

11,
0110,
11,
0220,

5,
000000000000000000000000000000000008000000000000,
11,
1110, //22

11,
0110,
11,
0220,

11,
0110,
11,
0220, //30

3,
40040000,
11,
500000000000000000000000000008000000000000000000,

3,
40040000,
3,
30030000, //38

#GOGOSTART
1111,
500000000000000000000000000008000000000000000000,
1102,
1110,

1111,
500000000000000000000000000008000000000000000000,
1102,
3330, //46
#GOGOEND

11,
22,
11,
22,

500000000000000008000000000000000000000000000000,
500000000000000008000000000000000000000000000000, //52

2,
2,
0,
3,

3044,
500000000000000000000000000008000000000000000000,
1102,
3330, //60

#GOGOSTART
1133,
600000000000000000000000000008000000000000000000,
1102,
1110,

1133,
600000000000000000000000000008000000000000000000,
1102,
3330,

1102,
3330,
3330, //71
#GOGOEND

11,
1,
11,
1110,

11,
1,
11,
1110, //79

33,
3,
0, //82
#END


COURSE:Easy
LEVEL:1
BALLOON:
SCOREINIT:870,9030
SCOREDIFF:623

#START
3,
33,

1,
1,
1,
11, //6

1,
1,
1,
11,

1,
0,
1,
01, //14

1,
0,
1,
01,

5,
000000000000000000000000000008000000000000000000,
1,
11, //22

1,
0,
1,
01,

1,
0,
1,
01, //30

3,
4,
11,
500000000000000000000000000008000000000000000000,

3,
4,
3,
3, //38

#GOGOSTART
11,
500000000000000000000000000008000000000000000000,
1,
2,

11,
500000000000000000000000000008000000000000000000,
1,
33, //46
#GOGOEND

1,
2,
1,
2,

500000000000000008000000000000000000000000000000,
500000000000000008000000000000000000000000000000, //52

2,
2,
0,
3,

33,
500000000000000000000000000008000000000000000000,
1,
33, //60

#GOGOSTART
11,
600000000000000000000000000008000000000000000000,
1,
2,

11,
600000000000000000000000000008000000000000000000,
1,
33,

1,
33,
33, //71
#GOGOEND

1,
1,
1,
11,

1,
1,
1,
11, //79

33,
3,
0, //82
#END