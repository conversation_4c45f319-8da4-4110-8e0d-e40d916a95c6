//TJADB Project
TITLE:Make it!
TITLEJA:Make it!
SUBTITLE:--i☆Ris
SUBTITLEJA:i☆Ris
BPM:172
WAVE:Make it!.ogg
OFFSET:-1.513
DEMOSTART:52.428

//AC14 score only

COURSE:Oni
LEVEL:7
BALLOON:12
SCOREINIT:730
SCOREDIFF:180

#START
10201121,
1022201000102010,
10201121,
100000200200200000500000000000000000000008000000,

10210121,
1110200011102000,
1110201000102060,
000000000000000000000000000000000000000008000000,
22122110, //9

11201221,
01020212,
11210221,
0010002220202020,

11201221,
01020212,
11021021,
01202010, //17

1110202010102010,
01210121,
1110201000102010,
01212211,

1110202010102010,
01210121,
1110201010002010,
0022201020201010, //25

10221202,
12102202,
500000000000000000000000000000000008000000000000,
2000102000102210,

12210121,
1000201010002220,
10211120,
11201122,

1110202011102020,
70000008,
2220102220101000,
33301020, //37

#GOGOSTART
1110201000102011,
1010202011102000,
1110201000400040,
02121020,

1110201000102011,
1010200011102000,
1110201000400040,
0020102011102000, //45

100000000000200000100000500000000000000008000000,
1020002011101000,
100000000000200000100000500000000000000008000000,
1020002011101110,

1000201000102011,
10210120,
34034034,
0030400040002210, //53
#GOGOEND

10201121,
1022201000102010,
10201121,
100000200200200000500000000000000000000008000000,

10210121,
1110200011102000,
1110201000102060,
000000000000000000000000000000000000000008000000,

3,
0,
0,
0, //65
#END


COURSE:Hard
LEVEL:4
BALLOON:12
SCOREINIT:680
SCOREDIFF:180

#START
10201121,
12010121,
10201121,
100000200000000000500000000000000000000008000000,

10210121,
1212,
10210126,
000000000000000000000000000000000000000008000000,
22122110, //9

11202201,
01020202,
11220201,
01020220,

11202201,
01020202,
11022021,
01002020, //17

10201121,
01210121,
10210202,
01212210,

10201121,
01210121,
10211022,
02212210, //25

10220202,
10102202,
500000000000000000000000000000000008000000000000,
20020210,

10210120,
10211020,
10211020,
11201120,

11221122,
9009,
8,
0022, //37

#GOGOSTART
10210121,
01201020,
10110404,
02121010,

10210121,
01201020,
10110404,
02121010, //45

100000000000200000100000500000000000000000000008,
0022,
100000000000200000100000500000000000000000000008,
0022,

10210121,
00210120,
33033033,
03304000, //53
#GOGOEND

10201121,
12010121,
10201121,
100000200000000000500000000000000000000008000000,

10210121,
1212,
10210126,
000000000000000000000000000000000000000008000000,

3,
0,
0,
0, //65
#END


COURSE:Normal
LEVEL:4
BALLOON:8
SCOREINIT:950
SCOREDIFF:250

#START
1110,
11010020,
1110,
100000100000000000500000000000000000000008000000,

10110110,
12,
10020060,
000000000000000000000000000000000000000008000000,
0, //9

11000001,
01000000,
11000001,
01000000,

11000001,
01000000,
11000001,
01000000, //17

10001001,
00010100,
10010101,
0022,

10001001,
00010100,
10001011,
0022, //25

10000001,
1120,
500000000000000000000000000000000008000000000000,
20020000,

10110010,
10011000,
20022000,
0111,

1212,
9009,
8,
0022, //37

#GOGOSTART
10110101,
0111,
10110404,
0011,

10110101,
0111,
10110404,
0011, //45

100000000000000000100000500000000000000000000008,
0022,
100000000000000000100000500000000000000000000008,
0011,

10110101,
00110100,
33033033,
03303000, //53
#GOGOEND

1110,
11010020,
1110,
100000100000000000500000000000000000000008000000,

10110110,
12,
10020006,
000000000000000000000000000000000000000008000000,

3,
0,
0,
0, //65
#END


COURSE:Easy
LEVEL:2
BALLOON:5,6,10
SCOREINIT:1080
SCOREDIFF:430

#START
11,
1,
11,
1,

1,
11,
6,
000000000000000000000000000000000000000008000000,
0, //9

20000002,
0,
20000002,
0,

20000002,
0,
20000002,
0, //17

10000001,
0,
10000001,
0,

10000001,
0,
10000007,
00000800, //25

1,
12,
5,
000000000000000000000000000008000000000000000000,

1100,
11,
22,
0111,

11,
9009,
8,
0011, //37

#GOGOSTART
10000001,
0011,
10000303,
0011,

10000001,
0011,
10000404,
0011, //45

100000000000000000000000500000000000000000000008,
0011,
100000000000000000000000500000000000000000000008,
0011,

7,
00000800,
30030030,
03003000, //53
#GOGOEND

11,
1,
11,
1,

1,
11,
6,
000000000000000000000000000008000000000000000000,

3,
0,
0,
0, //65
#END