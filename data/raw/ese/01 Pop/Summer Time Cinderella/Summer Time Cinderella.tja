//TJADB Project
TITLE:Summer Time Cinderella
TITLEJA:サマータイムシンデレラ
SUBTITLE:--
SUBTITLEJA:
BPM:143
WAVE:Summer Time Cinderella.ogg
OFFSET:-2.171
DEMOSTART:61.071


COURSE:Oni
LEVEL:7
BALLOON:9,15,21,5,5,47
SCOREINIT:860,2600
SCOREDIFF:230

#START
2000002020000022,
20220222,
2000002020000022,
20220220,

21110111,
21112020,
21120120,
2010201110211010, //8

20202021,
20202121,
2222,
1001201020012010,

2222,
20202120,
20212021,
2010201020201011, //16

1000201000002022,
1000201000102022,
1000201000102022,
1002002010100010,

20250000,
000000000000000008000000000000000000200000200200,
1000201020001011,
2010201011101110,
11210120,
1020020021102000, //26

20222022,
20222022,
20222200,
2000000010010010,

1010201110211010,
21121212,
70008011,
7008,
1011101110111111,
1020102030030030, //36

#GOGOSTART
00410121,
1010202011102011,
1000201011102010,
1110201110002222,

1010201011102010,
1201201012012010,
10201122,
1020101110202020, //44

1110201110200010,
1010202011102010,
10211121,
1000202120201010, 
#GOGOEND

2010201120102010,
2010201121102110,
4010101020101122,
7, //52

8,
21120112,
7878,
#MEASURE 1/4
0,

#MEASURE 4/4
7,
0,
0800, //59
#END


COURSE:Hard
LEVEL:4
BALLOON:7,11,17,3,3,38
SCOREINIT:870,3750
SCOREDIFF:243

#START
20022002,
22,
20022002,
20220220,

20220220,
20112020,
1111,
2010001020020020, //8

500000000000000000000008000000000000100000500000,
000000000000000008000000000000100000100000100000,
500000000000000000000008000000000000100000000000,
2001001020010010,

500000000000000000000000000000000000000008000000,
0011,
20012001,
21212000, //16

10210022,
10210122,
10210120,
1002002020100010,

20250000,
000000000000000008000000000000000000000000000000,
10211022,
11201212,
100000000000000000100000500000000000000000000008,
0011, //26

20222020,
20222020,
20222200,
1000000010010010,

1000001020020020,
10001122,
70008011,
7008,
1011100010111000,
1011100030030030, //36

#GOGOSTART
00410121,
11221121,
10210121,
1010201010002220,

10201021,
1001001020020020,
10201122,
10122211, //44

10210201,
11221121,
10211121,
10022200,
#GOGOEND

11201120,
22102210,
4010101020101110,
7, //52

8,
22220220,
7878,
#MEASURE 1/4
0,

#MEASURE 4/4
9,
0000000000090000,
0800, //59
#END


COURSE:Normal
LEVEL:4
BALLOON:17,5,8,11,25
SCOREINIT:1200,6620
SCOREDIFF:463

#START
22,
2,
22,
2,

10010010,
20020000,
1111,
21, //8

500000000000000000000008000000000000100000500000,
000000000000000008000000000000100000100000000000,
500000000000000000000008000000000000100000000000,
22,

500000000000000000000000000000000000000008000000,
0011,
11,
2220, //16

10110010,
10110010,
10110010,
22,

7,
08,
10011001,
1122,
100000000000000000000000500000000000000000000008,
0011, //26

2222,
2220,
10110105,
000000000000000000000008000000000000000000000000,

12,
1022,
70008010,
7008,
500000000008000000000000500000000008000000000000,
600000000008000000000000300000000300000000300000, //36

#GOGOSTART
0412,
11101020,
1212,
11102000,

1212,
1001000010010000,
10001015,
000000000008000000000000200000000000200000000000, //44

10110201,
01102022,
00011101,
10022200,
#GOGOEND

1212,
1212,
44,
7, //52

8,
20020020,
22,
#MEASURE 1/4
0,

#MEASURE 4/4
9,
0000000000090000,
0800, //59
#END


COURSE:Easy
LEVEL:3
BALLOON:13,4,5,9,21
SCOREINIT:1240,11090
SCOREDIFF:790

#START
2,
2,
2,
2,

1,
11,
1,
2, //8

10000005,
000000000000000008000000000000000000000000000000,
1001,
500000000000000000000008000000000000000000000000,

500000000000000000000000000000000000000008000000,
01,
11,
1110, //16

10010000,
1,
10010000,
22,

7,
08,
11,
1011,
100000000000000000000000500000000000000000000008,
0, //26

22,
2,
11,
1,

12,
12,
78,
70000800,
11,
12, //36

#GOGOSTART
0301,
1110,
1110,
1120,

1110,
1111,
12,
12, //44

500000000000000000000008000000000000000000000000,
500000000000000000000008000000000000000000000000,
1011,
12,
#GOGOEND

1110,
2220,
44,
7, //52

8,
2,
22,
#MEASURE 1/4
0,

#MEASURE 4/4
9,
0000000000090000,
0800, //59
#END