//TJADB Project
TITLE:Surges
TITLEJA:Surges
SUBTITLE:--
SUBTITLEJA:
BPM:137
WAVE:Surges.ogg
OFFSET:-0.611
DEMOSTART:21.170

//Shinuchi: 8830/5160/2940/1810/1230

COURSE:Edit
LEVEL:10
BALLOON:150
SCOREINIT:470,1230
SCOREDIFF:108

#START
1011021011202022,
1011012011202011,
2011021011202022,
1011012011202012,

2021012021102021,
1021012011202021,
1021012021012021,
2021012010021122,

1002112211021122,
1102101120021122,
1002112211021121,
0212101210012220, //12

#GOGOSTART
300000202020100200100200100100000200100100200100,
2121211120021122,
100000202020100200100200100100200200100100200100,
0211212120021122,

100000202020100200100200100100000200100100200100,
2121211120021122,
100000202020100200100200100100200200100100200100,
0202101010212020, //20

3002112211021122,
1102101120021122,
1002112211021122,
1212101120012222,

1021122112211221,
2121211120011221,
1221122112211221,
1221212120012022, //28
#GOGOEND

7,
0,
0,
0,

0,
0,
0,
0000000000800011, //36

2011021011202022,
1011012011202012,
2021012021102021,
1021012011202021,
1021012021012021,
2021012010021122, //42

1002112211021122,
1102101120022020,
2222112211021122,
1212112220021121,
200000000200000000101010200000000000000000000000,

200100100200100100101010200100100100200100100100,
200100100200100100101010200100100100200100100100,
200100100200100100101010200100100100200100100100,
200100100200100100101010200000100000400000000000, //51

#GOGOSTART
300000202020100200100200100100000200100100200100,
2121211120021122,
100000202020100200100200100100200200100100200100,
0202101010212020,

3002112211021121,
0211212120022121,
2022112211021121,
1212122210112222,

1002112211021121,
0211212120011221,
1221122112211221,
0201201010212022, //63
#GOGOEND

200000000000
#GOGOSTART
100200200200102020200100200200200000,
100100202010100000200200100100202010100000202010,
1011112212121020,
100100100100202020100100100200100200101010200000,
#GOGOEND

0,
0, //69
#END


COURSE:Oni
LEVEL:8
BALLOON:
SCOREINIT:590,1860
SCOREDIFF:145

#START
1001011001102010,
1011011000002000,
1001011001102010,
1011011000002000,

1001011001102010,
1011011000002000,
1001011001012010,
1011011000002000,

1000201011012110,
1101201010002010,
1000201011012110,
1101201010002010, //12

#GOGOSTART
3022112011011210,
1122102010012210,
1022112101211020,
500000000000000000000008000000000100200200100000,

3022112011011210,
1122102010012210,
1022112101211021,
0102101110012210, //20

6,
000000000000000000000008000000000100200200100000,
1022112011011210,
1122102010012210,

6,
000000000000000000000008000000000100200200100000,
1022112011011211,
0122101010000000, //28
#GOGOEND

#SCROLL 0.75
1002002000202020,
1002002000202020,
1002002000202020,
1002002000202020,

1002002002102020,
1002002002102020,
1002002002102020,
1002002002102020, //36

#SCROLL 1
1001011001102010,
1011011002102020,
1001011001102010,
1011011002102020,
1001011001012010,
1011011000012010, //42

1001211011012110,
1101201010012010,
1110211101012111,
0102101110012010,
4,

5,
0,
0,
000000000000000000000000000008000000400000000000, //51

#GOGOSTART
3022112011011210,
1122101110012210,
1022112101211021,
0122101110012210,

6,
000000000000000000000008000000000100200200100000,
1022112011011211,
0122101110012210,

6,
000000000000000000000008000000000100200200100000,
1022112011011211,
0122101110012210, //63
#GOGOEND

100000000000
#GOGOSTART
100100100100101010100000200000200000,
100111100111100111100200,
1011112211102020,
1111222100304030,
#GOGOEND

0,
0, //69
#END


COURSE:Hard
LEVEL:6
BALLOON:26
SCOREINIT:740,3080
SCOREDIFF:195

#START
1001001000102000,
1001001000000000,
1001001000102000,
1001001000000000,

1001001000102000,
1001001000002000,
1001001000102000,
1001001000002000,

1000201011002010,
1001101010002000,
1000201011002010,
1001101010002000, //12

#GOGOSTART
3000101011011010,
1011101010002000,
1000101101101010,
500000000000000000000008000000000000200000000000,

3000101011011010,
1011101010002000,
1000101101101011,
0101101010002000, //20

6,
000000000000000000000008000000000000200000000000,
1000101011011010,
1011101010002000,

6,
000000000000000000000008000000000000200000000000,
1000101011011011,
0101101010000000, //28
#GOGOEND

#SCROLL 0.75
1002002000002000,
1002002000000000,
1002002000002000,
1002002000000000,

1002002000102000,
1002002000102000,
1002002000102000,
1002002000102000, //36

#SCROLL 1
1001001000102000,
1001001000002000,
1001001000102000,
1001001000002000,
1001001000102000,
1001001000102000, //42

1000201011002010,
1101101010002000,
1000201011002011,
0101101010002020,
4,

5,
0,
0,
000000000000000000000000000008000000400000000000, //51

#GOGOSTART
3000101011011010,
1011101010002000,
1000101101101011,
0101101010002000,

6,
000000000000000000000008000000000000200000000000,
1000101011011011,
0101101010002000,

6,
000000000000000000000008000000000000200000000000,
1000101011011011,
0101101010002000, //63
#GOGOEND

1000
#GOGOSTART
111010101000,
1011101110111010,
9,
09,
#GOGOEND

8,
0, //69
#END


COURSE:Normal
LEVEL:4
BALLOON:6,18
SCOREINIT:900,5490
SCOREDIFF:283

#START
10010010,
10010000,
10010010,
10010000,

20020020,
20020000,
20020020,
20020000,

1212,
1120,
1212,
1120, //12

#GOGOSTART
30101110,
10111000,
10101110,
500000000000000000000008000000000000000000000000,

30101110,
10111000,
10101110,
700000000000000000000000000000080000000000000000, //20

6,
000000000000000000000008000000000000000000000000,
10101110,
10111020,

6,
000000000000000000000008000000000000000000000000,
10101110,
10111000, //28
#GOGOEND

#SCROLL 0.75
10020000,
20020000,
20020000,
20020000,

10020020,
20020000,
10020020,
20020000, //36

#SCROLL 1
10010010,
10010020,
10010010,
10010020,
10010010,
10010000, //42

1212,
1120,
1212,
1120,
4,

5,
0,
0,
000000000000000000000000000008000000000000000000, //51

#GOGOSTART
30101110,
10111000,
10101110,
10111000,

6,
000000000000000000000008000000000000000000000000,
10101110,
10111020,

6,
000000000000000000000008000000000000000000000000,
10101110,
10111020, //63
#GOGOEND

10
#GOGOSTART
101110,
10101110,
9,
09,
#GOGOEND

8,
0, //69
#END


COURSE:Easy
LEVEL:3
BALLOON:5,15
SCOREINIT:960,9580
SCOREDIFF:445

#START
1,
10010000,
1,
10010000,

2,
20020000,
2,
20020000,

11,
12,
11,
12, //12

#GOGOSTART
31,
1110,
11,
500000000000000000000008000000000000000000000000,

31,
1110,
1011,
700000000000000000000000000000080000000000000000, //20

6,
000000000000000000000008000000000000000000000000,
1011,
1110,

6,
000000000000000000000008000000000000000000000000,
1011,
1110, //28
#GOGOEND

#SCROLL 0.75
1,
0,
2,
0,

1,
1,
2,
2, //36

#SCROLL 1
10010000,
10010000,
10010000,
10010000,
20020000,
20020000, //42

11,
22,
11,
22,
4,

5,
0,
0,
000000000000000000000000000008000000000000000000, //51

#GOGOSTART
31,
1110,
11,
1110,

6,
000000000000000000000008000000000000000000000000,
1011,
1110,

6,
000000000000000000000008000000000000000000000000,
1011,
1110, //63
#GOGOEND

1
#GOGOSTART
110,
1111,
9,
09,
#GOGOEND

8,
0, //69
#END