//TJADB Project
TITLE:S’il vous President
TITLEJA:シル・ヴ・プレジデント
SUBTITLE:--Pmarusama
SUBTITLEJA:P丸様。
BPM:176
WAVE:S’il vous President.ogg
OFFSET:-1.478
DEMOSTART:50.064

//Shinuchi: 7370/4510/3130/2110/1490

COURSE:Edit
LEVEL:9
BALLOON:2
SCOREINIT:520,1490
SCOREDIFF:130
 
#START
00000222,
1011101110111011,
2010201040111111,

100000202020200000100000100000200200100100200000,
1111202012102000,
100000202020200000100000100000200200100100200000,
1111201120102000,

100000202020200000100000100000200200100100200000,
1111201012102000,
100000202020200000100000100000200200100100200000,
1011202040303000,
600008000000500008000000700000800000400000400000, //12

0000222220202022,
000200200000000200200200200200200200000020000200,
000200200200200000200200000000200020002000200020,
020202000400400000000000,

2222202020220220,
000000200200000020002000200000200020002000200020,
002000000000200020002000200020002002020000000000,
200200200200200000002020000200200020002000000000, //20

3011200210112020,
1010222010222020,
1011202010112020,
1122112020101010,

3011200210112020,
1010222010222020,
1011201020002222,
1122112120102020, //28

10011020,
11011020,
10011020,
3003003000201000,

1211212212112122,
1122112211221212,
100100100100100100100100101010101010101010101010,
10103222, //36

#GOGOSTART
300000202020100000202020100000100100200000200000,
1022102012112020,
1022102011102121,
1111211121112111,

2011101120201020,
200200100000100000500008000000200200200000200000,
100000200200100000100000200200100000100000500008,
0022102210202020, //44

1022102210102020,
101010100000101010100000400000000000500008000000,
1022102210102020,
101010100000101010100000400000100000500008000000,

1011202210102020,
1122121000202020,
1011101110111011,
2010201040111110,
3000202010222020,
34343040, //54
#GOGOEND

100000202020200000100000100000200200100100200000,
1111202012102000,
100000202020200000100000100000200200100100200000,
1111201120102000,

100000202020200000100000100000200200100100200000,
1111201012102000,
100000202020200000100000100000200200100100200000,
1011202040303000, //62

2011201120112011,
2011202210021150,
0,
000000000000000008000000000000000000000000000000,
0, //67
#END


COURSE:Oni
LEVEL:7
BALLOON:20,10,24
SCOREINIT:680,2270
SCOREDIFF:178
 
#START
00000222,
500000000000000000000000000008000000200000000000,
11221020,

1010201120102010,
1011102010102000,
1010201120102010,
2022201020102000,

1010201120102010,
1011102010102000,
1010201120102010,
2022201020102000,
3332, //12

1000201120002020,
1000202220001000,
1000201120002020,
1000202220001000,

1000201120002020,
1000202220001000,
1000201120002020,
1000202220101010, //20

1000200020112020,
1000100010211050,
000000000000000000000008000000100000200000200000,
0010202220101010,

1000200020112020,
1000100020211020,
500000000000000000000000000000000008000000000000,
1120112010202010, //28

10122021,
10122021,
21112011,
500000000000000000000008000000100000200000000000,

1011101120102000,
700000000000000000000000000000000000000000000000,
00080120,
30403222, //36

#GOGOSTART
3000300030001011,
2010102020112010,
1022102020101011,
1011101120112010,

1000201110102011,
2010102000222000,
1011201110102070,
00008222, //44

1000201022201020,
1011102010222000,
1000102011102020,
1011102010222000,

1000101110102020,
1020201000101120,
500000000000000000000000000008000000200000000000,
1010102210002000,
1000202010111020,
34343040, //54
#GOGOEND

1010201120102010,
1011102010102000,
1010201120102010,
2022201020102000,

1010201120102010,
1011102010102000,
1010201120102010,
2022201020102000, //62

4433,
4000402220001070,
0,
000000000000000000080000000000000000000000000000,
0, //67
#END


COURSE:Hard
LEVEL:5
BALLOON:9,14
SCOREINIT:760,3380
SCOREDIFF:218

#START
0,
500000000000000000000000000008000000000000000000,
11101020,

11210120,
11121120,
11210120,
22212220,

11210120,
11121120,
11210120,
22212220,
3332, //12

10012002,
10022010,
10012002,
10022010,

10012002,
10022010,
10012002,
10022111, //20

10100122,
10101215,
000000000000000000000008000000000000200000000000,
01210111,

10100122,
10102112,
500000000000000000000000000000000008000000000000,
11101220, //28

10022000,
10022000,
21121011,
500000000000000000000008000000000000000000000000,

11112220,
9009,
8,
4440, //36

#GOGOSTART
30303012,
02120121,
02110011,
0112,

10011002,
11110020,
11210115,
000000000000000000000008000000000000200000000000, //44

10221020,
22101020,
10112010,
11202010,

20101012,
11020112,
500000000000000000000000000008000000200000000000,
11121020,
10221110,
3334, //54
#GOGOEND

11210120,
11121120,
11210120,
22212220,

11210120,
11121120,
11210120,
22212220, //62

4444,
40402007,
0,
000000000000000000080000000000000000000000000000,
0, //67
#END


COURSE:Normal
LEVEL:4
BALLOON:7,12
SCOREINIT:790,5020
SCOREDIFF:265

#START
0,
500000000000000000000000000008000000000000000000,
1112,

10110010,
10101110,
10110010,
10102220,

10110010,
10101110,
10110010,
10102220,
3330, //12

10011000,
10022000,
10011000,
10022000,

10011000,
10022000,
10011000,
10022000, //20

1102,
10101005,
000000000000000000000008000000000000000000000000,
0,

1102,
1122,
500000000000000000000000000000000008000000000000,
0, //28

11,
12,
11,
500000000000000000000008000000000000000000000000,

10101110,
9009,
8,
4440, //36

#GOGOSTART
30303002,
02020111,
01110011,
0111,

10011000,
20220000,
10010005,
000000000000000000000008000000000000000000000000, //44

10011010,
11101000,
10022020,
1110,

2111,
11010020,
500000000000000000000000000008000000000000000000,
11101020,
10101110,
3334, //54
#GOGOEND

10110010,
10101110,
10110010,
10102220,

10110010,
10101110,
10110010,
10102220, //62

4444,
40400007,
0,
000000000000000000080000000000000000000000000000,
0, //67
#END


COURSE:Easy
LEVEL:3
BALLOON:5,10
SCOREINIT:800,8440
SCOREDIFF:385

#START
0,
500000000000000000000000000008000000000000000000,
1110,

10010000,
1110,
10010000,
2220,

10010000,
1110,
10010000,
2220,
3330, //12

11,
12,
11,
12,

11,
12,
11,
12, //20

1100,
10001005,
000000000000000000000008000000000000000000000000,
0,

1100,
12,
500000000000000000000000000000000008000000000000,
0, //28

1,
1,
2,
500000000000000000000008000000000000000000000000,

1110,
9009,
8,
4440, //36

#GOGOSTART
30303001,
00010002,
00020001,
0110,

11,
2,
10000005,
000000000000000000000008000000000000000000000000, //44

11,
1110,
12,
1110,

0110,
10010000,
500000000000000000000000000008000000000000000000,
1110,
11,
3330, //54
#GOGOEND

10010000,
1110,
10010000,
2220,

10010000,
1110,
10010000,
2220, //62

44,
40000007,
0,
000000000000000000080000000000000000000000000000,
0, //67
#END