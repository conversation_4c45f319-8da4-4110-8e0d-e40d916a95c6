//TJADB Project
TITLE:<PERSON><PERSON><PERSON>
TITLEJA:イガク
SUBTITLE:--<PERSON><PERSON> feat. <PERSON><PERSON><PERSON>
SUBTITLEJA:原口沙輔 feat. 重音テト
BPM:170
WAVE:Igaku.ogg
OFFSET:-1.234
DEMOSTART:47.806

//shinuchi: 5050/2620/1900/1370/1160
//Note: Ura chart stanzas do not align with the other 4 charts.

COURSE:Edit
LEVEL:10
BALLOON:2,2
SCOREINIT:420,1310
SCOREDIFF:108

#START
#MEASURE 1/16
#SCROLL 4
0,
#SCROLL 3.2
0,
#SCROLL 2.67
0,
#SCROLL 2.29
0,
#SCROLL 2
0,
#SCROLL 1.78
0,
#SCROLL 1.6
0,
#SCROLL 1.45
0,
#SCROLL 1.33
0,
#SCROLL 1.23
0,
#SCROLL 1.14
0,
#MEASURE 5/16
#SCROLL 1.07
0
#SCROLL 1
1000, //12

#MEASURE 4/4
30002200200010001010200010002200,
20001000101020001000110020002020,
10002200200010001010200010002200, //15

#MEASURE 33/64
200000001000000010001000200000001,
#MEASURE 1/16
#SCROLL 4
000
#SCROLL 1
1,
#SCROLL 4
000
#SCROLL 1
1,
#SCROLL 4
000
#SCROLL 1
1,
#SCROLL 4
000
#SCROLL 1
2,
#SCROLL 4
000
#SCROLL 1
2,
#SCROLL 4
000
#SCROLL 1
2,
#SCROLL 4
000
#SCROLL 1
2,
#MEASURE 3/64
#SCROLL 4
0, //24

#MEASURE 4/4
#SCROLL 1
10002200200010001010200010002200,
20001000101020001000110020000000,
#MEASURE 8/4
#SCROLL 2
300000400000000300000400000300000400300400
#SCROLL 8
680000, //27

#MEASURE 4/4
#SCROLL 1
30200121,
000200100111200000200100,
10200121, //30

#MEASURE 33/64
000000001000000020000000100000001,
#MEASURE 1/16
#SCROLL 4
000
#SCROLL 1
1,
#SCROLL 4
000
#SCROLL 1
1,
#SCROLL 4
000
#SCROLL 1
1,
#SCROLL 4
000
#SCROLL 1
2,
#SCROLL 4
000
#SCROLL 1
2,
#SCROLL 4
000
#SCROLL 1
2,
#SCROLL 4
000
#SCROLL 1
2,
#MEASURE 3/64
#SCROLL 4
0, //39

#MEASURE 4/4
#SCROLL 1
10201121,
000000200000100000101010200000000000200200100000,
#MEASURE 8/4
#SCROLL 2
300040000030004000300040
#SCROLL 1
10202022, //42

#MEASURE 4/4
1010202010102011,
100200200111200100200100,
1010202010102011, //45

#MEASURE 33/64
100000002000000020000000100000001,
#MEASURE 1/16
#SCROLL 4
000
#SCROLL 1
1,
#SCROLL 4
000
#SCROLL 1
1,
#SCROLL 4
000
#SCROLL 1
1,
#SCROLL 4
000
#SCROLL 1
2,
#SCROLL 4
000
#SCROLL 1
2,
#SCROLL 4
000
#SCROLL 1
2,
#SCROLL 4
000
#SCROLL 1
2,
#MEASURE 3/64
#SCROLL 4
0, //54

#MEASURE 4/4
#SCROLL 1
1010202010102011,
100000200000200000101010200000100000200200100000,
#MEASURE 8/4
#SCROLL 2
300000400000000300000400000300000400300400
#SCROLL 8
680000, //57

#MEASURE 4/4
#SCROLL 1
10000000000022000000000022000010,
10002020002022000000000022000000,
10000000000022000000000022000010,
10002000000022001000000022002000, //61

#SCROLL 2
333330
#SCROLL 1
22,
#SCROLL 2
333330
#SCROLL 1
22,
#SCROLL 2
33
#SCROLL 1
22
#SCROLL 2
33
#SCROLL 1
22,
#SCROLL 2
300000
#SCROLL 1
200000
#SCROLL 2
300000
#SCROLL 1
200000
100100100100
#SCROLL 8
608000000000, //65

#SCROLL 1
#GOGOSTART
3011201210112210,
200000100100200000100000200100201010200000100100,
1011201210112210, //68

#MEASURE 33/64
200000000000000000000000100000001000000010000000200000000000000000000000200000000000000000000000100,
#MEASURE 1/16
#SCROLL 4
000
#SCROLL 1
1,
#SCROLL 4
000
#SCROLL 1
1,
#SCROLL 4
000
#SCROLL 1
2,
#MEASURE 19/64
#SCROLL 4
000000000
#SCROLL 8
600000008000000000000000000000000000000000000000, //73

#MEASURE 4/4
#SCROLL 1
1011221020221120,
2011201021112022,
#MEASURE 8/4
#SCROLL 2
3030303040003030303040004000
#SCROLL 1
2110, //76

#MEASURE 4/4
3011201210112210,
200000100100200000100000200100201010200000100100,
1011201210112210, //79

#MEASURE 33/64
200000000000000000000000100000001000000010000000200000000000000000000000200000000000000000000000100,
#MEASURE 1/16
#SCROLL 4
000
#SCROLL 1
1,
#SCROLL 4
000
#SCROLL 1
1,
#SCROLL 4
000
#SCROLL 1
2,
#MEASURE 19/64
#SCROLL 4
000000000
#SCROLL 8
600000008000000000000000000000000000000000000000, //84

#MEASURE 4/4
#SCROLL 1
1011221020221120,
2011201021112022,
#MEASURE 8/4
#SCROLL 2
3030303040003030303040004000
#SCROLL 1
2220, //87
#GOGOEND

#MEASURE 4/4
30002200200010001010200010002200,
20001000101020001000110020002020,
10002200200010001010200010002200,
#MEASURE 33/64
200000001000000010001000200000001,
#MEASURE 1/16
#SCROLL 4
000
#SCROLL 1
1,
#SCROLL 4
000
#SCROLL 1
1,
#SCROLL 4
000
#SCROLL 1
1,
#SCROLL 4
000
#SCROLL 1
2,
#SCROLL 4
000
#SCROLL 1
2,
#SCROLL 4
000
#SCROLL 1
2,
#SCROLL 4
000
#SCROLL 1
2,
#MEASURE 3/64
#SCROLL 4
0, //99

#MEASURE 4/4
#SCROLL 1
10002200200010001010200010002200,
20001000101020001000110020000000,
#MEASURE 8/4
200
#SCROLL 0.9
200
#SCROLL 0.81
100
#SCROLL 0.9
200
#SCROLL 0.81
200
#SCROLL 0.73
100
#SCROLL 0.81
200
#SCROLL 0.73
200
#SCROLL 0.66
100
#SCROLL 0.73
200
#SCROLL 0.66
200
#SCROLL 0.59
100
#SCROLL 0.66
200
#SCROLL 0.59
200
#SCROLL 8
680000, //102

#MEASURE 4/4
#SCROLL 1
30200200,
01001000,
20100100,
0020202221112222, //106

1121221121221122,
1122112211212211,
2010221111201122,
100200100000000000100000101010101010100000000000,
200000000000000000000000000000500008000000000000, //111

#MEASURE 8/4
#SCROLL 2
3000300040000000400030004000400030003000202020201100100070008000,
#MEASURE 4/4
#SCROLL 1
#GOGOSTART
3011201210112210, //113

#MEASURE 33/64
200000000000000000000000100000001000000010000000200000000000000000000000200000000000000000000000100,
#MEASURE 1/16
#SCROLL 4
000
#SCROLL 1
1,
#SCROLL 4
000
#SCROLL 1
1,
#SCROLL 4
000
#SCROLL 1
2,
#MEASURE 19/64
#SCROLL 4
000000000
#SCROLL 8
600000008000000000000000000000000000000000000000, //118

#MEASURE 4/4
#SCROLL 1
1011221020221120,
2011201021112022,
#MEASURE 8/4
#SCROLL 2
3030303040003030303040004000
#SCROLL 1
2110, //121

#MEASURE 4/4
3011221212112210,
200200100100200000100100200100201010200000100100,
1011221212112210, //124

#MEASURE 33/64
200000000000200000000000100000001000000010000000200000000000000000000000200000000000200000000000100,
#MEASURE 1/16
#SCROLL 4
000
#SCROLL 1
1,
#SCROLL 4
000
#SCROLL 1
1,
#SCROLL 4
000
#SCROLL 1
2,
#MEASURE 19/64
#SCROLL 4
000000000
#SCROLL 8
600000008000000000000000000000000000000000000000, //129

#MEASURE 4/4
#SCROLL 1
1011221121221121,
200200100100200000100000201010100100200000200200,
#MEASURE 8/4
#SCROLL 2
1212121040001212121040003000
#SCROLL 1
2220, //132
#GOGOEND

#MEASURE 1/4
#BPMCHANGE 42.5
#SCROLL 4
7,
0,
#MEASURE 4/4
8, //135
#END


COURSE:Oni
LEVEL:8
BALLOON:23,2,2
SCOREINIT:440,1580
SCOREDIFF:110

#START
#MEASURE 1/16
#SCROLL 4
0,
#SCROLL 3.2
0,
#SCROLL 2.67
0,
#SCROLL 2.29
0,
#SCROLL 2
0,
#SCROLL 1.78
0,
#SCROLL 1.6
0,
#SCROLL 1.45
0,
#SCROLL 1.33
0,
#SCROLL 1.23
0,
#SCROLL 1.14
0,
#MEASURE 5/16
#SCROLL 1.07
0
#SCROLL 1
3000, //12

#MEASURE 4/4
1021201010201021,
2010102010212000,
1021201010201021,
2010102012121212, //16

1021201010201021,
2010102010212000,
30400304,
000300000400300400680000, //20

10200121,
0020001120001010,
10200121,
0020001012121212, //24

10201121,
0020101120001020,
30400304,
0030004000102012, //28

10201121,
0010201120001011,
10201121,
0010201012121212, //32

10201121,
0010201120001020,
30400304,
000300000400300400680000, //36

1010002010002012,
1011102010002000,
1010002010002012,
1011102010002000, //40

33334000,
33334000,
43004300,
400000400000400000680000, //44

#GOGOSTART
1021201210212012,
1021201210202000,
1021201210212012,
100000200100200000100200100000200000608000000000, //48

1111201022221020,
1110111020002220,
33334033,
3030400030001110, //52

1021201210212012,
1021201210202000,
1021201210212012,
100000200100200000100200100000200000608000000000, //56

1111201022221020,
1110111020002220,
33334033,
33403022, //60
#GOGOEND

1021201010201021,
2010102010212000,
1021201010201021,
2010102012121212, //64

1021201010201021,
2010102010212000, 
7,
000000000000800000680000, //68

10100100,
02002000,
10100101,
0020202220212120, //72

1020102120102120,
1210212010201020,
2000201110200021,
100000200000000000500000000000000008000000000000,
4, //77

34304433,
4040111110107080,
#GOGOSTART
1021201210212012,
100000200100200000100200100000200000608000000000, //81

1111201022221020,
1110111020002220,
33334033,
3030400030001110, //85

1021212012121020,
1021201210212120,
1021212012121020,
100000200100200000100200100000200000608000000000, //89

1212101021212020,
1210121020002222,
1212121040001212,
1210400030002020, //93
#GOGOEND

#MEASURE 1/4
#BPMCHANGE 42.5
#SCROLL 4
7,
0,
#MEASURE 4/4
8, //96
#END


COURSE:Hard
LEVEL:6
BALLOON:20,7,2
SCOREINIT:470,2420
SCOREDIFF:123

#START
#MEASURE 1/16
#SCROLL 4
0,
#SCROLL 3.2
0,
#SCROLL 2.67
0,
#SCROLL 2.29
0,
#SCROLL 2
0,
#SCROLL 1.78
0,
#SCROLL 1.6
0,
#SCROLL 1.45
0,
#SCROLL 1.33
0,
#SCROLL 1.23
0,
#SCROLL 1.14
0,
#MEASURE 5/16
#SCROLL 1.07
0
#SCROLL 1
3000, //12

#MEASURE 4/4
12211012,
21102220,
12211012,
21102000,

12211012,
21102220,
30300303,
03033340, //20

10101011,
02,
10101011,
0000000011101110,

10101111,
02,
30300303,
03030222, //28

10201021,
02102000,
10201021,
0020100011101110,

10201021,
02102000,
30300303,
03033340, //36

11011020,
11112000,
11011020,
11112020,

33333000,
33333000,
33003300,
3334, //44

#GOGOSTART
1011100010102020,
10201220,
1011100010102020,
1214,

1011100010101010,
2222,
33333033,
33304000, //52

1011100010102020,
10201220,
1011100010102020,
1214,

1011100010101010,
2222,
33333033,
33304000, //60
#GOGOEND

12211012,
21102220,
12211012,
21102000,

12211012,
21102220,
7,
0084, //68

10001001,
02,
10001007,
08,

1010100011101000,
1110111010101000,
20200202,
000000200000000000500000000000000008000000000000, 
0, //77

33304444,
3340,
#GOGOSTART
1011100010102020,
1214,

1011100010101010,
2222,
33333033,
33304000, //85

11221122,
10221220,
11221122,
10221040,

1011100010101010,
2222,
33333033,
33304000, //93
#GOGOEND

#MEASURE 1/4
#BPMCHANGE 42.5
#SCROLL 4
7,
0,
#MEASURE 4/4
8, //96
#END


COURSE:Normal
LEVEL:5
BALLOON:12,5,2
SCOREINIT:490,3560
SCOREDIFF:140

#START
#MEASURE 1/16
#SCROLL 4
0,
#SCROLL 3.2
0,
#SCROLL 2.67
0,
#SCROLL 2.29
0,
#SCROLL 2
0,
#SCROLL 1.78
0,
#SCROLL 1.6
0,
#SCROLL 1.45
0,
#SCROLL 1.33
0,
#SCROLL 1.23
0,
#SCROLL 1.14
0,
#MEASURE 5/16
#SCROLL 1.07
0
#SCROLL 1
3000, //12

#MEASURE 4/4
1110,
1120,
1110,
2,

1110,
1120,
30300303,
03030040, //20

10101011,
0,
10101011,
0,

10101011,
0,
30300303,
03030000, //28

10102011,
0120,
10102011,
0120,

10102011,
0120,
30300303,
03030040, //36

11,
2220,
11,
2220,

33333000,
33333000,
33,
3334, //44

#GOGOSTART
10101110,
1110,
10101110,
1114,

10101110,
2220,
33333033,
33304000, //52

10101110,
1110,
10101110,
1114,

10101110,
2220,
33333033,
33304000,  //60
#GOGOEND

1110,
1120,
1110,
2,

1110,
1120,
7,
0084, //68

10001001,
0,
10001007,
08,

10101110,
11101010,
20200202,
000000200000000000500000000000000008000000000000, 
0, //77

30003330,
3330,
#GOGOSTART
10101110,
1114,

10101110,
2220,
33333033,
33304000, //85

10111110,
1110,
20222220,
2224,

10101110,
2220,
33333033,
33304000, //93
#GOGOEND

#MEASURE 1/4
#BPMCHANGE 42.5
#SCROLL 4
7,
0,
#MEASURE 4/4
8, //96
#END


COURSE:Easy
LEVEL:3
BALLOON:10,4,2
SCOREINIT:560,6130
SCOREDIFF:180

#START
#MEASURE 1/16
#SCROLL 4
0,
#SCROLL 3.2
0,
#SCROLL 2.67
0,
#SCROLL 2.29
0,
#SCROLL 2
0,
#SCROLL 1.78
0,
#SCROLL 1.6
0,
#SCROLL 1.45
0,
#SCROLL 1.33
0,
#SCROLL 1.23
0,
#SCROLL 1.14
0,
#MEASURE 5/16
#SCROLL 1.07
0
#SCROLL 1
0000, //12

#MEASURE 4/4
1110,
1,
1110,
2,

1110,
1,
30300303,
03030000, //20

1011,
0,
1011,
0,

1111,
0,
30300303,
03030000, //28

1111,
02,
1111,
02,

1111,
02,
30300303,
03030000, //36

1,
11,
1,
11,

600000000000000000000008000000000000000000000000,
600000000000000000000008000000000000000000000000,
33,
3330, //44

#GOGOSTART
1011,
1110,
1011,
1110,

1110,
2220,
600000000000000000000008000000000000600000000000,
000000000008000000000000000000000000000000000000, //52

1011,
1110,
1011,
1110,

1110,
2220,
600000000000000000000008000000000000600000000000,
000000000008000000000000000000000000000000000000, //60
#GOGOEND

1110,
1,
1110,
2,

1110,
1,
7,
08, //68

11,
0,
10001007,
08,

1111,
1110,
500000000000000008000000000000500000000000000008,
000000500000000000000000000000000008000000000000,
0, //77

33,
3330,
#GOGOSTART
1011,
1110,

1110,
2220,
600000000000000000000008000000000000600000000000,
000000000008000000000000000000000000000000000000, //85

1111,
1110,
2222,
2220,

1110,
2220,
600000000000000000000008000000000000600000000000,
000000000008000000000000000000000000000000000000, //93
#GOGOEND

#MEASURE 1/4
#BPMCHANGE 42.5
#SCROLL 4
7,
0,
#MEASURE 4/4
8, //96
#END