//TJADB Project
TITLE:Oni no Utage
TITLEJA:鬼ノ宴
SUBTITLE:--<PERSON><PERSON><PERSON>
SUBTITLEJA:友成空
BPM:130
WAVE:Oni no Utage.ogg
OFFSET:-1.224
DEMOSTART:37.670

COURSE:Oni
LEVEL:8
BALLOON:
SCOREINIT:710,2270
SCOREDIFF:193

#START
10211121,
00211121,
10211121,
0002, //4

1000201010222010,
01211022,
10211021,
01211020,

1000201010222010,
01211022,
1000201010222010,
02121020, //12

1010201011102010,
0010102210102000,
1010201011102010,
0010102210404040,

1010201011102010,
0010102210102000,
1010201011102030,
02121020, //20

#GOGOSTART
100000202020100000200000100000200100100000100100,
1020101020121022,
100000200000100000200200100000202020100000100100,
1020111010201000,

100000202020100000200000100000200100100000100100,
1020101020121022,
100000200000100000200200100000202020100000100100,
12112010, //28

600000000000000000000000000008000000300000600000,
000008000000300000600000000008000000300000300000,
0011101011001010,
0011101011001020,

600000000000000000000000000008000000300000600000,
000008000000300000600000000008000000300000300000,
0011101011001020,
1100102010002000, //36
#GOGOEND

10221121,
02212140,
10221121,
02121444,

#GOGOSTART
100000202020100000200000100000200100100000100100,
1020101020121022,
1020102210222011,
1020101020001022,
1020102210222030,
02121040, //46

100000200100100000200000100000101010100000100100,
1022102210001000,
1021102010212011,
1022102210102020,

1110201021201011,
1010212010001022,
1020102210001030,
04343020,
#GOGOEND
0, //55
#END


COURSE:Hard
LEVEL:5
BALLOON:
SCOREINIT:810,3650
SCOREDIFF:245

#START
20002202,
02,
20002202,
0, //4

10201011,
01102000,
10201021,
01102000,

10201011,
01102000,
10201021,
02121000, //12

10211011,
01101020,
10211011,
01101020,

10211011,
01101020,
10211013,
02121000, //20

#GOGOSTART
1000102010111010,
12112012,
10121012,
12112000,

1000102010111010,
12112012,
10121021,
12111020, //28

6,
000000000000000000000000000000000000000008000000,
0010101011001010,
0010101011001000,

6,
000000000000000000000000000000000000000008000000,
0010101011001010,
1100101010000000, //36
#GOGOEND

20002002,
02202000,
20002002,
02121000,

#GOGOSTART
1000102010111010,
12112012,
10121021,
12112012,
10121013,
02121000, //46

1011101010001010,
02121000,
1011101010001010,
02121120,

11211011,
0010111010002010,
10211033,
04343000,
#GOGOEND
0, //55
#END


COURSE:Normal
LEVEL:3
BALLOON:7,7,7,7,7
SCOREINIT:910,5950
SCOREDIFF:330

#START
20000002,
0,
20000002,
0, //4

10001001,
01,
10001007,
00000800,

10001001,
01,
10001007,
00000800, //12

10001001,
0110,
10001001,
0110,

10001001,
0110,
10001007,
00000800, //20

#GOGOSTART
10101011,
1110,
1212,
10111000,

10101011,
1110,
1212,
10111000, //28

6,
000000000000000000000000000000000000000008000000,
03303030,
03303030,

6,
000000000000000000000000000000000000000008000000,
03303030,
30333000, //36
#GOGOEND

20000002,
0,
20002007,
00000800,

#GOGOSTART
10101011,
1110,
1212,
10111000,
10201027,
00000800, //46

10101011,
01101000,
10101011,
01101010,

10101011,
01102000,
10201033,
03303000,
#GOGOEND
0, //55
#END


COURSE:Easy
LEVEL:2
BALLOON:6,6,6,6,6
SCOREINIT:960,9770
SCOREDIFF:548

#START
20000002,
0,
20000002,
0, //4

10000001,
0,
10001007,
00000800,

10000001,
0,
10001007,
00000800, //12

10000001,
01,
10000001,
01,

10000001,
01,
10000007,
00000800, //20

#GOGOSTART
1110,
1110,
11,
1110,

1110,
1110,
11,
1110, //28

6,
000000000000000000000000000000000000000008000000,
0333,
0333,

6,
000000000000000000000000000000000000000008000000,
0333,
3330, //36
#GOGOEND

20000002,
0,
20002007,
00000800,

#GOGOSTART
1110,
1110,
11,
1110,
11,
2, //46

10001001,
0,
10001001,
0,

10001001,
0,
10001007,
00000800,
#GOGOEND
0, //55
#END