//TJADB Project
TITLE:4+1 no Sorezore no Mirai
TITLEJA:4+1のそれぞれの未来
SUBTITLE:--cos<PERSON><PERSON>@bousouP feat. Hatsune Miku
SUBTITLEJA:太鼓 de タイムトラベル00's / cosMo＠暴走P feat.初音ミク
BPM:255
WAVE:4+1 no Sorezore no Mirai.ogg
OFFSET:-1.163
DEMOSTART:40.225

//shinuchi: 3170/2310/1520/1000/-

COURSE:Edit
LEVEL:10
BALLOON:4,4,5,5,100,4,100,2,4,100
SCOREINIT:990
SCOREDIFF:0

#START
11,
100000000000100000000000700000000000080000000000, //2

10001021,
0000102220002000,
10102021,
2000102220002022, //6

1000102210102011,
2000101120202022,
2000102220001022,
2223, //10

1011200010111020,
1011200010201212,
1011200010111020,
1011200010102212, //14

1011201010222010,
1011201010201212,
11,
100000000000100000000000700000000000080000000000, //18

#GOGOSTART
100000101010100200101010,
101010100200101010100000,
101010100200101010100200,
111100111222, //22

101010202020101010100200,
111112222122,
101010101020202020200300,
0, //26
#GOGOEND

1,
0,
1,
0, //30

1,
1,
111,
4, //34

1111,
10101400,
12101210,
12101140, //38

1000112000112000,
1120001111104000,
2111211111112040,
0022, //42

1011200010112010,
1011200010102201,
1011201020112010,
2011200022011101, //46

1011200010112010,
1011200010112030,
0011101110101040,
000000100100100100100000101010100000100000000000, //50

0000200010112010,
1011200010102201,
1234,
3444, //54

1111211111112010,
1111211121112010,
111100200100111100200100,
111100111100400000000000, //58

22,
#BARLINEOFF
22,
2220,
22, //62

2222,
22,
2022,
2220, //66

#BARLINEON
33,
33,
33,
33, //70

3232,
3232,
3232,
3232, //74

30114011,
31104011,
30114011,
31104110, //78

31114011,
3011101040101110,
3010101040111010,
3011101040101110, //82

1111111111111111,
111100111100111100111100,
3000000000002220,
12102012, //86

500000000008000000600000000008000000700000000000,
000000080000400000000000100000200000300000000000,
1111111111111110,
111111111100400000000000, //90

1011200010111020,
1011200010201212,
1011200010111020,
100000100100200020002000100100000100200200200200, //94

101010100000200000100000000000100100200200100100,
100100200100222100222100,
100100100000200000100100100000202020200000202020,
222222102100, //98

100000201010101010200100,
0000221120100020,
100020001000201010200300,
000000000000101010100000100200100000202020200000, //102

111100000100200000100200,
101010200000100000000000200200200000100000200000,
101010200000200000100100200000100000200000202020,
2000222211112222, //106

0000100020121011,
2012102012102211,
2143,
4444, //110

#SECTION
1111201112201000,
1122201222201000, //112

#BRANCHSTART p,102,102
#N
500800000000000000000000000000000000000000000000,
0
#GOGOSTART
2, //n114
#E
500800000000000000000000000000000000000000000000,
0
#GOGOSTART
2, //e114
#M
500800000000000000000000000000000000000000000000,
0
#GOGOSTART
2, //m114

#BRANCHSTART r,1,2
#N
1120112011221020,
11201120112210
#GOGOEND
00,
#GOGOSTART
1011202010102010,
00221022102210
#GOGOEND
00, //n118

#GOGOSTART
3022102010221020,
10221022112210
#GOGOEND
00,
#GOGOSTART
1022102211012020,
10221012110120
#GOGOEND
00, //n122

#GOGOSTART
3011101010111010,
10111010111110
#GOGOEND
00,
#GOGOSTART
100202100200100202100200,
100202100202100202100
#GOGOEND
000, //n126

#GOGOSTART
0000300010111010,
00101202201010
#GOGOEND
00,
#GOGOSTART
0000000
#GOGOEND
0,
#GOGOSTART
22111022111040
#GOGOEND
00, //n130

#GOGOSTART
100000100000100000200
#GOGOEND
020,
#GOGOSTART
100020200010100000200
#GOGOEND
000,
#GOGOSTART
7008022
#GOGOEND
0,
#GOGOSTART
3030302
#GOGOEND
0, //n134

#GOGOSTART
1011020
#GOGOEND
1,
#GOGOSTART
10221022102210
#GOGOEND
00,
#GOGOSTART
1212121212121212,
1212121212121000, //n138

7,
0,
0,
0, //n142

0,
0,
000000000000000000000000000000000000080000300000,
0, //n146

0,
0,
0,
0,
#GOGOEND
0, //n151

#E
1020102000120101,
10101020111110
#GOGOEND
00,
#GOGOSTART
1011201101112011,
02111011021110
#GOGOEND
00, //e118

#GOGOSTART
1110101020202020,
11101010202020
#GOGOEND
00,
#GOGOSTART
3020302030112010,
30104010401110
#GOGOEND
00, //e122

#GOGOSTART
1112021110201220,
11120211202210
#GOGOEND
00,
#GOGOSTART
1110210202102210,
12102102021010
#GOGOEND
00, //e126

#GOGOSTART
1011201020112010,
11201122102220
#GOGOEND
00,
#GOGOSTART
0000000
#GOGOEND
0,
#GOGOSTART
4040404
#GOGOEND
0, //e130

#GOGOSTART
10201110202010
#GOGOEND
20,
#GOGOSTART
30003000101220
#GOGOEND
11,
#GOGOSTART
202010100010002020102
#GOGOEND
020,
#GOGOSTART
10102020102220
#GOGOEND
00, //e134

#GOGOSTART
100020100000101010202
#GOGOEND
020,
#GOGOSTART
10111020110110
#GOGOEND
20,
#GOGOSTART
0010102010221020,
100000100100100000200000700000000000080000000000, //e138

7,
0,
0,
0, //e142

0,
0,
000000000000000000000000000000000000080000300000,
0, //e146

0,
0,
0,
0,
#GOGOEND
0, //e151

#M
1000102010201202,
10001020101012
#GOGOEND
02,
#GOGOSTART
1010201020102101,
20102010222211
#GOGOEND
11, //m118

#GOGOSTART
1000102010201202,
10002010201022
#GOGOEND
01,
#GOGOSTART
10101111,
20221000102010
#GOGOEND
20, //m122

#GOGOSTART
1220102010201222,
100000000200100000200000100000200000202020
#GOGOEND
200000,
#GOGOSTART
100100100000200000100100100000202020100000202020,
10221110201022
#GOGOEND
11, //m126

#GOGOSTART
100000100200100111100100,
21102110212120
#GOGOEND
00,
#GOGOSTART
0000000
#GOGOEND
0,
#GOGOSTART
211100211100211200700
#GOGOEND
800, //m130

#GOGOSTART
1020112
#GOGOEND
1,
#GOGOSTART
0120112
#GOGOEND
0,
#GOGOSTART
101010100010001010100
#GOGOEND
010,
#GOGOSTART
102010101010202020202
#GOGOEND
020, //m134

#GOGOSTART
101010202020101010202
#GOGOEND
120,
#GOGOSTART
101010202020101210202
#GOGOEND
020,
#GOGOSTART
11,
100000000000100000000000700000000000080000000000, //m138

7,
0,
0,
0, //m142

0,
0,
000000000000000000000000000000000000080000300000,
0, //m146

0,
0,
0,
0,
#GOGOEND
0, //m151

#BRANCHEND
#END


COURSE:Oni
LEVEL:10
BALLOON:4,4,10,5,4,100
SCOREINIT:390,1130
SCOREDIFF:90

#START
11,
100000000000100000000000700000000000
#LYRIC Sukina ongaku o nagashinagara
080000000000, //2

30001021,
0122,
10102021,
201200
#LYRIC Tsumugi de sou jibun dake no monogatari o
22, //6

10121121,
2000101020202022,
2000102220001022,
2224, //10

1000200010112010,
0010200010102210,
1000200010112010,
0010200010102222, //14

1000200010112010,
0010200010102210,
11,
100000000000100000000000700000000000080000000000, //18

#GOGOSTART
#LYRIC Taiko no oto wa eien towa no toki o koete ima demo
100111100100,
200200111222,
#LYRIC Arata ni hajimaru inochi e to maki
100111100222,
100100222111, //22

#LYRIC Tsugarete yuku to sugaradeatta melody o tomoshibi toshite
100000100000101010100200,
100200100000101010100200,
#LYRIC Ayumu hibi o tataeyou
100200100000101010100700,
000000000000000000000000000000080000
#LYRIC Ronri no wa hito sorezore de
000000000000, //26
#GOGOEND

1,
0,
1,
0
#LYRIC Soshite kyou mo jamadattakedo
000, //30

1,
1,
111,
400
#LYRIC Taisetsu datte omoeru kana
0, //34

1111,
10101400,
12101210,
1210
#LYRIC Fumishimete mae e susume!
1140, //38

1000112000112000,
1120001111101000,
1011201111111040,
00
#LYRIC Kimi wa ima doko ni mukattemasu ka
22, //42

1000200010112010,
0010200010102210,
1000201000112010,
00102000
#LYRIC Tadori chakui to sono basho wa donna keshikidesu ka
22101000, //46

1000201110002011,
1000100010221030,
0011101110101040,
00111010
#LYRIC Kono saki no tabiji mo dou ka suteki na dekigoto ga
11101000, //50

0000200010112010,
0010200010102210,
1234,
3444, //54

111020111010
#LYRIC Takusan arimasu you ni negatteimasu
2010,
1110201120102010,
1110201111102011,
1111111040000000, //58

#LYRIC
22,
#BARLINEOFF
22,
2220,
22, //62

2222,
22,
2022,
2220, //66

#BARLINEON
#LYRIC Kantan na mirai o te o hatakinagara aruitei kou
33,
33,
33,
33, //70

#LYRIC Futsuu na mirai o utainagara aruitei kou
3232,
3232,
3232,
3232, //74

#LYRIC Muzukashii mirai o sasaeainagara aruitei kou
30114011,
31104011,
30114011,
31104110, //78

#LYRIC Oni no youna mirai mo hagemashiainagara aruitei kou
31114011,
3011101040101110,
3010101040111010,
3011101040101110, //82

#LYRIC Ura fumen no youna mirai de sura mo
1110111011101110,
1111111011111110,
3000000000002220,
1210201
#LYRIC Deatta nakama to tomoni norikoeteikeru!
2, //86

500000000008000000600000000008000000700000000000,
000000080000400000000000100000200000300000000000,
1110111011111011,
1111111040000000, //90

#LYRIC
1000200010112010,
1010200010102211,
1000200010112010,
1010200022102222, //94

1110201000112011,
1010201022102210,
1110102210221022,
00
#LYRIC Kimi wa ima doko ni tattemasu ka
22, //98

10002001,
0000111110201020,
10110114,
0000
#LYRIC Tabidachi no hi ni mita yume kotae wa demashita ka
111022202220, //102

10012012,
12102212,
1010201120102040,
00002220
#LYRIC Kono tabi no hate ni dou ka taisetsuna mono o
11101110, //106

0000100020111010,
2011102010102111,
2143,
4444, //110

111110111110
#LYRIC Mitsukeraremasu you ni negatteimasu
2000,
1111201111201000,
1111221122112210,
3
#GOGOSTART
0, //114

#LYRIC Don don katsu ni tsumugu mirai o
1000102010201222,
10001020122210
#GOGOEND
00,
#GOGOSTART
20
#LYRIC Soko ni nani ga chitteyoutomo
10201020102111,
20102010211120
#GOGOEND
00, //118

#GOGOSTART
1000
#LYRIC Tsukutteikou saikou no monogatari o sou matome
102010201222,
10002010201021
#GOGOEND
11,
#GOGOSTART
20101111,
20111000102010
#GOGOEND
20, //122

#GOGOSTART
#LYRIC Don katsu katsu ni tsumugu mirai o
1110102010201222,
10010010201011
#GOGOEND
10,
#GOGOSTART
11
#LYRIC Soko ni nani ga chitteyoutomo
10201110221022,
10222010211120
#GOGOEND
00, //126

#GOGOSTART
1000
#LYRIC Tanoshindeikou kanaerareta yume no subete o
102010111020,
11201120112120
#GOGOEND
00,
#GOGOSTART
0000000
#GOGOEND
0,
#GOGOSTART
11112222121040
#GOGOEND
00, //130

#GOGOSTART
#LYRIC Yakume o sasae tsumuida rekishi no saki no bokura ga
1020112
#GOGOEND
1,
#GOGOSTART
0120112
#GOGOEND
0,
#GOGOSTART
#LYRIC Hibikaseru ima taiko full combo e tsunagaru
202020100010202020100
#GOGOEND
010,
#GOGOSTART
200010101010202020200
#GOGOEND
000, //134

#GOGOSTART
#LYRIC Mudana koto wa hitotsu monai
101010202020101010202
#GOGOEND
020,
#GOGOSTART
#LYRIC Shippai de mo seikou de mo
101020201010202010201
#GOGOEND
020,
#GOGOSTART
11,
100000000000100000000000700000000000080000000000, //138

#LYRIC
7,
0,
0,
0, //142

0,
0,
000000000000000000000000000000000000080000300000,
0, //146

0,
0,
0,
0, //150
#GOGOEND

0, //151
#END


COURSE:Hard
LEVEL:8
BALLOON:4,4,8,4,11,4,60
SCOREINIT:460,1770
SCOREDIFF:105

#START
11,
700000000000000000000000000000000000080000000000, //2

30001011,
0122,
1111,
10010022,

10020020,
10002222,
20022002,
2220, //10

10201121,
01201110,
10201121,
01202220,

10201121,
01201110,
11,
700000000000000000000000000000000000080000000000, //18

#GOGOSTART
10222000,
1120,
10222020,
12,

11100020,
11100020,
11100027,
000000000000000000000000000000080000000000000000, //26
#GOGOEND

1,
0,
1,
0,

1,
1,
111,
4, //34

1111,
10101400,
12101210,
12101110,

10120120,
12012120,
11111114,
0, //42

10201121,
01201110,
10210121,
01201110,

10211021,
10101123,
01111114,
0011, //50

10201121,
01201110,
1234,
3444,

11211120,
11212120,
11211121,
11104000, //58

0,
#BARLINEOFF
0,
0,
0,

0,
0,
0,
0, //66

#BARLINEON
33,
33,
33,
33,

3232,
3232,
3232,
3232, //74

30114011,
31104011,
30114011,
31104110,

30114011,
31104011,
30114011,
31104110, //82

11101110,
1010100011101000,
3,
2220,

500000000008000000600000000008000000700000000000,
000000080000400000000000100000200000300000000000,
7,
0840, //90

10201121,
01201110,
10201121,
01201110,

21120202,
11212120,
21121212,
0, //98

10001001,
00001110,
20120120,
0222,

10012002,
10102202,
11212124,
0011, //106

20102110,
21121120,
2143,
4444,

11110020,
11110020,
11111110,
3
#GOGOSTART
0, //114

10121210,
1012111
#GOGOEND
0,
#GOGOSTART
20212120,
2021222
#GOGOEND
0,

#GOGOSTART
10121210,
2021212
#GOGOEND
0,
#GOGOSTART
10101111,
2220121
#GOGOEND
0, //122

#GOGOSTART
11121210,
10010010102010
#GOGOEND
00,
#GOGOSTART
22212120,
2121222
#GOGOEND
0,

#GOGOSTART
10112112,
1121222
#GOGOEND
0,
#GOGOSTART
0000000
#GOGOEND
0,
#GOGOSTART
11101010000040
#GOGOEND
00, //130

#GOGOSTART
1022200
#GOGOEND
0,
#GOGOSTART
1010200
#GOGOEND
0,
#GOGOSTART
1010202
#GOGOEND
0,
#GOGOSTART
1010202
#GOGOEND
0,

#GOGOSTART
100000100000101010100
#GOGOEND
000,
#GOGOSTART
200000200000202020200
#GOGOEND
000,
#GOGOSTART
11,
700000000000000000000000000000000000080000000000, //138

9,
0,
0,
0,

0,
000000000000090000000000000000000000000000000000,
0,
080000000000000000000000000000000000000000000000, //146

0,
0,
0,
0,
#GOGOEND

0, //151
#END


COURSE:Normal
LEVEL:7
BALLOON:4,7,4,5,8,4,40
SCOREINIT:540,2800
SCOREDIFF:130

#START
17,
0008, //2

10001001,
02,
1110,
10010000,

7,
0800,
22,
2220, //10

10201001,
0210,
10201001,
0210,

10201001,
0210,
17,
0008, //18

#GOGOSTART
12,
1120,
12,
1,

1102,
1102,
10100027,
000000000000000000000000000000080000000000000000, //26
#GOGOEND

1,
0,
1,
0,

1,
1,
111,
4, //34

1111,
1110,
1111,
1110,

20020020,
02002020,
20000004,
0, //42

10001001,
0210,
10010020,
0210,

11,
20201003,
00202004,
0, //50

10001001,
0210,
1234,
3444,

1102,
1102,
1101,
14, //58

0,
#BARLINEOFF
0,
0,
0,

0,
0,
0,
0, //66

#BARLINEON
33,
33,
33,
33,

3232,
3232,
3232,
3232, //74

3141,
3141,
3141,
3141,

3141,
3141,
3141,
3141, //82

1111,
11101110,
3,
22,

500000000008000000500000000008000000600000000000,
000000000000000000000000000000000008000000000000,
7,
0840, //90

10201001,
0210,
10201001,
0210,

10020200,
10020200,
10020202,
0, //98

10001001,
01,
20020020,
0220,

12,
1120,
10101004,
0, //106

1121,
2121,
2143,
4444,

11100020,
11100020,
11101110,
3
#GOGOSTART
0, //114

10101110,
1010100
#GOGOEND
0,
#GOGOSTART
20202220,
2020200
#GOGOEND
0,

#GOGOSTART
10101110,
2020200
#GOGOEND
0,
#GOGOSTART
10101110,
2220200
#GOGOEND
0, //122

#GOGOSTART
10101110,
2020222
#GOGOEND
0,
#GOGOSTART
10101110,
2020222
#GOGOEND
0,

#GOGOSTART
10101110,
2020222
#GOGOEND
0,
#GOGOSTART
0000000
#GOGOEND
0,
#GOGOSTART
1111004
#GOGOEND
0, //130

#GOGOSTART
1000200
#GOGOEND
0,
#GOGOSTART
1010200
#GOGOEND
0,
#GOGOSTART
1000200
#GOGOEND
0,
#GOGOSTART
1010202
#GOGOEND
0,

#GOGOSTART
100000000000101010100
#GOGOEND
000,
#GOGOSTART
100000000000101010100
#GOGOEND
000,
#GOGOSTART
17,
0008, //138

9,
0,
0,
0,

0,
000000000000090000000000000000000000000000000000,
0,
080000000000000000000000000000000000000000000000, //146

0,
0,
0,
0,
#GOGOEND

0, //151
#END


COURSE:Easy
LEVEL:5
BALLOON:4,5,4,4,6,4,30
SCOREINIT:410,4040
SCOREDIFF:93

//NS1 before bug fix: 4* Score 370/88

#START
7,
0008, //2

10001001,
0,
11,
10010000,

7,
0800,
22,
2220, //10

10001001,
0,
10001001,
0,

10001001,
0,
7,
0008, //18

#GOGOSTART
11,
1110,
11,
2,

1100,
1100,
10100007,
000000000000000000000000000000080000000000000000, //26
#GOGOEND

1,
0,
1,
0,

1,
1,
111,
1, //34

11,
11,
1011,
0,

20020000,
0022,
20000004,
0, //42

10001001,
0,
20020020,
0,

11,
10101003,
00202004,
0, //50

10001001,
01,
33,
4444,

1100,
1100,
1101,
14, //58

0,
#BARLINEOFF
0,
0,
0,

0,
0,
0,
0, //66

#BARLINEON
33,
33,
33,
33,

33,
33,
33,
33, //74

34,
34,
34,
34,

34,
34,
34,
34, //82

1111,
1110,
3,
2,

500000000008000000500000000008000000600000000000,
000000000000000000000000000000000008000000000000,
7,
0840, //90

10001001,
0,
10001001,
0,

10010000,
10010000,
10020202,
0, //98

10001001,
0,
20020020,
0,

11,
1110,
20202004,
0, //106

10001001,
01,
34,
4444,

1102,
1102,
1101,
3
#GOGOSTART
0, //114

1111,
1010100
#GOGOEND
0,
#GOGOSTART
2222,
2020200
#GOGOEND
0, //118

#GOGOSTART
1110,
2020200
#GOGOEND
0,
#GOGOSTART
1110,
2000000
#GOGOEND
0, //122

#GOGOSTART
1111,
2020200
#GOGOEND
0,
#GOGOSTART
1111,
2020200
#GOGOEND
0,

#GOGOSTART
1122,
1010202
#GOGOEND
0,
#GOGOSTART
0000000
#GOGOEND
0,
#GOGOSTART
1010004
#GOGOEND
0, //130

#GOGOSTART
1000100
#GOGOEND
0,
#GOGOSTART
1010100
#GOGOEND
0,
#GOGOSTART
1000100
#GOGOEND
0,
#GOGOSTART
2020200
#GOGOEND
0,

#GOGOSTART
1000102
#GOGOEND
0,
#GOGOSTART
1000102
#GOGOEND
0,
#GOGOSTART
7,
0008, //138

9,
0,
0,
0,

0,
000000000000090000000000000000000000000000000000,
0,
080000000000000000000000000000000000000000000000, //146

0,
0,
0,
0,
#GOGOEND

0, //151
#END