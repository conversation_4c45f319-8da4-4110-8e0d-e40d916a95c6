//TJADB Project
TITLE:In<PERSON><PERSON> ni Kirawarete iru.
TITLEJA:命に嫌われている。
SUBTITLE:--<PERSON><PERSON> feat. Hatsune Miku
SUBTITLEJA:カンザキイオリ
BPM:100
WAVE:Inochi ni Kirawarete iru..ogg
OFFSET:-2.522
DEMOSTART:59.522

COURSE:Edit
LEVEL:8
SCOREINIT:660
SCOREDIFF:183


#START


1002001010020010,
10000122,
1000001020010020,
#MEASURE 5/4
100000
#BPMCHANGE 200
1120, //4
#MEASURE 4/4
10121020,
12121020,
10121020,
12121020, //8
10121020,
10121020,
10121020,
1222, //12
10121022,
12121020,
10121022,
12121020, //16
10121022,
12121020,
10121020,
1222, //20
10121122,
12121020,
10121122,
1020102010002022, //24
10121122,
12121020,
10121020,
1222, //28
1011101010102000,
11212121,
1011101010102000,
1010201020102022, //32
1011101010102000,
11212121,
500000000008000000100000200000100100200000100000,
500000000008000000100000200000100100200000100100, //36
12121112,
1020102010102022,
12121112,
1020102010221011, //40
12121112,
1020102010102022,
12121112,
1222122212221111, //44
#GOGOSTART
1000202210102011,
2010200010102210,
1010202210102011,
1010201020112210, //48
1000202210102011,
2010200010102210,
1010202210102011,
1010201012221222, //52
1000102210201120,
1022102010201120,
1020102210201120,
1022102010201122, //56
1010201110102011,
1010201110102011,
1010201110102010,
30414141, //60
#GOGOEND
30121020,
12121020,
10121020,
12121020, //64
10121020,
10121020,
10121020,
1222,


#END


COURSE:Oni
LEVEL:7
SCOREINIT:820
SCOREDIFF:233


#START


1002001010020010,
10000112,
1000001020010020,
#MEASURE 5/4
100000
#BPMCHANGE 200
1120, //4
#MEASURE 4/4
10010010,
01001120,
10010010,
01002210, //8
10010020,
10010020,
10010010,
1122, //12
11221022,
11200000,
11211021,
11200020, //16
11221120,
11200022,
10210120,
0022, //20
11221022,
11200110,
11211021,
11200020, //24
11221120,
11200020,
10210120,
, //28
12021120,
11112220,
11121120,
11212110, //32
11221122,
11211025,
000000000000000000000000000000000008000000500000,
000000000000000000000000000000000008000000000000, //36
12021120,
11112220,
11121120,
11212110, //40
11221122,
10221120,
11211022,
1110200010002020, //44
#GOGOSTART
11221122,
1110200000001010,
22112211,
2220100010002020, //48
11221122,
1110200000001020,
21122112,
2220100011102220, //52
11221122,
1110200000001010,
22112211,
2220100000001020, //56
21122112,
1110200010102020,
1110202011102020,
3344, //60
#GOGOEND
10120210,
12021120,
10120210,
12012210, //64
10120120,
10120120,
10010010,
1122, //68


#END


COURSE:2
LEVEL:5
SCOREINIT:850
SCOREDIFF:263

#START
11,
1,
11,
#MEASURE 5/4
100
#BPMCHANGE 200
12, //4
#MEASURE 4/4
10010010,
01001020,
10010010,
01001020, //8
10010020,
10010020,
10010010,
1122, //12
11101010,
11100000,
11101010,
11100020, //16
11101010,
11100020,
10110110,
, //20
11101020,
11200000,
11101020,
11200020, //24
11101020,
11200020,
10220110,
, //28
11011020,
1112,
11011020,
1212, //32
11011020,
10102025,
000000000000000000000000000000000008000000500000,
000000000000000000000000000000000008000000000000, //36
11011120,
1122,
11011120,
1212, //40
11011120,
1122,
11111022,
1212, //44
#GOGOSTART
11221020,
11100020,
11221020,
22201010, //48
11221020,
11100020,
11221120,
11201020, //52
11221020,
11200020,
11221020,
22100020, //56
11221020,
11201020,
11221120,
3344, //60
#GOGOEND
10010010,
01001020,
10010010,
01001020, //64
10010020,
10010020,
10010010,
1122,
#END


COURSE:1
LEVEL:3
BALLOON:13
SCOREINIT:1239
SCOREDIFF:588

#START
11,
1,
11,
#MEASURE 5/4
100
#BPMCHANGE 200
00, //4
#MEASURE 4/4
1,
,
1,
, //8
2,
2,
1,
, //12
11,
1100,
11,
1100, //16
22,
2205,
000000000000000000000000000000000000000000000008,
, //20
11,
1100,
11,
1100, //24
22,
2205,
000000000000000000000000000000000000000000000008,
, //28
11,
1110,
11,
1110, //32
11,
10101007,
,
0008, //36
11,
1120,
11,
1120, //40
11,
1120,
11,
3, //44
#GOGOSTART
1111,
1100,
1111,
2200, //48
1111,
1105,
0,
000000000000000008000000000000000000000000000000, //52
1111,
1100,
1111,
2200, //56
1111,
1100,
3333,
4400, //60
#GOGOEND
1,
,
1,
, //64
1,
1,
1,
2222, //68
#END


COURSE:0
LEVEL:2
BALLOON:10
SCOREINIT:1160
SCOREDIFF:436

#START
11,
1,
11,
#MEASURE 5/4
100
#BPMCHANGE 200
00,
#MEASURE 4/4
1,
,
1,
, //8
1,
1,
1,
0, //12
1,
1,
1,
1, //16
1,
1005,
000000000000000000000000000000000000000000000008,
0, //20
11,
1,
11,
1, //24
11,
1005,
000000000000000000000000000000000000000000000008,
, //28
1,
11,
1,
12, //32
1,
10001007,
,
0080, //36
1,
11,
1,
12, //40
1,
11,
11,
3, //44
#GOGOSTART
11,
1100,
11,
1100, //48
22,
1105,
,
000000000000000080000000000000000000000000000000, //52
11,
1100,
11,
1100, //56
22,
1100,
33,
3300, //60
#GOGOEND
1,
,
1,
, //64
1,
1,
1,
, //68
#END