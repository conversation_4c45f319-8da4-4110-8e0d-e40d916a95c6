//TJADB Project
TITLE:Violence Trigger
TITLEJA:バイオレンストリガー
SUBTITLE:--<PERSON><PERSON><PERSON><PERSON><PERSON> feat. Hatsune Miku/#C.O.M.P.A.S.S.
SUBTITLEJA:八王子P feat.初音ミク 「戦闘摂理解析システム#コンパス」より
BPM:196
WAVE:Violence Trigger.ogg
OFFSET:-1.325
DEMOSTART:49.080

//Shinuchi: 12770/9270/4740/1750

COURSE:Oni
LEVEL:9
BALLOON:14,28
SCOREINIT:610,1910
SCOREDIFF:155

#START
0,
0,
0,

1111111111111111,
1111111111111111,
1111111111111120,
000222200200120100400000, //7

#GOGOSTART
10410140,
1010401000104011,
10410140,
11410140,

10410140,
11410140,
1110401212104010,
#GOGOEND
0004, //15

1020102210200020,
1022102010212020,
1020102210200020,
1022102010121020,

1000111110200020,
1022102010212020,
1020102210200010,
0020211020201212, //23

1010201210112010,
1011201010212020,
1010201210112010,
1011201010121020,

1010201210112010,
1011201010212020,
1010201210112010,
2011201220112212, //31

1,
2,
1021,
0,

1020102210221020,
1020102210221020,
33044033,
000222200200120100400000, //39

#GOGOSTART
1022102110112011,
1010201210102010,
1120122010121050,
000000000008000000600000000000000008000000000000,

1022102110112011,
1010201212102010,
2011201220121050,
000000000000000000000008000000000000200200200200, //47
#GOGOEND

1210121021202120,
1022201011200010,
1210121021202120,
2011102000202020, //51

#GOGOSTART
1022102110112011,
1010201212102010,
2011201220121050,
000000000000000000000000000000000008000000100200,

1022102110112011,
1010201122102010,
2011201220111040,
#GOGOEND
00202227,
00000008,
2223, //61

#GOGOSTART
10410140,
1010401000104011,
10410140,
100000100000400000100000500000000000000008000000,

7,
00000008,
1110401212104030,
000300000300000120100400,

0,
0,
0,
0,
#GOGOEND
0, //74
#END


COURSE:Hard
LEVEL:6
BALLOON:35,22
SCOREINIT:1190,5080
SCOREDIFF:395

#START
0,
0,
0,

7,
0,
00000008,
0, //7

#GOGOSTART
10100200,
22020200,
10100200,
22020200,

10100200,
22020200,
500000000000000000000000000000000000000008000000,
#GOGOEND
0004, //15

1110,
1112,
1110,
1112,

1110,
1112,
10101002,
0, //23

1212,
10201022,
1212,
10201022,

1212,
10201022,
1212,
1002, //31

1,
2,
1021,
0,

3111,
3111,
600000000000000000000000000000000000000008000000,
0, //39

#GOGOSTART
10101121,
01211100,
11201125,
000000000008000000500000000000000008000000000000,

10101121,
01211100,
11201125,
000000000000000000000008000000000000000000000000, //47
#GOGOEND

11201120,
10210200,
11201120,
10210020, //51

#GOGOSTART
10101121,
01211100,
11201125,
000000000000000000000000000000000008000000000000,

10101121,
01211100,
600000000000000000000000000000000000000008000000,
#GOGOEND
00000007,
0,
0008, //61

#GOGOSTART
10100200,
22020200,
10100200,
22020200,

6,
000000000000000000000000000000000000000000000008,
00000004,
04040403,

0,
0,
0,
0,
#GOGOEND
0, //74
#END


COURSE:Normal
LEVEL:4
BALLOON:21,7,13
SCOREINIT:1600,10860
SCOREDIFF:1025

#START
0,
0,
0,

7,
0,
00000008,
0, //7

#GOGOSTART
1100,
2,
1100,
2,

1100,
2,
500000000000000000000000000000000000000008000000,
#GOGOEND
0, //15

11,
1,
11,
1,

11,
1,
500000000000000000000000000000000000000008000000,
0, //23

1110,
1,
1110,
1,

1110,
1,
7,
8, //31

2,
2,
2,
0,

3111,
3111,
600000000000000000000000000000000000000008000000,
0, //39

#GOGOSTART
10101001,
02,
10101005,
000000000000000000000000000000000008000000000000,

10101001,
02,
10101005,
000000000000000000000008000000000000000000000000, //47
#GOGOEND

1110,
2200,
1110,
2200, //51

#GOGOSTART
10101001,
02,
10101005,
000000000000000000000000000000000008000000000000,

30303003,
04,
600000000000000000000000000000000000000008000000, //58
#GOGOEND
00000007,
0,
0008, //61

#GOGOSTART
1100,
2,
1100,
2,

6,
000000000000000000000000000000000000000000000008,
00000003,
03030303,

0,
0,
0,
0,
#GOGOEND
0, //74
#END


COURSE:Easy
LEVEL:3
BALLOON:17,6,10
SCOREINIT:1340,15240
SCOREDIFF:978

#START
0,
0,
0,

7,
0,
00000008,
0, //7

#GOGOSTART
1100,
1,
1100,
1,

1100,
1,
500000000000000000000000000000000000000008000000,
#GOGOEND
0, //15

1,
1,
1,
0,

1,
1,
500000000000000000000000000000000000000008000000,
0, //23

11,
1,
11,
1,

11,
1,
7,
8, //31

2,
0,
2,
0,

3,
3,
600000000000000000000000000000000000000008000000,
0, //39

#GOGOSTART
1110,
0,
10101005,
000000000000000000000008000000000000000000000000,

1110,
0,
10101005,
000000000000000000000008000000000000000000000000, //47
#GOGOEND

11,
1,
11,
1, //51

#GOGOSTART
1110,
0,
10101005,
000000000000000000000000000008000000000000000000,

3330,
0,
600000000000000000000000000000000000000008000000,
#GOGOEND
00000007,
0,
00000800, //61

#GOGOSTART
1100,
1,
1100,
1,

6,
000000000000000000000000000000000000000000000008,
00000003,
03030303,

0,
0,
0,
0,
#GOGOEND
0, //74
#END