//TJADB Project
TITLE:Crush (Prod. SHOW)
TITLEJA:Crush (Prod. SHOW)
SUBTITLE:--Shin-shinjuku GR School Den'onbu / Den'onbu
SUBTITLEJA:真新宿GR学園 「電音部」より
BPM:130
WAVE:Crush (Prod. SHOW).ogg
OFFSET:-2.071
DEMOSTART:57.670

//Shinuchi: 7030/4580/2780/1890

COURSE:Oni
LEVEL:7
BALLOON:
SCOREINIT:570,2000
SCOREDIFF:153

#START
0,
0,
0,

3,
3,
3,
3, //7

1022202020222020,
1022202020222020,
1022202020222020,
1022202020222020,

1002202022202020,
1002202022202020,
2220222022202220,
1022102210222210, //15

1020222022102020,
1020222022102020,
1020222220221022,
1020102020222020,

1020122020221020,
1020122020221020,
1020102022222020,
1022102010002000, //23

1000101000101210,
1000101000101210,
1000101000201210,
1000101000201210,

1000111000101210,
1000111000101210,
1000111000201210,
0020202030030030, //31

#GOGOSTART
3002102210112010,
1002102210202020,
1002102210112010,
1020202030030030,

3002102210112010,
1002102210202020,
1002102210112010,
1020202030030030, //39

3020111120220222,
1020111020220222,
1111222020220222,
1020102010212000,

1012101210121000,
1022102210221000,
1022202220222000,
1020102010021020, //47
#GOGOEND

1000000020001022,
2010100020222010,
1000002220001022,
200100100000202020202020,

1,
1,
1,
1,

12,
12,
12,
0020202030030030, //59

#GOGOSTART
3022102210112010,
1022102210202020,
1022102210112010,
1020202030030030,

3022102210112210,
1022102210202020,
1022102210112210,
1020202030030030,
#GOGOEND

3,
0, //69
#END


COURSE:Hard
LEVEL:5
BALLOON:
SCOREINIT:630,3020
SCOREDIFF:183

#START
0,
0,
0,

3,
3,
3,
3, //7

22222022,
20222000,
22222022,
20222000,

2202,
20222202,
22222222,
10202022, //15

1000222020002020,
1000222020002020,
1000222020002020,
10222022,

1020002020222020,
1020002020222020,
1020002020222020,
10222020, //23

1000101000101110,
1000101000101110,
100000000000500000000000000000000008000000000000,
0,

1000101000101110,
1000101000101110,
100000000000500000000000000000000008000000000000,
0020202030030030, //31

#GOGOSTART
3000101110002000,
10121222,
1000101110002000,
1020202030030030,

3000101110002000,
10121222,
1000101110002000,
1020202030030030, //39

3000222020020020,
1000222020020020,
1000222020020020,
12120000,

1020102010222000,
1020102010222000,
1020102010222000,
1020102010021020, //47
#GOGOEND

1021,
01102001,
1021,
01102000,

1,
1,
1,
1,

12,
12,
12,
0020202030030030, //59

#GOGOSTART
3000101110002000,
10121222,
1000101110002000,
1020202030030030,

3000101110002000,
10121222,
1000101110002000,
1020202030030030,
#GOGOEND
3,
0, //69
#END


COURSE:Normal
LEVEL:4
BALLOON:
SCOREINIT:760,5260
SCOREDIFF:275

#START
0,
0,
0,

3,
3,
3,
3, //7

2,
22,
2,
22,

2202,
22,
2202,
2, //15

22,
2,
22,
2,

2002,
2002,
2022,
2022, //23

10010010,
10010010,
100000000000500000000000000000000008000000000000,
0,

10010010,
10010010,
100000000000500000000000000000000008000000000000,
0010101030030030, //31

#GOGOSTART
30111020,
00200111,
00111020,
0010101030030030,

30111020,
00200111,
00111020,
0010101030030030, //39

3000000020020020,
1000000020020020,
1000000020020020,
1,

10102220,
10102220,
10102220,
10102020, //47
#GOGOEND

1021,
0120,
1021,
0120,

1,
1,
1,
1,

12,
12,
12,
0010101030030030, //59

#GOGOSTART
30111020,
00200111,
00111020,
0010101030030030,

30111020,
00200111,
00111020,
0010101030030030,
#GOGOEND
3,
0, //69
#END


COURSE:Easy
LEVEL:3
BALLOON:
SCOREINIT:710,8770
SCOREDIFF:385

#START
0,
0,
0,

3,
3,
3,
3, //7

2,
2,
2,
2,

2002,
2,
2,
0, //15

2,
2,
2,
2,

2002,
2002,
22,
22, //23

1,
1,
100000000000500000000000000000000008000000000000,
0,

1,
1,
100000000000500000000000000000000008000000000000,
0000000030030030, //31

#GOGOSTART
3002,
00000111,
0002,
0000000030030030,

3002,
00000111,
0002,
0000000030030030, //39

3000000020020020,
0000000020020020,
0000000020020020,
0,

10002220,
10002220,
10002220,
1022, //47
#GOGOEND

12,
02,
12,
02,

1,
1,
1,
1,

12,
12,
12,
0000000030030030, //59

#GOGOSTART
3002,
00000111,
0002,
0000000030030030,

3002,
00000111,
0002,
0000000030030030,
#GOGOEND
3,
0, //69
#END