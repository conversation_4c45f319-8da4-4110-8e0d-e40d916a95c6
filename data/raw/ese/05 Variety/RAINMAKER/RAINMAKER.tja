//TJADB Project
TITLE:RAIN<PERSON>KER
TITLEJA:RAINMAKER
SUBTITLE:--<PERSON><PERSON><PERSON> -<PERSON><PERSON><PERSON><PERSON>’s Theme-
SUBTITLEJA:新日本プロレス オカダ・カズチカ入場テーマ
BPM:165
WAVE:RAINMAKER.ogg
OFFSET:-1.758
DEMOSTART:13.394

//Shinuchi: 5200/3270/1900/1460

COURSE:Oni
LEVEL:8
BALLOON:50,5
SCOREINIT:470,1690
SCOREDIFF:123

#START
33300040,
33300040,
33300040,
33033303,

1010222010102220,
1010222010102220,
1110222011102220,
33334000, //8

#GOGOSTART
1110201022202010,
2210201022104010,
1110201022201020,
1110221110104000,

1110201022202010,
2210201022104010,
1110201022201020,
1011101110112220, //16
#GOGOEND

7,
0,
0,
8,

1010222010102220,
1010222010102220,
1022102210221222,
1000300040002222, //24

#GOGOSTART
1110201022202010,
2210201022104010,
1110201022201020,
1110221110104000,

1110201022202010,
2210201022104010,
1110201022201020,
1011101112112111, //32
#GOGOEND

22400000,
22400000,
22400000,
22400000,

22201020,
22201020,
22201020,
2210221070008000, //40

#SCROLL 0.5
10112011,
21102110,
10112011,
21102212,

10112011,
21102110,
10112011,
11221222, //48

11102000,
11102000,
11102000,
11011101,

1010222010102220,
1010222010102220,
1010222010102220,
#SCROLL 1
600000000000000000000008000000000000400000000000, //56

#GOGOSTART
1110201022202010,
2210201022104010,
1110201022201020,
1110221110104000,

1110201022202010,
2210201022104010,
1110201022203000,
0333,

1110201022201022,
1011012110104000,
1110201022201022,
1022121210104000,

1110201022201022,
1010222212104000,
1110201022201020,
1011222210112210, //72
#GOGOEND

44400030,
44400030,
44400030,
44400030,

44400030,
44400030,
44400030,
4040400002022222,

2,
0, //82
#END


COURSE:Hard
LEVEL:5
BALLOON:40
SCOREINIT:430,2270
SCOREDIFF:118

#START
33300040,
33300040,
33300040,
33033303,

11201120,
11201120,
1110200011102000,
33334000, //8

#GOGOSTART
11212210,
22112230,
11212210,
22112232,

11212210,
22112230,
11212210,
1011101020201020, //16
#GOGOEND

7,
0,
0,
8,

11201120,
11201120,
12121212,
3340, //24

#GOGOSTART
11212210,
22112230,
11212210,
22112232,

11212210,
22112230,
11212212,
1011101110111000, //32
#GOGOEND

22400000,
22400000,
22400000,
22400000,

22201020,
22201020,
22201020,
22200000, //40

#SCROLL 0.5
12,
11201120,
12,
11102020,

12,
11201120,
12,
10102220, //48

11100000,
11100000,
11100000,
11011101,

11201120,
11201120,
11201120,
#SCROLL 1
600000000000000000000008000000000000400000000000, //56

#GOGOSTART
11212210,
22112230,
11212210,
22112232,

11212210,
22112230,
11212230,
0333, //64

1010201022201000,
1011101110103000,
1010201022201000,
1022202220203020,

1010201022201000,
1011101110103000,
1010201022201000,
1110111011111000, //72
#GOGOEND

44400030,
44400030,
44400030,
44400030,

44400030,
44400030,
44400030,
4040400002022020,

4,
0, //82
#END


COURSE:Normal
LEVEL:4
BALLOON:25
SCOREINIT:560,3910
SCOREDIFF:173

#START
3,
3,
3,
3,

1212,
1212,
1212,
3340, //8

#GOGOSTART
1122,
1110,
1122,
1110,

1122,
1110,
1122,
1110, //16
#GOGOEND

7,
0,
0,
8,

1212,
1212,
1212,
3340, //24

#GOGOSTART
1122,
1110,
1122,
1110,

1122,
1110,
1122,
1110, //32
#GOGOEND

22400000,
22400000,
22400000,
22400000,

22201020,
22201020,
22201020,
22204000, //40

1120,
1110,
1120,
1110,

1120,
1110,
1120,
1110, //48

11100000,
11100000,
11100000,
11001100,

1212,
1212,
1212,
600000000000000000000000000000000008000000000000, //56

#GOGOSTART
1122,
1110,
1122,
1110,

1122,
1110,
1123,
0333, //64

1122,
1113,
1122,
1113,

1122,
1113,
1122,
1111, //72
#GOGOEND

44400000,
44400000,
44400000,
44400000,

44400000,
44400000,
44400000,
44400000,

4,
0, //82
#END


COURSE:Easy
LEVEL:3
BALLOON:20,3,3,3,3
SCOREINIT:550,6310
SCOREDIFF:198

#START
3,
3,
3,
3,

12,
12,
12,
3340, //8

#GOGOSTART
11,
1110,
11,
1110,

11,
1110,
11,
1110, //16
#GOGOEND

7,
0,
0,
8,

12,
12,
12,
3340, //24

#GOGOSTART
11,
1110,
11,
1110,

11,
1110,
11,
1110, //32
#GOGOEND

4400,
4400,
4400,
4400,

2210,
2210,
2210,
2240, //40

11,
1,
11,
1,

11,
1,
11,
1, //48

78,
78,
78,
78,

12,
12,
12,
3, //56

#GOGOSTART
11,
1110,
11,
1110,

11,
1110,
0103,
0333, //64

0101,
0113,
0101,
0113,

0101,
0113,
0101,
1110, //72
#GOGOEND

2200,
2200,
2200,
2200,

2200,
2200,
2200,
2200,

4,
0, //82
#END