//TJADB Project
TITLE:Aleph-0
SUBTITLE:--LeaF
BPM:125
WAVE:Aleph-0.ogg
OFFSET:-1.926
DEMOSTART:46.137
SCOREMODE:2


COURSE:Oni
LEVEL:10
BALLOON:1,9,120
SCOREINIT:1690
SCOREDIFF:0

#START
#BARLINEOFF
0,
0, //2

#BARLINEON
#MEASURE 2/4
#SCROLL 2
4,
#MEASURE 4/4
0000000000001011,
2022122210221220, //5

1011101010210010,
1101121010223000,
1011101010210010,
#MEASURE 2/4
11011010,
#BARLINEOFF
#MEASURE 1/16
#BPMCHANGE 15.625
#SCROLL 1
708, //10

#BARLINEON
#MEASURE 4/4
#BPMCHANGE 125
#SCROLL 2
1011101010210010,
1101121010223000,
1011101010210010,
1101121010003000, //14

#BPMCHANGE 70
#SCROLL 0.5
1000201020101100,
#BARLINEOFF
1000101210101100,
#BA<PERSON>IN<PERSON><PERSON>
1000201020101100,
#BARLINEOFF
1020101011117000,
#BARLINEON
#MEASURE 1/16
#BPMCHANGE 17.5
8, //19

#MEASURE 15/16
#BPMCHANGE 125
#SCROLL 1
4,
#MEASURE 1/8
#SCROLL 2
5,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
8, //36

#MEASURE 4/4
#BPMCHANGE 251.50
#SCROLL 1
#GOGOSTART
11012020,
#BPMCHANGE 255.49
11021020,
#BPMCHANGE 259.49
11012110,
#BPMCHANGE 263.50
4444, //40

#BPMCHANGE 267.50
11012020,
#BPMCHANGE 271.49
11021020,
#BPMCHANGE 275.50
11012110,
#BPMCHANGE 279.50
4444, //44

#BPMCHANGE 283.49
11012020,
#BPMCHANGE 287.50
11021020,
#BPMCHANGE 291.50
11012110,
#BPMCHANGE 295.49
4444, //48

#BPMCHANGE 299.49
11012020,
#BPMCHANGE 303.50
11021020,
#BPMCHANGE 307.50
11012110,
#BPMCHANGE 311.50
4444, //52

#BPMCHANGE 315.50
11012020,
#BPMCHANGE 319.49
11021020,
#BPMCHANGE 323.50
11012110,
#BPMCHANGE 327.49
4444, //56

#BPMCHANGE 331.50
11012020,
#BPMCHANGE 335.50
11021020,
#BPMCHANGE 339.50
11012110,
#BPMCHANGE 343.50
4444, //60

#BPMCHANGE 347.50
11012020,
#BPMCHANGE 351.49
11021020,
#BPMCHANGE 355.50
11012110,
#BPMCHANGE 359.50
4444, //64

#BPMCHANGE 363.50
11012020,
#BPMCHANGE 367.49
11021020,
#BPMCHANGE 371.50
11012110,
#BPMCHANGE 375.50
4444, //68

#MEASURE 3/4
#BPMCHANGE 378.99
111110,
#BPMCHANGE 382
111110,
#BPMCHANGE 385
111110,
#BPMCHANGE 388
111110, //72

#BPMCHANGE 391
222220,
#BPMCHANGE 394
222220,
#MEASURE 4/4
#BPMCHANGE 397.51
22222222, //75
#GOGOEND

#BPMCHANGE 200
#SCROLL 0.5
0,
#BARLINEOFF
0,
1111111111111111,
2111211121121121, //79

#BPMCHANGE 250
#SCROLL 1
#BARLINEON
3,
#BARLINEOFF
0002, //81

#BARLINEON
#GOGOSTART
111020102020
#GOGOEND
5000,
000000000000000000000000000008000000
#GOGOSTART
100000000000,
111020221010
#GOGOEND
5000,
000008000000
#GOGOSTART
100000100000100000200000100000100000, //85

111020102020
#GOGOEND
5000,
000000000000000000000000000008000000
#GOGOSTART
100000000000,
111020221010
#GOGOEND
5000,
000008000000
#GOGOSTART
100000100000100000200000100000100000, //89
#GOGOEND

3022,
1222,
#MEASURE 2/4
#SCROLL 0.5
1,
2,
2,
22, //95

#MEASURE 4/4
#SCROLL 1
3,
22,
3,
22, //99

3,
22,
32,
22, //103

3,
22,
32,
2, //107

3,
2,
3,
22, //111

#SCROLL 2
#GOGOSTART
1110101010101010,
2220111022201110,
#GOGOEND
#SCROLL 1
10222022,
#SCROLL 2
#GOGOSTART
1110101011101010, //115

1110101010101010,
1110101010101010,
2220111022201110,
#GOGOEND
#SCROLL 1
12221222, //119

#SCROLL 2
#GOGOSTART
1110101010101010,
2220111022201110,
#GOGOEND
#SCROLL 1
10222022,
#SCROLL 2
#GOGOSTART
1110101011101010, //123

1110101010201020,
1110101010201020,
2220111022201110,
#GOGOEND
#SCROLL 1
12221222, //127

#BPMCHANGE 10
700000000000000800000000000000000000000000000000, //128
#END


COURSE:Hard
LEVEL:8
BALLOON:1,17,85
SCOREINIT:2580
SCOREDIFF:0

#START
#BARLINEOFF
0,
0, //2

#BARLINEON
#MEASURE 2/4
#SCROLL 2
4,
#MEASURE 4/4
00000011,
2000102010201110, //5

1010101020010010,
11212030,
1010101020010010,
#MEASURE 2/4
1121,
#BARLINEOFF
#MEASURE 1/16
#BPMCHANGE 15.625
#SCROLL 1
708, //10

#BARLINEON
#MEASURE 4/4
#BPMCHANGE 125
#SCROLL 2
1010202020010010,
11212030,
1010202020010010,
11212000, //14

#BPMCHANGE 70
#SCROLL 0.5
1000200000101100,
#BARLINEOFF
1000100000101100,
#BARLINEON
1000200000101100,
#BARLINEOFF
100000000000100000100000700000000000000000000008,
#BARLINEON
#MEASURE 1/16
#BPMCHANGE 17.5
0, //19

#MEASURE 15/16
#BPMCHANGE 125
#SCROLL 1
4,
#MEASURE 1/8
#SCROLL 2
5,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
8, //36

#MEASURE 4/4
#BPMCHANGE 251.50
#SCROLL 1
#GOGOSTART
10010020,
#BPMCHANGE 255.49
11001020,
#BPMCHANGE 259.49
11011020,
#BPMCHANGE 263.50
4444, //40

#BPMCHANGE 267.50
10010020,
#BPMCHANGE 271.49
11001020,
#BPMCHANGE 275.50
11011020,
#BPMCHANGE 279.50
4444, //44

#BPMCHANGE 283.49
10010020,
#BPMCHANGE 287.50
11001020,
#BPMCHANGE 291.50
11011020,
#BPMCHANGE 295.49
4444, //48

#BPMCHANGE 299.49
10010020,
#BPMCHANGE 303.50
11001020,
#BPMCHANGE 307.50
11011020,
#BPMCHANGE 311.50
4444, //52

#BPMCHANGE 315.50
10010020,
#BPMCHANGE 319.49
11001020,
#BPMCHANGE 323.50
11011020,
#BPMCHANGE 327.49
4444, //56

#BPMCHANGE 331.50
10010020,
#BPMCHANGE 335.50
11001020,
#BPMCHANGE 339.50
11011020,
#BPMCHANGE 343.50
4444, //60

#BPMCHANGE 347.50
10010020,
#BPMCHANGE 351.49
11001020,
#BPMCHANGE 355.50
11011020,
#BPMCHANGE 359.50
4444, //64

#BPMCHANGE 363.50
10010020,
#BPMCHANGE 367.49
11001020,
#BPMCHANGE 371.50
11011020,
#BPMCHANGE 375.50
4444, //68

#MEASURE 3/4
#BPMCHANGE 378.99
500000000000000000000008000000000000,
#BPMCHANGE 382
500000000000000000000008000000000000,
#BPMCHANGE 385
500000000000000000000008000000000000,
#BPMCHANGE 388
500000000000000000000008000000000000, //72

#BPMCHANGE 391
600000000000000000000008000000000000,
#BPMCHANGE 394
600000000000000000000008000000000000,
#MEASURE 4/4
#BPMCHANGE 397.51
600000000000000000000000000000000008000000000000, //75
#GOGOEND

#BPMCHANGE 200
#SCROLL 0.5
0,
#BARLINEOFF
0,
11111111,
21212121, //79

#BPMCHANGE 250
#SCROLL 1
#BARLINEON
3,
#BARLINEOFF
0002, //81

#BARLINEON
#GOGOSTART
110100
#GOGOEND
50,
000000000000000000000000000008000000
#GOGOSTART
000000000000,
110200
#GOGOEND
50,
000008000000
#GOGOSTART
100000000000100000200000000000000000, //85

110100
#GOGOEND
50,
000000000000000000000000000008000000
#GOGOSTART
000000000000,
110200
#GOGOEND
50,
000008000000
#GOGOSTART
100000100000000000200000000000000000, //89
#GOGOEND

32,
1222,
#MEASURE 2/4
#SCROLL 0.5
1,
2,
2,
22, //95

#MEASURE 4/4
#SCROLL 1
3,
22,
3,
22, //99

3,
22,
32,
22, //103

3,
22,
32,
2, //107

3,
2,
3,
22, //111

#SCROLL 2
#GOGOSTART
11111111,
11111111,
#GOGOEND
#SCROLL 1
1,
#SCROLL 2
#GOGOSTART
11111111, //115

11111111,
22222222,
11111111,
#GOGOEND
#SCROLL 1
11, //119

#SCROLL 2
#GOGOSTART
11111111,
11111111,
#GOGOEND
#SCROLL 1
1,
#SCROLL 2
#GOGOSTART
11111111, //123

11111111,
22222222,
11111111,
#GOGOEND
#SCROLL 1
11, //127

#BPMCHANGE 10
700000000000000800000000000000000000000000000000, //128
#END


COURSE:Normal
LEVEL:7
BALLOON:1,12,50
SCOREINIT:4050
SCOREDIFF:0

#START
#BARLINEOFF
0,
0, //2

#BARLINEON
#MEASURE 2/4
#SCROLL 2
4,
#MEASURE 4/4
0002,
20121210, //5

1010100020020020,
11102030,
1010100020020020,
#MEASURE 2/4
1110,
#BARLINEOFF
#MEASURE 1/16
#BPMCHANGE 15.625
#SCROLL 1
708, //10

#BARLINEON
#MEASURE 4/4
#BPMCHANGE 125
#SCROLL 2
1010200020020020,
11202030,
1010200020020020,
11202000, //14

#BPMCHANGE 70
#SCROLL 0.5
10000110,
#BARLINEOFF
10100110,
#BARLINEON
10000110,
#BARLINEOFF
100000000000100000100000700000000000000000000008,
#BARLINEON
#MEASURE 1/16
#BPMCHANGE 17.5
0, //19

#MEASURE 15/16
#BPMCHANGE 125
#SCROLL 1
4,
#MEASURE 1/8
#SCROLL 2
5,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
8, //36

#MEASURE 4/4
#BPMCHANGE 251.50
#SCROLL 1
#GOGOSTART
1,
#BPMCHANGE 255.49
11001000,
#BPMCHANGE 259.49
11,
#BPMCHANGE 263.50
4440, //40

#BPMCHANGE 267.50
1,
#BPMCHANGE 271.49
11001000,
#BPMCHANGE 275.50
11,
#BPMCHANGE 279.50
4440, //44

#BPMCHANGE 283.49
1,
#BPMCHANGE 287.50
11001000,
#BPMCHANGE 291.50
11,
#BPMCHANGE 295.49
4440, //48

#BPMCHANGE 299.49
10010000,
#BPMCHANGE 303.50
11001000,
#BPMCHANGE 307.50
11,
#BPMCHANGE 311.50
4440, //52

#BPMCHANGE 315.50
1,
#BPMCHANGE 319.49
11001000,
#BPMCHANGE 323.50
11,
#BPMCHANGE 327.49
4440, //56

#BPMCHANGE 331.50
1,
#BPMCHANGE 335.50
11001000,
#BPMCHANGE 339.50
11,
#BPMCHANGE 343.50
4440, //60

#BPMCHANGE 347.50
1,
#BPMCHANGE 351.49
11001000,
#BPMCHANGE 355.50
11,
#BPMCHANGE 359.50
4440, //64

#BPMCHANGE 363.50
1,
#BPMCHANGE 367.49
11001000,
#BPMCHANGE 371.50
11,
#BPMCHANGE 375.50
4440, //68

#MEASURE 3/4
#BPMCHANGE 378.99
500000000000000000000008000000000000,
#BPMCHANGE 382
500000000000000000000008000000000000,
#BPMCHANGE 385
500000000000000000000008000000000000,
#BPMCHANGE 388
500000000000000000000008000000000000, //72

#BPMCHANGE 391
600000000000000000000008000000000000,
#BPMCHANGE 394
600000000000000000000008000000000000,
#MEASURE 4/4
#BPMCHANGE 397.51
600000000000000000000000000000000008000000000000, //75
#GOGOEND

#BPMCHANGE 200
#SCROLL 0.5
0,
#BARLINEOFF
0,
1111,
11111110, //79

#BPMCHANGE 250
#SCROLL 1
#BARLINEON
3,
#BARLINEOFF
0002, //81

#BARLINEON
#GOGOSTART
100100
#GOGOEND
50,
000000000000000000000000000008000000
#GOGOSTART
000000000000,
110100
#GOGOEND
50,
000008000000
#GOGOSTART
100000000000000000200000000000000000, //85

100100
#GOGOEND
50,
000000000000000000000000000008000000
#GOGOSTART
000000000000,
110100
#GOGOEND
50,
000008000000
#GOGOSTART
100000000000000000200000000000000000, //89
#GOGOEND

3,
2,
#MEASURE 2/4
#SCROLL 0.5
1,
0,
2,
2, //95

#MEASURE 4/4
#SCROLL 1
3,
22,
3,
22, //99

3,
22,
32,
22, //103

3,
22,
32,
2, //107

3,
2,
3,
22, //111

#SCROLL 2
#GOGOSTART
1111,
1111,
#GOGOEND
#SCROLL 1
1,
#SCROLL 2
#GOGOSTART
1111, //115

1111,
1111,
1111,
#GOGOEND
#SCROLL 1
1, //119

#SCROLL 2
#GOGOSTART
1111,
1111,
#GOGOEND
#SCROLL 1
1,
#SCROLL 2
#GOGOSTART
1111, //123

1111,
1111,
1111,
#GOGOEND
#SCROLL 1
1, //127

#BPMCHANGE 10
700000000000000800000000000000000000000000000000, //128
#END


COURSE:Easy
LEVEL:5
BALLOON:1,10,40
SCOREINIT:6430
SCOREDIFF:0

#START
#BARLINEOFF
0,
0, //2

#BARLINEON
#MEASURE 2/4
#SCROLL 2
4,
#MEASURE 4/4
0002,
2022, //5

1120,
1120,
1120,
#MEASURE 2/4
11,
#BARLINEOFF
#MEASURE 1/16
#BPMCHANGE 15.625
#SCROLL 1
708, //10

#BARLINEON
#MEASURE 4/4
#BPMCHANGE 125
#SCROLL 2
1220,
1220,
1220,
1220, //14

#BPMCHANGE 70
#SCROLL 0.5
1001,
#BARLINEOFF
1001,
#BARLINEON
1001,
#BARLINEOFF
100000000000000000000000700000000000000000000008,
#BARLINEON
#MEASURE 1/16
#BPMCHANGE 17.5
0, //19

#MEASURE 15/16
#BPMCHANGE 125
#SCROLL 1
4,
#MEASURE 1/8
#SCROLL 2
5,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
8, //36

#MEASURE 4/4
#BPMCHANGE 251.50
#SCROLL 1
#GOGOSTART
1,
#BPMCHANGE 255.49
1,
#BPMCHANGE 259.49
11,
#BPMCHANGE 263.50
44, //40

#BPMCHANGE 267.50
1,
#BPMCHANGE 271.49
1,
#BPMCHANGE 275.50
11,
#BPMCHANGE 279.50
44, //44

#BPMCHANGE 283.49
1,
#BPMCHANGE 287.50
1,
#BPMCHANGE 291.50
11,
#BPMCHANGE 295.49
44, //48

#BPMCHANGE 299.49
1,
#BPMCHANGE 303.50
1,
#BPMCHANGE 307.50
11,
#BPMCHANGE 311.50
44, //52

#BPMCHANGE 315.50
1,
#BPMCHANGE 319.49
1,
#BPMCHANGE 323.50
11,
#BPMCHANGE 327.49
44, //56

#BPMCHANGE 331.50
1,
#BPMCHANGE 335.50
1,
#BPMCHANGE 339.50
11,
#BPMCHANGE 343.50
44, //60

#BPMCHANGE 347.50
1,
#BPMCHANGE 351.49
1,
#BPMCHANGE 355.50
11,
#BPMCHANGE 359.50
44, //64

#BPMCHANGE 363.50
1,
#BPMCHANGE 367.49
1,
#BPMCHANGE 371.50
11,
#BPMCHANGE 375.50
44, //68

#MEASURE 3/4
#BPMCHANGE 378.99
500000000000000000000008000000000000,
#BPMCHANGE 382
500000000000000000000008000000000000,
#BPMCHANGE 385
500000000000000000000008000000000000,
#BPMCHANGE 388
500000000000000000000008000000000000, //72

#BPMCHANGE 391
600000000000000000000008000000000000,
#BPMCHANGE 394
600000000000000000000008000000000000,
#MEASURE 4/4
#BPMCHANGE 397.51
600000000000000000000000000000000008000000000000, //75
#GOGOEND

#BPMCHANGE 200
#SCROLL 0.5
0,
#BARLINEOFF
0,
11,
1111, //79

#BPMCHANGE 250
#SCROLL 1
#BARLINEON
3,
#BARLINEOFF
0, //81

#BARLINEON
#GOGOSTART
100100
#GOGOEND
50,
000000000000000000000000000008000000
#GOGOSTART
000000000000,
100100
#GOGOEND
50,
000008000000
#GOGOSTART
000000000000000000000000000000000000, //85

100100
#GOGOEND
50,
000000000000000000000000000008000000
#GOGOSTART
000000000000,
100100
#GOGOEND
50,
000008000000
#GOGOSTART
000000000000000000000000000000000000, //89
#GOGOEND

3,
0,
#MEASURE 2/4
#SCROLL 0.5
1,
0,
2,
0, //95

#MEASURE 4/4
#SCROLL 1
3,
22,
3,
22, //99

3,
22,
32,
22, //103

3,
22,
32,
2, //107

3,
2,
3,
22, //111

#SCROLL 2
#GOGOSTART
11,
11,
#GOGOEND
#SCROLL 1
1,
#SCROLL 2
#GOGOSTART
11, //115

11,
11,
11,
#GOGOEND
#SCROLL 1
1, //119

#SCROLL 2
#GOGOSTART
11,
11,
#GOGOEND
#SCROLL 1
1,
#SCROLL 2
#GOGOSTART
11, //123

11,
11,
11,
#GOGOEND
#SCROLL 1
1, //127

#BPMCHANGE 10
700000000000000800000000000000000000000000000000, //128
#END