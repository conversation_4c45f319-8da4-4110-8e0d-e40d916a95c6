//TJADB Project
TITLE:Jamboree <PERSON>!
TITLEJA:ジャンボリミッキー！
SUBTITLE:--
SUBTITLEJA:
BPM:143
WAVE:Jamboree <PERSON>.ogg
OFFSET:-1.934
DEMOSTART:17.008

COURSE:Edit
LEVEL:9
BALLOON:
SCOREINIT:500,1480
SCOREDIFF:128

#START
100000100000100000100000100100100100101010101010,
1020002200220020,
1020002200220020,
1020002200220020,
12023232,

1022002210220022,
1022002210220022,
1022102210221022,
1010101012121210, //9

#GOGOSTART
1202102112011022,
1202102112011022,
1202102112011022,
1202102211021000,

1102112011021120,
1102112011021120,
1120112011201120,
111100111100111111100200, //17
#GOGOEND

3001101022022020,
1001101022022020,
1001101022022020,
3333,

1122121211221212,
1122112211021020,
1111222211112222,
100100200200100100200200100000101010200000000000, //25

1122121211221212,
1122112211021020,
1111222211112222,
100100200200100100200200100000101010200000200000,

10421042,
10421042,
10421042,
100000400111200000400200,

10421042,
100000400111200000400200,
100000400111200000400200,
100000400111200100400000, //37

#GOGOSTART
1122112211221122,
100100000200100000500000000008000000200000200000,
1122102011201120,
1122102030203000,

1122112211221122,
100100000200100000500000000008000000200000200000,
1122102011221122,
1111211120002020, //45

1202102212021020,
1221221011021020,
1202102212021020,
1221221011021020,

1022102210121122,
1022102210121122,
1022102210121122,
100000000100100000100000100100100100101010101010, //53
#GOGOEND

10221122,
11221122,
11221122,
11221122,

12121212,
1120112011201120,
1121112111211120,
3333,
0,
0, //63
#END


COURSE:Oni
LEVEL:5
BALLOON:2,2
SCOREINIT:630,2230
SCOREDIFF:170

#START
0,
1001001020222000,
1001001020202220,
1001001020222000,
1001001030003000,

1002001110020011,
1002001110020010,
3333,
600000000000000000000000000000000000000008000000, //9

#GOGOSTART
1001001010222010,
1001001020202220,
1001001010222010,
1001001030003000,

1002001110020011,
1002001110020010,
3333,
33333333, //17
#GOGOEND

0,
0,
500000000000000000000000000000000000000008000000,
3333,

11121112,
1020102010222020,
11221122,
12121040, //25

11121112,
1020102010222020,
11221122,
12121001,

12211221,
12211221,
12211221,
1020201110202010,

12211221,
1020201110202010,
1020201110202010,
1020201110004000, //37

#GOGOSTART
1011101110111011,
1001007080222020,
1011101000222020,
1011102030203000,

1011101110111011,
1001007080222020,
1011102030203011,
1020302030222020, //45

500000000000000000000000000000000000000008000000,
2002002000221020,
500000000000000000000000000000000000000008000000,
1001001000222020,

1020202010221020,
1020202010222020,
1020202010221020,
1333, //53
#GOGOEND

2222,
10202011,
1222,
10202011,

1020102010221020,
1020102010222020,
1020102010221020,
3333,
0,
0, //63
#END


COURSE:Hard
LEVEL:4
BALLOON:
SCOREINIT:690,3260
SCOREDIFF:225

#START
0,
1001001020002000,
1001001020002000,
1001001020002000,
1001001030003000,

1001001020020020,
1001001020020020,
3333,
600000000000000000000000000000000008000000000000, //9

#GOGOSTART
1001001020002000,
1001001020002000,
1001001020002000,
1001001030003000,

1001001020020020,
1001001020020020,
3333,
33333333, //17
#GOGOEND

0,
0,
500000000000000000000000000000000008000000000000,
3333,

11101110,
11111020,
11101110,
12121020, //25

11101110,
11111020,
11101110,
12121001,

10011001,
10011001,
10011001,
10022001,

10011001,
10022001,
10022001,
10022040, //37

#GOGOSTART
1011100010111000,
1001001000000000,
1011102000200000,
1011102000200000,

1011100010111000,
1001001000000000,
1011102000200000,
32323011, //45

500000000000000000000000000000000008000000000000,
2002002000001010,
500000000000000000000000000000000008000000000000,
2002002000000010,

12202201,
12202201,
12202022,
2333, //53
#GOGOEND

0001,
10000011,
1001,
10000011,

1221,
10202011,
10202011,
3333,
0,
0, //63
#END


COURSE:Normal
LEVEL:3
BALLOON:7
SCOREINIT:850,5390
SCOREDIFF:338

#START
0,
1011,
1011,
1011,
1033,

11,
11,
3333,
600000000000000000000000000008000000000000000000, //9

#GOGOSTART
1011,
1011,
1011,
1033,

11,
11,
3333,
70000008, //17
#GOGOEND

0,
0,
500000000000000000000000000008000000000000000000,
3333,

11,
1110,
11,
1110, //25

11,
1110,
11,
10101001,

10011001,
10011001,
10011001,
10022001,

10011001,
10022001,
10022001,
10022000, //37

#GOGOSTART
1111,
1001001000000000,
2,
2033,

1111,
1001001000000000,
3,
3330, //45

500000000000000000000000000008000000000000000000,
1001001000000000,
500000000000000000000000000008000000000000000000,
1001001000000000,

12,
12,
12,
0333, //53
#GOGOEND

0001,
10000011,
1001,
10000011,

1001,
10000011,
10000011,
3333,
0,
0, //63
#END


COURSE:Easy
LEVEL:2
BALLOON:5
SCOREINIT:720,7260
SCOREDIFF:423

#START
0,
1,
1,
1,
1033,

11,
11,
3333,
600000000000000000000000000008000000000000000000, //9

#GOGOSTART
11,
11,
11,
1033,

11,
11,
3333,
70000008, //17
#GOGOEND

0,
0,
500000000000000000000000000008000000000000000000,
3333,

11,
1,
11,
1, //25

11,
1,
11,
1,

11,
11,
11,
12,

11,
12,
12,
11, //37

#GOGOSTART
1110,
1,
2,
2033,

1110,
1,
3,
3330, //45

500000000000000000000000000008000000000000000000,
1,
500000000000000000000000000008000000000000000000,
1,

12,
12,
12,
0333, //53
#GOGOEND

0001,
1001,
1001,
1001,

1001,
1001,
1001,
3333,
0,
0, //63
#END