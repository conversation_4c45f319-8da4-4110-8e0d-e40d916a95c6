//TJADB Project
TITLE:1・2・3
TITLEJA:１・２・３
SUBTITLE:--Pokémon (2019)
SUBTITLEJA:アニメ「ポケットモンスター(2019)」より
BPM:182
WAVE:1 2 3 (2019).ogg
OFFSET:-0.827
DEMOSTART:38.713

//shinuchi: 12950/10170/3550/2180

COURSE:Oni
LEVEL:6
BALLOON:11,10,14
SCOREINIT:630,2330
SCOREDIFF:165

#START
#BARLINEOFF
#MEASURE 2/4
22000000,

#BARLINEON
#MEASURE 4/4
11220112,
01220112,
0011202010102010,
02201103,
0040004030222010, //6

01112101,
01110121,
01112101,
01110122,

02110122,
02110122,
0020102010222030,
0030300030102011, //14

1010201020100011,
1010201000102011,
1010201020100022,
2020101000102022,

2010201020100011,
11222125,
000000000000000000000000000000000000000008000000,
00011020, //22

#MEASURE 3/4
112,
112,
112,
#MEASURE 4/4
7008,

10211120,
10211120,
11221121,
01122040, //30

#GOGOSTART
3000300030001011,
11210220,
10112112,
0022101000202010,

1010200010112010,
1010200010112010,
11201122,
0022101020101110, //38

1000201000201011,
11201112,
1000201000201011,
11201122,

1000201110102010,
1011100010112000,
1110400011104000,
33340220, //46

3000201010201011,
11210220,
10112112,
0022101000202010,

1010200010112010,
1010200010112010,
11201122,
0022101020101011, //54

2010102000101011,
2010102000112020,
1011201010222070,
0000000080001022,

2020101020001022,
2020101020001022,
22112211,
02210020, //62

30211121,
11210020,
30211121,
12210022,

700000000000000000000000000000000000000000000008,
#GOGOEND
0, //68
#END


COURSE:Hard
LEVEL:4
BALLOON:9,11,9,9,8,10
SCOREINIT:780,3970
SCOREDIFF:215

#START
#BARLINEOFF
#MEASURE 2/4
0,

#BARLINEON
#MEASURE 4/4
10110111,
01110225,
000000000000000000000000000000000000000008000000,
02201103,
03033001, //6

00101101,
00110101,
00101102,
00220111,

00220111,
00220111,
01102023,
00000111, //14

00101101,
00110111,
00101102,
00110222,

00201101,
00222015,
000000000000000000000000000000000000000008000000,
0, //22

#MEASURE 3/4
102,
112,
102,
#MEASURE 4/4
7008,

10111010,
1112,
900000000000000000000000000000000009000000000008,
0, //30

#GOGOSTART
30303011,
01110220,
10102011,
01110221,

10201101,
10201011,
7008,
0, //38

10110202,
01101112,
20110201,
01102010,

10110020,
1122,
3434,
30340000, //46

30011011,
01110220,
10011022,
01110221,

00201101,
10201011,
7008,
00000111, //54

02220111,
01120111,
01110207,
00008012,

02102012,
02102022,
01102011,
01110020, //62

30011020,
10110020,
30011020,
10110022,

700000000000000000000000000000000000000000000008,
#GOGOEND
0,
#END


COURSE:Normal
LEVEL:1
BALLOON:10,8,6,6,8
SCOREINIT:1250,11680
SCOREDIFF:788

#START
#BARLINEOFF
#MEASURE 2/4
0,

#BARLINEON
#MEASURE 4/4
1,
10000005,
000000000000000000000000000000000000000008000000,
0,
0, //6

11,
1,
11,
1,

2,
2,
11,
0, //14

11,
1100,
11,
1100,

2,
20002005,
000000000000000000000000000000000000000008000000,
0, //22

#MEASURE 3/4
101,
101,
7,
#MEASURE 4/4
00000800,

11,
1110,
900000000000000000000000000000000009000000000008,
0, //30

#GOGOSTART
3330,
1,
1110,
1,

2,
22,
7008,
0, //38

10010000,
1,
10010000,
1,

1,
11,
33,
3, //46

31,
1,
11,
1,

2,
22,
7008,
0, //54

11,
22,
11,
2,

1110,
1110,
6,
000000000000000000000000000008000000000000000000, //62

31,
1100,
31,
1100,

700000000000000000000000000000000000000000000008,
#GOGOEND
0, //68
#END


COURSE:Easy
LEVEL:1
BALLOON:8,6,5,5,7
SCOREINIT:1190,15290
SCOREDIFF:900

#START
#BARLINEOFF
#MEASURE 2/4
0,

#BARLINEON
#MEASURE 4/4
1,
10000005,
000000000000000000000000000000000000000008000000,
0,
0, //6

1,
1,
1,
1,

2,
2,
11,
0, //14

11,
1,
11,
1,

2,
20000005,
000000000000000000000000000000000000000008000000,
0, //22

#MEASURE 3/4
1,
1,
7,
#MEASURE 4/4
00000800,

1,
11,
900000000000000000000000000000000009000000000008,
0, //30

#GOGOSTART
3330,
1,
11,
1,

2,
2,
7008,
0, //38

1,
1,
2,
2,

1,
11,
33,
3, //46

31,
1,
11,
1,

2,
2,
7008,
0, //54

1,
1,
11,
1,

11,
11,
6,
000000000000000000000000000008000000000000000000, //62

31,
1,
31,
1,

700000000000000000000000000000000000000000000008,
#GOGOEND
0, //68
#END