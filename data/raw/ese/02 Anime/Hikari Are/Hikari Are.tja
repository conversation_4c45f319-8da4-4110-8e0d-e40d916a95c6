//TJADB Project
TITLE:Hikari Are
TITLEJA:ヒカリアレ
SUBTITLE:--BURNOUT SYNDROMES/Haikyuu‼
SUBTITLEJA:「ハイキュー!! 烏野高校 VS 白鳥沢学園高校」より
BPM:178
WAVE:Hikari Are.ogg
OFFSET:-1.794
DEMOSTART:33.135

//shinuchi: 6610/4070/2600/1460

COURSE:Oni
LEVEL:9
BALLOON:7,10
SCOREINIT:480,1560
SCOREDIFF:123

#START
#BARLINEOFF
#MEASURE 1/4
02,

#BARLINEON
#MEASURE 4/4
10221022,
1000202010221020,
10221022,
1022102010221020, //5

12221222,
1022102010221020,
1020201020221020,
70008002, //9

1010102010221020,
1011102010221020,
1010102010221020,
1011102010221022, //13

1010102010221022,
1011102010221022,
7008,
#SCROLL 1.75
3444, //17

#SCROLL 1
3002101002101002,
1010021010201120,
1002101002101002,
1010021011201120, //21

1020201022101022,
1020201022201022,
1022201000404000,
#SCROLL 1.75
3444, //25

#SCROLL 1
#GOGOSTART
3010202010112210,
1010202010112210,
1010201010112212,
1011201010112222, //29

1010222010112210,
1011202010112212,
100000200200200000200000500000000000000008000000,
#GOGOEND
#SCROLL 1.75
3444, //33

#SCROLL 1
#GOGOSTART
3010221010112210,
1011202210112210,
1010221010112212,
1011201010112222, //37

1010221010112210,
1011202210112210,
1122201122201000,
#GOGOEND
#SCROLL 1.75
3444, //41

#SCROLL 1
#GOGOSTART
3010221010112210,
1011202210112210,
1010221210112212,
1011201120112222, //45

1010221010112210,
1011202210112222,
100100200200200000100200500000000000000008000000,
#GOGOEND
#SCROLL 1.75
3444, //49

#SCROLL 1
#GOGOSTART
3010221210112210,
1011202210112210,
1010221210112212,
1011201210112222, //53

1010221210112210,
1011202210112222,
112220112220
#GOGOEND
#SCROLL 1.75
3000,
3
#SCROLL 2.5
4, //57

#SCROLL 1
1022201022201022,
2010222011221222,
1022221022221022,
2010222011221222, //61

#GOGOSTART
1022102012221022,
1022102012221222,
1022102212221022,
1022102212221000, //65
#GOGOEND

#SCROLL 1.75
3444,
#SCROLL 1
3022221022221022,
#MEASURE 9/8
2
#SCROLL 2.5
30000000, //68
#END


COURSE:Hard
LEVEL:6
BALLOON:5,8,5
SCOREINIT:640,2920
SCOREDIFF:175

#START
#BARLINEOFF
#MEASURE 1/4
02,

#BARLINEON
#MEASURE 4/4
10022002,
10022002,
10022002,
10022002,

10022001,
10022002,
10012002,
78, //9

10121010,
10121000,
10121010,
10121000,

10121012,
10121012,
7008,
#SCROLL 1.25
3444, //17

#SCROLL 1
30210210,
21021110,
10210210,
21021110,

12210122,
21012210,
12020330,
#SCROLL 1.25
3444, //25

#SCROLL 1
#GOGOSTART
30221022,
10221020,
10112010,
0000111010002000,

10221022,
10221020,
200200200000000000200000500000000000000008000000,
#GOGOEND
#SCROLL 1.25
3444, //33

#SCROLL 1
#GOGOSTART
30221022,
10221120,
10112010,
0000111010002000,

10201120,
10221125,
000000000000000000000000000000000000000008000000,
#GOGOEND
#SCROLL 1.25
3444, //41

#SCROLL 1
#GOGOSTART
30221022,
10221120,
1000100022202000,
1110200011102000,

10221120,
1000202011102000,
200200200000000000200000500000000000000008000000,
#GOGOEND
#SCROLL 1.25
3444, //49

#SCROLL 1
#GOGOSTART
30221022,
1000202011102000,
1000111020001000,
1000111010002000,

1000200011102000,
1000202011102070,
000800
#GOGOEND
#SCROLL 1.25
40,
3
#SCROLL 1.45
3, //57

#SCROLL 1
10210210,
2010002011101000,
10210210,
2010002011102000,

#GOGOSTART
1020102000201110,
1020002011101000,
1020102000201110,
1020002011102000,
#GOGOEND

#SCROLL 1.25
3444,
#SCROLL 1
3020201020201022,
#MEASURE 9/8
230000000, //68
#END


COURSE:Normal
LEVEL:5
BALLOON:4,7,8,4,4,17
SCOREINIT:790,5000
SCOREDIFF:233

#START
#BARLINEOFF
#MEASURE 1/4
#SCROLL 0.9
0,
#BARLINEON
#MEASURE 4/4
11,
10011000,
11,
10011000,

10011001,
10022001,
10022001,
78, //9

1111,
1110,
1111,
10111000,

2222,
20222007,
0008,
3344, //17

6,
000000000000000000000008000000000000300000000000,
6,
000008000000000000000000000000000000000000000000,

11010011,
01001010,
22020440,
3344, //25

#GOGOSTART
30101110,
1110,
1122,
0122,

10101110,
1122,
500000000000000000080000,
#GOGOEND
3344, //33

#GOGOSTART
30101110,
1110,
1122,

00101110,
1102,
20110070,
0008,
#GOGOEND
3344, //41

#GOGOSTART
30101110,
10101110,
1127,
0800,

10102220,
10102220,
500000000000000000000000000000000008000000000000,
#GOGOEND
3344, //49

#GOGOSTART
30101110,
10102220,
1122,
00101110,

10102220,
10110017,
000800
#GOGOEND
40,
39, //57

0,
98,
0,
0,

#GOGOSTART
11010011,
01001010,
22020022,
02002020,
#GOGOEND

3344,
600000000000000000000000000000000000000008000000,
#MEASURE 9/8
030000000, //68
#END


COURSE:Easy
LEVEL:3
BALLOON:3,5,5,3,4,14
SCOREINIT:730,8860
SCOREDIFF:298

#START
#BARLINEOFF
#MEASURE 1/4
#SCROLL 0.9
0,
#BARLINEON
#MEASURE 4/4
1,
11,
1,
11,

1,
11,
22,
78, //9

11,
1,
22,
2,

11,
22,
500000000000000000000000000008000000000000000000,
3344, //17

6,
000000000000000008000000000000000000300000000000,
6,
000008000000000000000000000000000000000000000000,

1001,
01,
7008,
3344, //25

#GOGOSTART
31,
11,
12,
0111,

11,
12,
500000000000000000000000000008000000000000000000,
#GOGOEND
3344, //33

#GOGOSTART
31,
11,
12,
0111,

1,
1027,
0008,
#GOGOEND
3344, //41

#GOGOSTART
31,
1110,
1027,
0800,

11,
12,
500000000000000000000000000008000000000000000000,
#GOGOEND
3344, //49

#GOGOSTART
31,
1110,
12,
0111,

1,
1027,
000800
#GOGOEND
30,
49, //57

0,
98,
0,
0,

#GOGOSTART
1110,
1110,
1110,
1110,
#GOGOEND

3344,
6,
#MEASURE 9/8
000008000000000000000000000000000000000000000000000000, //68
#END