//TJADB Project
TITLE:<PERSON>dor<PERSON> Ponpokorin -Old Audio/AC5 Chart-
TITLEJA:おどるポンポコリン
SUBTITLE:--B.B.Queens/<PERSON><PERSON>-chan
SUBTITLEJA:旧音源・AC5譜面 「ちびまる子ちゃん」より
BPM:140
WAVE:Odor<PERSON> Ponpokirin.ogg
OFFSET:-1.752
DEMOSTART:41.187

COURSE:Oni
LEVEL:2
SCOREMODE:0

STYLE:SINGLE
BALLOON:
SCOREINIT:
SCOREDIFF:

#START
0,
10111011,
01011110,

10111012,
02202020,
11101010,
11104040,

1000201110002011,
100000100000000000500000000000000008000000100000,
0011102011100010,
11030000, //11

1000201110002011,
11010001,
0011102020111000,
11030001,

01101000,
1010111000100010,
01101000,
1010111000100000, //19

00102201,
00102205,
000000000000000000000000000000000000000000000008,
0010001011101020,

1000111020001110,
01012110,
1000111010002220,
01012110, //27

1000111010002220,
0010001110101020,
500000000000000000000000000000000008000000000000,
01011212,

1000111010002220,
01012110,
1000111010002220,
0010101010111000, //35

1000111020000010,
01121215,
000000000000000000000000000000000000000000000008,
0044,

1000201110002011,
100000100000000000500000000000000008000000100000,
0011102011100010,
21030000, //43

1000201110002220,
11210001,
0011102020111000,
11030000,

00000220,
1010111000100000,
00000220,
1010111000100000,

00102201,
00102205,
000000000000000000000000000000000000000000000008,
0010001011101020, //55

1000111010001110,
01012110,
1000111010002220,
01012110,

1000111010002220,
0010001110101020,
500000000000000000000000000000000008000000000000,
01011212,

1000111010002220,
01012110,
1000111010002220,
0010101010111000,

1000111020000010,
01121215,
0,
000000000008000000000000000000000000000000000000, //71
#END

STYLE:DOUBLE
BALLOON:
SCOREINIT:
SCOREDIFF:

#START P1
0,
10111011,
01011110,

10111012,
02202020,
11101010,
11104040,

1000201110002011,
100000100000000000500000000000000008000000100000,
0011102011100010,
11030000, //11

1000201110002011,
11010001,
0011102020111000,
11030001,

01101000,
1010111000100010,
01101000,
1010111000100000, //19

00102201,
00102205,
000000000000000000000000000000000000000000000008,
0010001011101020,

1000111020001110,
01012110,
1000111010002220,
01012110, //27

1000111010002220,
0010001110101020,
500000000000000000000000000000000008000000000000,
01011212,

1000111010002220,
01012110,
1000111010002220,
0010101010111000, //35

1000111020000010,
01121215,
000000000000000000000000000000000000000000000008,
0044,

1000201110002011,
100000100000000000500000000000000008000000100000,
0011102011100010,
21030000, //43

1000201110002220,
11210001,
0011102020111000,
11030000,

00000220,
1010111000100000,
00000220,
1010111000100000,

00102201,
00102205,
000000000000000000000000000000000000000000000008,
0010001011101020, //55

1000111010001110,
01012110,
1000111010002220,
01012110,

1000111010002220,
0010001110101020,
500000000000000000000000000000000008000000000000,
01011212,

1000111010002220,
01012110,
1000111010002220,
0010101010111000,

1000111020000010,
01121215,
0,
000000000008000000000000000000000000000000000000, //71
#END

BALLOON:
SCOREINIT:
SCOREDIFF:

#START P2
0,
10111011,
01011110,

10111012,
02202020,
11101010,
11104040,

1000201110002011,
100000100000000000500000000000000008000000100000,
0011102011100010,
11030000, //11

1000201110002011,
11010001,
0011102020111000,
11030000,

00000220,
1010111000100000,
00000220,
1010111000100000, //19

00102201,
00102205,
000000000000000000000000000000000000000000000008,
0010001011101020,

1000111020001110,
01012110,
1000111010002220,
01012110, //27

1000111010002220,
0010001110101020,
500000000000000000000000000000000008000000000000,
01011212,

1000111010002220,
01012110,
1000111010002220,
0010101010111000, //35

1000111020000010,
01121215,
000000000000000000000000000000000000000000000008,
0044,

1000201110002011,
100000100000000000500000000000000008000000100000,
0011102011100010,
21030000, //43

1000201110002220,
11210001,
0011102020111000,
11030001,

01101000,
1010111000100010,
01101000,
1010111000100000,

00102201,
00102205,
000000000000000000000000000000000000000000000008,
0010001011101020, //55

1000111010001110,
01012110,
1000111010002220,
01012110,

1000111010002220,
0010001110101020,
500000000000000000000000000000000008000000000000,
01011212,

1000111010002220,
01012110,
1000111010002220,
0010101010111000,

1000111020000010,
01121215,
0,
000000000008000000000000000000000000000000000000, //71
#END


COURSE:Hard
LEVEL:2
BALLOON:
SCOREINIT:490
SCOREDIFF:110
SCOREMODE:1

#START
0,
10111011,
01011110,
10111012,
02202020,
11101010, //6

//branch condition unknown, 8 Good + 12 OK from stanza #1-5 gives #E.
#BRANCHSTART p,65,80
#N
11104040,

10121012,
100000100000000000500000000000000008000000100000,
01101101,
11030000,

10121012,
11010001,
01101020,
11030001,

01101220,
11010101,
01101220,
11010100,

00102201,
00102205,
000000000000000000000000000000000000000000000008,
01011010, //N23

10121011,
01011010,
10121022,
01011010,

10121022,
01011010,
500000000000000000000000000000000008000000000000,
01011000,

10121022,
01011010,
10121022,
0010101010111000,

10121001,
01101015,
000000000000000000000000000000000000000000000008,
0044, //N39

10111012,
100000100000000000500000000000000008000000100000,
01101101,
10030000,

10111012,
11010001,
01101100,
11030001,

01101220,
11010101,
01101220,
11010100,

00102201,
00102205,
000000000000000000000000000000000000000000000008,
01011010, //N55

10121022,
01011010,
10121022,
01011010,

10121022,
01011010,
500000000000000000000000000000000008000000000000,
01011010,

10121022,
01011010,
10121022,
0010101010111000,

10121001,
01101015,
0,
000000000008000000000000000000000000000000000000, //N71

#E
11104040,

10121012,
100000100000000000500000000000000008000000100000,
01101101,
11030000,

10121012,
11010001,
01101020,
11030001,

01101220,
11110101,
01101220,
11110100,

00102201,
00102205,
000000000000000000000000000000000000000000000008,
01011010, //E23

10121012,
01012010,
10121022,
01012010,

10121022,
01011010,
500000000000000000000000000000000008000000000000,
01011010,

10121022,
01012010,
10121022,
0010101010111000,

10121001,
01101015,
000000000000000000000000000000000000000000000008,
0044, //E39

10121012,
100000100000000000500000000000000008000000100000,
01101101,
11030000,

10121012,
11010001,
01101110,
11030001,

01101220,
11110101,
01101220,
11110100,

00102201,
00102205,
000000000000000000000000000000000000000000000008,
01011010, //E55

10121022,
01012010,
10121022,
01012010,

10121022,
01011010,
500000000000000000000000000000000008000000000000,
01011010,

10121022,
01012010,
10121022,
0010101010111000,

10121001,
01101015,
0,
000000000008000000000000000000000000000000000000, //E71

#M
11104040,

1000201110002011,
100000100000000000500000000000000008000000100000,
0011102011100010,
11030000,

1000201110002011,
11010001,
0011102020111000,
11030001,

01101220,
1010111000100010,
01101220,
1010111000100000,

00102201,
00102205,
000000000000000000000000000000000000000000000008,
0010001011101020, //M23

1000111020001110,
01012110,
1000111010002220,
01012110,

1000111010002220,
0010001110101020,
500000000000000000000000000000000008000000000000,
01011212,

1000111010002220,
01012110,
1000111010002220,
0010101010111000,

1000111020000010,
01121215,
000000000000000000000000000000000000000000000008,
0044, //M39

1000201110002011,
100000100000000000500000000000000008000000100000,
0011102011100010,
21030000,

1000201110002220,
11210001,
0011102020111000,
11030001,

01101220,
1010111000100010,
01101220,
1010111000100000,

00102201,
00102205,
000000000000000000000000000000000000000000000008,
0010001011101020, //M55

1000111010001110,
01012110,
1000111010002220,
01012110,

1000111010002220,
0010001110101020,
500000000000000000000000000000000008000000000000,
01011212,

1000111010002220,
01012110,
1000111010002220,
0010101010111000,

1000111020000010,
01121215,
0,
000000000008000000000000000000000000000000000000, //M71
#BRANCHEND
#END


COURSE:Normal
LEVEL:4
SCOREMODE:1

STYLE:SINGLE
BALLOON:
SCOREINIT:710
SCOREDIFF:180

#START
0,
10111011,
0022,

10111011,
0022,
11101010,
11104040, //7

10121012,
100000000000100000500000000000000008000000100000,
01101010,
11030000,

10121012,
11010001,
01101010,
11030001,

01101000,
10110101,
01101000,
10110300,

0121,
20102205,
000000000000000000000000000000000000000000000008,
01011010, //23

10121012,
0022,
10121012,
0022,

10121012,
0022,
500000000000000000000000000000000008000000000000,
01011010,

10121012,
0022,
10121012,
0010101010111000,

10121000,
10101015,
000000000000000000000000000000000000000000000008,
0044, //39

10121012,
100000000000100000500000000000000008000000100000,
01101010,
11030000,

10121012,
11010001,
01101010,
11030000,

00000220,
10110100,
00000220,
10110100,

0121,
20102205,
000000000000000000000000000000000000000000000008,
01011010, //55

10121012,
0022,
10121012,
0022,

10121012,
0022,
500000000000000000000000000000000008000000000000,
01011010,

10121012,
0022,
10121012,
0010101010111000,

10121000,
10101015,
0,
000000000008000000000000000000000000000000000000, //71
#END

STYLE:DOUBLE
BALLOON:
SCOREINIT:710
SCOREDIFF:180

#START P1
0,
10111011,
0022,

10111011,
0022,
11101010,
11104040, //7

10121012,
100000000000100000500000000000000008000000100000,
01101010,
11030000,

10121012,
11010001,
01101010,
11030001,

01101000,
10110101,
01101000,
10110300,

0121,
20102205,
000000000000000000000000000000000000000000000008,
01011010, //23

10121012,
0022,
10121012,
0022,

10121012,
0022,
500000000000000000000000000000000008000000000000,
01011010,

10121012,
0022,
10121012,
0010101010111000,

10121000,
10101015,
000000000000000000000000000000000000000000000008,
0044, //39

10121012,
100000000000100000500000000000000008000000100000,
01101010,
11030000,

10121012,
11010001,
01101010,
11030000,

00000220,
10110100,
00000220,
10110100,

0121,
20102205,
000000000000000000000000000000000000000000000008,
01011010, //55

10121012,
0022,
10121012,
0022,

10121012,
0022,
500000000000000000000000000000000008000000000000,
01011010,

10121012,
0022,
10121012,
0010101010111000,

10121000,
10101015,
0,
000000000008000000000000000000000000000000000000, //71
#END

BALLOON:
SCOREINIT:710
SCOREDIFF:180

#START P2
0,
10111011,
0022,

10111011,
0022,
11101010,
11104040, //7

10121012,
100000000000100000500000000000000008000000100000,
01101010,
11030000,

10121012,
11010001,
01101010,
11030000,

00000220,
10110100,
00000220,
10110300,

0121,
20102205,
000000000000000000000000000000000000000000000008,
01011010, //23

10121012,
0022,
10121012,
0022,

10121012,
0022,
500000000000000000000000000000000008000000000000,
01011010,

10121012,
0022,
10121012,
0010101010111000,

10121000,
10101015,
000000000000000000000000000000000000000000000008,
0044, //39

10121012,
100000000000100000500000000000000008000000100000,
01101010,
11030000,

10121012,
11010001,
01101010,
11030001,

01101000,
10110101,
01101000,
10110100,

0121,
20102205,
000000000000000000000000000000000000000000000008,
01011010, //55

10121012,
0022,
10121012,
0022,

10121012,
0022,
500000000000000000000000000000000008000000000000,
01011010,

10121012,
0022,
10121012,
0010101010111000,

10121000,
10101015,
0,
000000000008000000000000000000000000000000000000, //71
#END


COURSE:Easy
LEVEL:4 
BALLOON:
SCOREINIT:790
SCOREDIFF:220
SCOREMODE:1

#START
0,
0101,
0101,

0101,
0101,
0101,
0122, //7

0111,
0111,
0111,
0112,

0111,
0111,
0111,
3,

0110,
0202,
0110,
0202,

0101,
0111,
500000000000000000000000000000000008000000000000,
2, //23

10111011,
0101,
10111011,
0101,

10111011,
0101,
500000000000000000000000000000000008000000000000,
0022,

10111011,
0101,
10111011,
00201110,

10111000,
0111,
500000000000000000000000000000000000000000000008,
0022, //39

0111,
0111,
0111,
0112,

0111,
0111,
0111,
3,

0110,
0202,
0110,
0202,

0101,
0111,
500000000000000000000000000000000008000000000000,
2, //55

10111011,
0101,
10111011,
0101,

10111011,
0101,
500000000000000000000000000000000008000000000000,
0022,

10111011,
0101,
10111011,
00201110,

10111000,
0111,
5,
000000000008000000000000000000000000000000000000, //71
#END