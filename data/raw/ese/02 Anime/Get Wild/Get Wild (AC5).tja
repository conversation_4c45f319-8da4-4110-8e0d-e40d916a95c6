//TJADB Project
TITLE:Get Wild -Old Audio/Chart-
TITLEJA:Get Wild
SUBTITLE:--TM NETWORK/City Hunter
SUBTITLEJA:旧音源・譜面 シティーハンター
BPM:133
WAVE:Get Wild (AC5).ogg
OFFSET:-3.644
DEMOSTART:61.162


COURSE:Oni
LEVEL:8
BALLOON:25
SCOREINIT:
SCOREDIFF:
SCOREMODE:0

#START
0,
0,

20202002,
2002002020000022,
2002002020220020,
2002002020003000, //6

1001012210201020,
1001012210201020,
1001012210201020,
1120112011112020,

1001012210201020,
1001012210201020,
1001012210201020,
1120112011112011, //14

2012012110102010,
1110201010000011,
21011221,
1120112011112011,

2012012110102010,
1110201010000011,
21122121,
1120112011112020, //22

1120112011201120,
1120112011112011,
1020102011201020,
1120201120101020,

1120112011201120,
1120112011112010,
1020111010102010,
3333, 
7,
000000000000000000000000000000080000000000100000, //32

30303001,
1001201010002011,
2020112020111120,
1020002011112010,

30303001,
1001201010002011,
2020112020111120,
1020002011112010, //40

30303001,
1001201010002011,
2020112020111120,
1020002011112010,

30303001,
1001201010002011,
2020112040401120,
12023000, //48

0,
0, //50
#END


COURSE:Hard
LEVEL:3
BALLOON:15
SCOREINIT:840
SCOREDIFF:210
SCOREMODE:1

#START
0,
0,

1110,
1001002020000000,
1001002020000000,
1001002040000000, //6

2002002010101000,
2002002010101000,
2002002010101000,
2220222010101000,

2002002010101000,
2002002010101000,
2002002010101000,
2044, //14

1002001010000010,
1002001010000010,
11011221,
10001130,

1002001010000010,
1002001010000011,
1010101110101020,
10001130, //22

2002001020020010,
2002001010000000,
12121122,
1001002020000010,

2002001020020010,
2002001010001010,
1,
3333,
700000000000000000000000000000000000000000000008,
00000001, //32

10101001,
2001001030000011,
1000100010111000,
1001001030000010,

10101001,
2001001030000011,
1000100010111000,
1001001030000020, //40

20202001,
2001001030000011,
1000102010111020,
1001001030000020,

20202001,
2001001030000011,
1000102010111020,
1001001030000000,

0,
0, //50
#END


COURSE:Normal
LEVEL:4
BALLOON:15
SCOREINIT:1110
SCOREDIFF:330
SCOREMODE:1

#START
22,
22,

1110,
1001000010000000,
1110,
1001000030000000, //6

20001110,
20001110,
20001110,
2033,

20001110,
20001110,
20001110,
20004001, //14

1001000010000010,
1001000010000010,
11,
30001110,

1001000020000010,
1001000020000010,
12,
30001110, //22

1002000010020000,
1002000010000000,
1110,
1001000010000010,

1002000010020000,
1002000010001010,
1,
3333,
700000000000000000000000000000000000000000000008,
00000001, //32

10101001,
1001000010000010,
10101001,
1001000010000010,

10101001,
1001000010000010,
10101001,
1001000030000020, //40

20202001,
1001000010000010,
1212,
10203002,

20202001,
1001000010000010,
1212,
1230,

0,
0, //50
#END


COURSE:Easy
LEVEL:3
BALLOON:
SCOREINIT:1250
SCOREDIFF:500
SCOREMODE:1

#START
1,
1,

1,
1,
11,
13, //6

00001110,
00001110,
00001110,
0033,

00001110,
00001110,
00001110,
03, //14

1100,
1100,
11,
3,

1100,
1100,
12,
3, //22

1210,
1210,
11,
11,

1210,
1210,
1,
1111,
5,
000000000000000000000008000000000000000000000000, //32

1110,
1110,
1110,
13,

1110,
1110,
1110,
13, //40

2220,
1110,
2220,
13,

2220,
1110,
2220,
13,

0,
0, //50
#END
