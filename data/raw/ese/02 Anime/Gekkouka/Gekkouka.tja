// TJADB Project
TITLE:Gekkouka
TITLEJA:月光花
SUBTITLE:--<PERSON><PERSON>/Black Jack
SUBTITLEJA:ブラック・ジャック
BPM:102
WAVE:Gekkouka.ogg
OFFSET:-2.924
DEMOSTART:58.806

COURSE:Oni
LEVEL:5
SCOREINIT:1000
SCOREDIFF:220

#START
11212000,
11212000,
11212000,
11212000,
11212210,
11212000,
11212210,
11212012,
1110202010201020,
1110202010201020,
1111200011112000,
1110202010201000,
1111200011112000,
1110202010201000,
1111200011112000,
2120211201121000,
#GOGOSTART
1020102012201020,
1020102011201020,
1220122010201120,
1020102020001111,
1020102011201020,
1020102011201020,
1220122010201020,
1020102010001111,
1020102011201020,
1020102011201020,
1120112010201020,
1020102010001112,
1020112010201220,
1020112010201220,
1120112010201220,
12121000,
#GOGOEND
1020102010201120,
1020102010201122,
000000500000000000000000000000000000000008000000,
,
,
,
#END

COURSE:Hard
LEVEL:2
SCOREINIT:1010
SCOREDIFF:290

#START
10012000,
10012000,
10012000,
10022000,
10212000,
10212000,
10212000,
10212000,
1110200010102000,
1110200010102000,
1110200010102000,
1110200010201000,
1110200010102000,
1110200010102000,
1110200010202000,
500000000000000000000000000000000008000000000000,
#GOGOSTART
30201120,
10201120,
10201120,
10121000,
30201120,
10201120,
10201220,
1000102010001110,
30201120,
10201120,
10201120,
1000200010001110,
30201120,
1000200011102000,
11201120,
11121000,
#GOGOEND
11201120,
10010010,
000000500000000000000000000000000000000008000000,
,
,
,
#END

COURSE:Normal
LEVEL:2
SCOREINIT:
SCOREDIFF:

#START
10011000,
10011000,
10011000,
10011000,
10012000,
10012000,
10012000,
10002011,
1011,
10000011,
1011,
10000011,
1012,
20000011,
1022,
500000000000000000000008000000000000000000000000,
#GOGOSTART
3030,
1210,
10111000,
10111000,
3030,
1210,
10111000,
20002011,
1010,
1210,
10111000,
10111000,
3212,
10222000,
1212,
10111000,
#GOGOEND
500000000000000000000000000008000000000000000000,
10010010,
000000500000000000000000000000000000000008000000,
,
,
,
#END

COURSE:Oni
LEVEL:1
SCOREINIT:
SCOREDIFF:

#START
10011000,
10011000,
10011000,
10011000,
10012000,
10012000,
10012000,
10012000,
10111010,
1,
10111010,
1,
10111020,
2,
10111020,
500000000000000000000008000000000000000000000000,
#GOGOSTART
3030,
1210,
10111000,
10001011,
1030,
1210,
10111000,
20222000,
3010,
1210,
10111000,
10111000,
3212,
10222000,
1212,
10111000,
#GOGOEND
500000000000000000000000000008000000000000000000,
10010010,
000000500000000000000000000000000000000008000000,
,
,
,
#END

COURSE:Oni
LEVEL:1
SCOREINIT:1180
SCOREDIFF:540

#START
10011000,
10011000,
10011000,
10011000,
10012000,
10012000,
10012000,
1021,
10022010,
1001,
10022010,
1001,
10011020,
2001,
10022020,
500000000000000000000000000008000000000000000000,
#GOGOSTART
3030,
1210,
10111000,
10111000,
3030,
1210,
10111000,
20222000,
3210,
1210,
10111000,
10111000,
3212,
10222000,
1212,
10111000,
#GOGOEND
50000800,
10010010,
000000500000000000000000000000000000000008000000,
,
,
,
#END

COURSE:Easy
LEVEL:3
SCOREINIT:
SCOREDIFF:

#START
1,
1,
1010,
1010,
1020,
1020,
1020,
1020,
1011,
1001,
1010,
1001,
1011,
1001,
1020,
2001,
#GOGOSTART
1202,
1210,
1010,
1210,
3030,
1210,
1220,
1011,
1202,
1010,
1210,
1210,
3030,
1212,
1210,
1220,
#GOGOEND
500000000000000000000000000008000000000000000000,
10010010,
000000500000000000000000000000000000000008000000,
,
,
,
#END

COURSE:Oni
LEVEL:1
SCOREINIT:
SCOREDIFF:

#START
1,
1,
1010,
1010,
1020,
1020,
1020,
1020,
1011,
1001,
1010,
1001,
1011,
1001,
1020,
2,
#GOGOSTART
3030,
1210,
1210,
1011,
1202,
1210,
1220,
1210,
3030,
1210,
1210,
1011,
1202,
1012,
1210,
1220,
#GOGOEND
500000000000000000000000000008000000000000000000,
10010010,
000000500000000000000000000000000000000008000000,
,
,
,
#END

COURSE:Oni
LEVEL:1
SCOREINIT:960
SCOREDIFF:620

#START
1,
1,
1010,
1010,
1020,
1020,
1020,
1020,
1011,
1001,
1010,
1001,
1011,
1001,
1020,
2,
#GOGOSTART
3030,
1210,
1210,
1210,
3030,
1210,
1210,
1220,
3030,
1210,
1212,
1210,
3110,
1212,
1210,
1220,
#GOGOEND
500000000000000000000000000008000000000000000000,
10010010,
000000500000000000000000000000000000000008000000,
,
,
,
#END
