//TJADB Project
TITLE:Shikairo Days
TITLEJA:シカ色デイズ
SUBTITLE:--<PERSON><PERSON><PERSON><PERSON><PERSON> / <PERSON>kanoko Nokonoko Koshitantan
SUBTITLEJA:TVアニメ「しかのこのこのここしたんたん」より
BPM:183
WAVE:Shikai<PERSON> Days.ogg
OFFSET:-0.707
DEMOSTART:0.648

COURSE:Oni
LEVEL:6
BALLOON:10,24,22,13
SCOREINIT:2350
SCOREDIFF:0

#START
12112121,
11202000,
12112121,
1010200020222020, 

12112121,
11202000,
12112121,
3030400040001011, //8

10102211,
0020101020201220,
10102212,
1020101020201011,

10102211,
0020101020201220,
10102212,
12112230, //16

1110202000102000,
1110202000102000,
1110202000102000,
11221122,

1110202000102000,
1110202000102000,
1110202000102000,
1010200020221000, //24

1110202010102020,
1110202010102020,
1110202010102020,
500000000000000000000000000000000008000000000000,

1110202011102020,
1110202011102020,
1110202011102020,
7008, //32

#MEASURE 3/4
100022202000,
100022202000,
100022202000,
500000000000000000000000000008000000,

#MEASURE 4/4
40210120,
07000000,
0008,
3330, //40

#GOGOSTART
1000200011102220,
1020002011102011,
2020002210200010,
2011102010201110,

20102112,
1020112000201070,
0,
08, //48

10201122,
1020002011102011,
2020002210200010,
2011102010201110,

20102112,
1020112022102050,
000000000000000000000008000000000000000000700000,
0008, //56
#GOGOEND

12121212,
02121212,
12121212,
12120440,

1120102010201020,
1120102010200010,
02103030,
3400, //64

12112121,
1010200020222020,
12112121,
33404000,
0, //69
#END


COURSE:Hard
LEVEL:5
BALLOON:8,20,18,9
SCOREINIT:3510
SCOREDIFF:0

#START
12111111,
11202000,
12111111,
11202220,

12111111,
11202000,
12111111,
33404000, //8

10102211,
02000000,
10102202,
12000000,

10102211,
02000000,
10102202,
12000000, //16

10220020,
10220020,
10220020,
11201120,

10220020,
10220020,
10220020,
11202030, //24

10221020,
10221020,
10221020,
500000000000000000000000000000000008000000000000,

10221120,
10221120,
10221120,
7008, //32

#MEASURE 3/4
122,
122,
122,
500000000000000000000000000008000000,

#MEASURE 4/4
40010020,
07000000,
0008,
3330, //40

#GOGOSTART
10201122,
12021121,
22012205,
000000000000000000000000000000000000000008000000,

00102022,
12120107,
0,
08, //48

10201122,
12021121,
22012205,
000000000000000000000000000000000000000008000000,

00102022,
12120105,
000000000000000000000008000000000000000000700000,
0008, //56
#GOGOEND

12121012,
02121202,
12121010,
11110440,

12121012,
12101001,
01103030,
3400, //64

12111111,
11202000,
12111111,
33404000,
0, //69
#END


COURSE:Normal
LEVEL:4
BALLOON:6,14,12,7
SCOREINIT:5690
SCOREDIFF:0

#START
1212,
1220,
1212,
1220,

1212,
1220,
3434,
3440, //8

10100001,
0,
10100001,
0,

10100001,
0,
10100001,
0, //16

1202,
1202,
1202,
1212,

1202,
1202,
1202,
1223, //24

10220020,
10220020,
10220020,
500000000000000000000000000000000008000000000000,

10220220,
10220220,
10220220,
7008, //32

#MEASURE 3/4
102,
102,
102,
500000000000000000000000000008000000,

#MEASURE 4/4
10010020,
09000000,
000090000800,
0, //40

#GOGOSTART
1122,
11001002,
20022005,
000000000000000000000000000000000000000008000000,

0122,
10110107,
0,
08, //48

1122,
11001002,
20022005,
000000000000000000000000000000000000000008000000,

0122,
10110105,
000000000000000000000008000000000000000000700000,
0008, //56
#GOGOEND

10101011,
01101000,
10111010,
500000000000000000000000000000000008000000000000,

1111,
11101005,
000000000000000000000000000000000000000000000008,
0300, //64

1212,
1220,
3434,
3440,
0, //69
#END


COURSE:Easy
LEVEL:3
BALLOON:4,11,10
SCOREINIT:9030
SCOREDIFF:0

#START
11,
12,
11,
12,

11,
12,
33,
34, //8

10000001,
0,
10000001,
0,

10000001,
0,
10000001,
0, //16

1011,
1011,
1011,
11,

1011,
1011,
1011,
12, //24

3011,
1011,
1011,
500000000000000000000000000008000000000000000000,

1011,
1011,
1011,
7008, //32

#MEASURE 3/4
2,
2,
2,
500000000000000000000000000008000000,

#MEASURE 4/4
0,
09000000,
000090000800,
0, //40

#GOGOSTART
1110,
11,
20002005,
000000000000000000000000000000000000000008000000,

0110,
10000007,
0,
08, //48

1110,
11,
20002005,
000000000000000000000000000000000000000008000000,

0110,
10000005,
000000000000000000000008000000000000000000300000,
03, //56
#GOGOEND

10001001,
0110,
11,
500000000000000000000000000008000000000000000000,

11,
10101005,
000000000000000000000000000000000000000008000000,
0300, //64

11,
12,
33,
34,
0, //69
#END