//TJADB Project
TITLE:Ao no Sumika
TITLEJA:青のすみか
SUBTITLE:--TV Anime "<PERSON><PERSON><PERSON> Kaisen Kaigyoku・Gyokusetsu" Opening Theme
SUBTITLEJA:TVアニメ『呪術廻戦 懐玉・玉折』オープニングテーマ
BPM:152
WAVE:Ao no Sumika.ogg
OFFSET:-1.378
DEMOSTART:48.507

//shinuchi: 10090/6960/3330/2080

COURSE:Oni
LEVEL:8
BALLOON:
SCOREINIT:660,2180
SCOREDIFF:183

#START
20020000,
3003003030303030,
2022002010202020,
1022002210202020,
3003003030111120, 
#MEASURE 2/4
0, //6

#MEASURE 4/4
1000002020000022,
20021221,
10221202,
20000110,

1000002020000022,
2000002210202010,
1020200220022002,
22221210, //14

4000102010002021,
1000201210201021,
1000201210201021,
1000201212102010,

1000201212102010,
1000201212102010,
1022200020101022,
10212212, //22

10212121,
1000201020102011,
1010202220102010,
1010201021102000,

3020102210201022,
1020102011111020,
30111222,
100010001000400000000000000200200100000000100000,
1101100000000000, //31

#GOGOSTART
1020102112101020,
1000201012112011,
1020102112101020,
1000201012112011,

1021102112101020,
1000201012102011,
1021102112101010,
0010202012102011, //39

1020102112101020,
1000201012112011,
1020102112101020,
0010102110100030,

1021102112101020,
0010201012101020,
1022222011111030,
0030
#GOGOEND
3022202010
#GOGOSTART
20, //47

100000101010100000000100100000200200100100200000,
1010201110221120,
1001100110221120,
1010201110111220,

0010200110221120,
1010201110221120,
0010200111221120,
0030303011122030, //55
#GOGOEND

0,
0,
0, //58
#END


COURSE:Hard
LEVEL:5
BALLOON:10
SCOREINIT:760,3640
SCOREDIFF:238

#START
2,
3003003030303030,
0,
22,
3003003030030030,
#MEASURE 2/4
0, //6

#MEASURE 4/4
10022002,
20022020,
1222,
2,

10022002,
20022020,
2222,
22020200, //14

40121021,
10121021,
10121201,
10201212,

10201212,
10211021,
1202,
10211020, //22

10202120,
20202120,
10212120,
10212110,

30121210,
10121210,
30111900,
000000000000090000000000800000000000000000000000,
0, //31

#GOGOSTART
1000201110101000,
1000201010110000,
1000201110101000,
10201121,

1001100110001010,
10201121,
1001100110001010,
01201120, //39

1000201110101000,
10201120,
1000201110101010,
0010100110100030,

1001100110001010,
01211211,
600000000000000000000000000000000000000000000008,
00
#GOGOEND
01121
#GOGOSTART
0, //47

1001100010102010,
10201121,
1001100010102010,
1000200010111030,

01101121,
10211121,
0010100110001010,
03333013, //55
#GOGOEND

0,
0,
0, //58
#END


COURSE:Normal
LEVEL:4
BALLOON:7,7,7
SCOREINIT:1180,7480
SCOREDIFF:493

#START
2,
600000000000000000000000000000000000000008000000,
0,
2,
600000000000000000000000000000000000000000000008,
#MEASURE 2/4
0, //6

#MEASURE 4/4
12,
2,
12,
2,

12,
2,
2022,
2, //14

41,
1011,
11,
1012,

1012,
1011,
1,
12, //22

1022,
2022,
1022,
1220,

3111,
1211,
30000900,
000000000000090000000000800000000000000000000000,
0, //31

#GOGOSTART
10201011,
1210,
10201011,
1012,

10101011,
0012,
10101011,
01001000, //39

10201011,
1210,
1217,
00000803,

00101011,
01001010,
600000000000000000000000000000000000000000000008,
00
#GOGOEND
00202
#GOGOSTART
0, //47

1110,
10101110,
1110,
10101103,

0112,
10101101,
0117,
00000803, //55
#GOGOEND

0,
0,
0, //58
#END


COURSE:Easy
LEVEL:3
BALLOON:6,5,5
SCOREINIT:1060,11060
SCOREDIFF:685

#START
0,
600000000000000000000000000000000000000008000000,
0,
0,
600000000000000000000000000000000000000000000008,
#MEASURE 2/4
0, //6

#MEASURE 4/4
1,
2,
1,
2,

1,
2,
22,
2, //14

41,
1011,
11,
12,

12,
1011,
1,
1, //22

12,
22,
12,
12,

3011,
1011,
30000900,
000000000000090000000000800000000000000000000000,
0, //31

#GOGOSTART
1011,
11,
1011,
12,

10101001,
02,
10101001,
01, //39

1011,
11,
1117,
000000000000000000000000080000000000000000300000,

00001001,
0011,
600000000000000000000000000000000000000000000008,
00
#GOGOEND
00000
#GOGOSTART
0, //47

1110,
11,
1110,
10000003,

01,
10101001,
0017,
000000000000000000000000080000000000000000300000, //55
#GOGOEND

0,
0,
0, //58
#END