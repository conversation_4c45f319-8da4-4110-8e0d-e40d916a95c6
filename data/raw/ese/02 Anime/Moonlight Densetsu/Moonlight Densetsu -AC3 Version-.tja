//TJADB Project
TITLE:Moonlight Densetsu -AC3 Version-
TITLEJA:ムーンライト伝説
SUBTITLE:--D<PERSON>I/<PERSON> Moon
SUBTITLEJA:AC3音源・譜面 美少女戦士セーラームーン
BPM:138
WAVE:Moonlight Densetsu -AC3 Version-.ogg
OFFSET:-4.672
DEMOSTART:80.523


COURSE:Oni
LEVEL:5
BALLOON:
SCOREINIT:
SCOREDIFF:
SCOREMODE:0

#START
10110110,
10100220,
10110110,
10100220,

10221022,
1100,
10101103,
00000111, //8

10220220,
10100111,
10220220,
10100111,

10220220,
10100111,
10101103,
00000111, //16

10110110,
1000102220101010,
10110110,
1000102220101010,

10110110,
1000102220101010,
10101103,
0002, //24

1011101110101020,
1011101110101020,
1011101110101020,
1002,

1011101110101020,
0000001010010010,
500000000000000000000000000000000000000000000008,
00000111, //32

10110110,
1000102220101010,
10110110,
1000102220101010,

10110110,
1000102220101010,
10101103,
0, //40

0,
0,
0,
00000111, //44

10110110,
1000102220101010,
10110110,
1000102220101010,

10110110,
1000102220101010,
10101103,
0002, //52

1011101110101020,
1011101110101020,
1011101110101020,
1002,

1011101110101020,
0000001010010010,
500000000000000000000000000000000000000000000008,
00000111, //60

10110110,
1000102220101010,
10110110,
1000102220101010,

10110110,
1000102220101010,
10101103,
0,
0, //69
#END


COURSE:Hard
LEVEL:2
BALLOON:
SCOREINIT:570
SCOREDIFF:130
SCOREMODE:1

#START
10110110,
10100220,
10110110,
10100220,

10221022,
1100,
10101103,
00000111, //8

10220220,
10100111,
10220220,
10100111,

10220220,
10100111,
10101103,
00000111, //16

10110110,
1000102220101010,
10110110,
1000102220101010,

10110110,
1000102220101010,
10101103,
0002, //24

1011101110101020,
1011101110101020,
1011101110101020,
1002,

1011101110101020,
0000001010010010,
500000000000000000000000000000000000000000000008,
00000111, //32

10110110,
1000102220101010,
10110110,
1000102220101010,

10110110,
1000102220101010,
10101103,
0, //40

0,
0,
0,
00000111, //44

10110110,
1000102220101010,
10110110,
1000102220101010,

10110110,
1000102220101010,
10101103,
0002, //52

1011101110101020,
1011101110101020,
1011101110101020,
1002,

1011101110101020,
0000001010010010,
500000000000000000000000000000000000000000000008,
00000111, //60

10110110,
1000102220101010,
10110110,
1000102220101010,

10110110,
1000102220101010,
10101103,
0,
0, //69
#END


COURSE:Normal
LEVEL:2
BALLOON:
SCOREINIT:920
SCOREDIFF:260
SCOREMODE:1

#START
1,
1,
1,
1,

11,
1100,
10101003,
00000110, //8

1100,
10100110,
1100,
10100110,

1100,
10100110,
10100003,
00000110, //16

1100,
20200110,
1100,
20200110,

1100,
20200110,
10100003,
0002, //24

1102,
1102,
10100003,
0002,

10100022,
0000001010010010,
500000000000000000000000000000000000000000000008,
00000110, //32

10220220,
10100110,
10220220,
10100110,

10220220,
10100110,
10100003,
0, //40

0,
0,
0,
00000110, //44

1100,
20200110,
1100,
20200110,

1100,
20200110,
10100003,
0002, //52

1102,
1102,
10100003,
0002,

10100022,
0000001010010010,
500000000000000000000000000000000000000000000008,
00000110, //60

10220220,
10100110,
10220220,
10100110,

10220220,
10100110,
10100003,
0,
0, //69
#END


COURSE:Easy
LEVEL:3
BALLOON:
SCOREINIT:930
SCOREDIFF:300
SCOREMODE:1

#START
1,
1,
1,
1,

11,
1,
10001003,
00000111, //8

1,
10000111,
1,
10000111,

1,
10000111,
10000003,
00000111, //16

1,
20000111,
1,
20000111,

1,
20000111,
10000003,
0, //24

1100,
1100,
10100003,
0,

10100022,
00011000,
500000000000000000000000000000000000000000000008,
00000111, //32

1,
20000111,
1,
20000111,

1,
20000111,
10001003,
0, //40

0,
0,
0,
00000111, //44

1,
20200111,
1,
20200111,

1,
20200111,
10000003,
0, //52

1100,
1100,
10100003,
0,

10100022,
00011000,
500000000000000000000000000000000000000000000008,
00000111, //60

1,
20200111,
1,
20200111,

1,
20200111,
10001003,
0,
0, //69
#END